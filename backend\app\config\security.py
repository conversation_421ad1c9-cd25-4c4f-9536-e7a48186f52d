"""
Security Configuration for AI-Powered Dashboard

Comprehensive security settings for production deployment including:
- API security configurations
- Rate limiting settings
- Input validation rules
- CORS policies
- Environment protection
- Security headers
"""

from typing import List, Dict, Any, Optional
from pydantic import Field, validator
from enum import Enum


class SecurityLevel(str, Enum):
    """Security levels for different environments"""
    DEVELOPMENT = "development"
    STAGING = "staging" 
    PRODUCTION = "production"


class SecurityConfig:
    """Security configuration constants"""
    
    # Rate Limiting Configuration
    RATE_LIMITS = {
        # Per IP limits (requests per minute)
        "api_general": 60,
        "api_intensive": 20,  # For AI processing endpoints
        "websocket_connections": 5,
        "file_uploads": 10,
        "auth_attempts": 5,
        
        # Per endpoint specific limits
        "process_input": 30,
        "search_queries": 40,
        "task_operations": 60,
        "event_operations": 60,
        
        # Burst limits (max requests in 10 seconds)
        "burst_general": 20,
        "burst_ai": 5,
    }
    
    # Input Validation Rules
    INPUT_VALIDATION = {
        "max_text_length": 5000,
        "max_title_length": 200,
        "max_description_length": 2000,
        "max_file_size_mb": 10,
        "allowed_file_types": [".txt", ".md", ".pdf", ".docx"],
        "max_batch_size": 100,
        "min_password_length": 8,
        "max_query_params": 50,
        "max_url_length": 2048,
    }
    
    # Security Headers
    SECURITY_HEADERS = {
        "X-Frame-Options": "DENY",
        "X-Content-Type-Options": "nosniff",
        "X-XSS-Protection": "1; mode=block",
        "Referrer-Policy": "strict-origin-when-cross-origin",
        "Permissions-Policy": "camera=(), microphone=(), geolocation=()",
        "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
    }
    
    # Content Security Policy
    CSP_DIRECTIVES = {
        "default-src": "'self'",
        "script-src": "'self' 'unsafe-inline'",
        "style-src": "'self' 'unsafe-inline' https://fonts.googleapis.com",
        "font-src": "'self' https://fonts.gstatic.com",
        "img-src": "'self' data: https:",
        "connect-src": "'self' ws: wss:",
        "object-src": "'none'",
        "base-uri": "'self'",
        "frame-ancestors": "'none'",
    }
    
    # CORS Configuration
    CORS_CONFIG = {
        "development": {
            "allowed_origins": ["http://localhost:3000", "http://127.0.0.1:3000"],
            "allow_credentials": True,
            "allowed_methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
            "allowed_headers": ["*"],
        },
        "production": {
            "allowed_origins": [],  # Set via environment variable
            "allow_credentials": True,
            "allowed_methods": ["GET", "POST", "PUT", "DELETE"],
            "allowed_headers": [
                "Content-Type", "Authorization", "X-Request-ID", 
                "X-API-Version", "Accept"
            ],
        }
    }
    
    # SQL Injection Prevention Patterns
    SQL_INJECTION_PATTERNS = [
        r"(\b(ALTER|CREATE|DELETE|DROP|EXEC(UTE)?|INSERT|SELECT|UNION|UPDATE)\b)",
        r"(\b(OR|AND)\s+\d+\s*=\s*\d+)",
        r"(\b(OR|AND)\s+['\"]?\w+['\"]?\s*=\s*['\"]?\w+['\"]?)",
        r"(--|#|/\*|\*/)",
        r"(\bUNION\s+(ALL\s+)?SELECT)",
        r"(\bINSERT\s+INTO)",
        r"(\bUPDATE\s+\w+\s+SET)",
        r"(\bDELETE\s+FROM)",
        r"(\bDROP\s+(TABLE|DATABASE))",
        r"(\bALTER\s+TABLE)",
        r"(\bCREATE\s+(TABLE|DATABASE))",
        r"(\bEXEC\s*\()",
    ]
    
    # XSS Prevention Patterns
    XSS_PATTERNS = [
        r"<script[^>]*>.*?</script>",
        r"javascript:",
        r"vbscript:",
        r"onload\s*=",
        r"onerror\s*=",
        r"onclick\s*=",
        r"onmouseover\s*=",
        r"<iframe[^>]*>",
        r"<object[^>]*>",
        r"<embed[^>]*>",
        r"<meta[^>]*http-equiv",
    ]
    
    # Sensitive Data Patterns (to mask in logs)
    SENSITIVE_PATTERNS = [
        r"(?i)(password|pwd|secret|token|key)\s*[:=]\s*['\"]?([^'\"\s]+)",
        r"(?i)(authorization|auth)\s*:\s*bearer\s+([a-zA-Z0-9\-_\.]+)",
        r"(?i)(api[_-]?key)\s*[:=]\s*['\"]?([^'\"\s]+)",
        r"\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b",  # Email
        r"\b\d{3}-\d{2}-\d{4}\b",  # SSN
        r"\b\d{4}[- ]?\d{4}[- ]?\d{4}[- ]?\d{4}\b",  # Credit card
    ]
    
    # Environment Variable Protection
    PROTECTED_ENV_VARS = [
        "OPENROUTER_API_KEY",
        "LANGSEARCH_API_KEY", 
        "DATABASE_URL",
        "SECRET_KEY",
        "ENCRYPTION_KEY",
        "JWT_SECRET",
        "SMTP_PASSWORD",
        "REDIS_PASSWORD",
    ]


# Security utility functions
def get_csp_header_value(environment: str = "production") -> str:
    """Generate Content Security Policy header value"""
    directives = []
    for directive, value in SecurityConfig.CSP_DIRECTIVES.items():
        directives.append(f"{directive} {value}")
    
    return "; ".join(directives)


def get_cors_config(environment: str = "production") -> Dict[str, Any]:
    """Get CORS configuration for environment"""
    return SecurityConfig.CORS_CONFIG.get(environment, SecurityConfig.CORS_CONFIG["production"])


def is_rate_limit_exceeded(endpoint: str, current_count: int) -> bool:
    """Check if rate limit is exceeded for endpoint"""
    limit = SecurityConfig.RATE_LIMITS.get(endpoint, SecurityConfig.RATE_LIMITS["api_general"])
    return current_count >= limit


def get_max_input_length(input_type: str = "text") -> int:
    """Get maximum allowed input length for type"""
    key = f"max_{input_type}_length"
    return SecurityConfig.INPUT_VALIDATION.get(key, SecurityConfig.INPUT_VALIDATION["max_text_length"])


def mask_sensitive_data(text: str) -> str:
    """Mask sensitive data in text for logging"""
    import re
    
    masked_text = text
    
    # Patterns with capture groups (key-value pairs)
    key_value_patterns = [
        r"(?i)(password|pwd|secret|token|key)\s*[:=]\s*['\"]?([^'\"\s]+)",
        r"(?i)(authorization|auth)\s*:\s*bearer\s+([a-zA-Z0-9\-_\.]+)",
        r"(?i)(api[_-]?key)\s*[:=]\s*['\"]?([^'\"\s]+)",
    ]
    
    # Patterns for complete replacement
    complete_patterns = [
        (r"\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b", "[EMAIL_REDACTED]"),  # Email
        (r"\b\d{3}-\d{2}-\d{4}\b", "[SSN_REDACTED]"),  # SSN
        (r"\b\d{4}[- ]?\d{4}[- ]?\d{4}[- ]?\d{4}\b", "[CARD_REDACTED]"),  # Credit card
    ]
    
    # Handle key-value patterns
    for pattern in key_value_patterns:
        masked_text = re.sub(pattern, r"\1: [REDACTED]", masked_text, flags=re.IGNORECASE)
    
    # Handle complete replacement patterns
    for pattern, replacement in complete_patterns:
        masked_text = re.sub(pattern, replacement, masked_text, flags=re.IGNORECASE)
    
    return masked_text


# Export security constants
__all__ = [
    "SecurityConfig",
    "SecurityLevel", 
    "get_csp_header_value",
    "get_cors_config",
    "is_rate_limit_exceeded",
    "get_max_input_length",
    "mask_sensitive_data",
]
