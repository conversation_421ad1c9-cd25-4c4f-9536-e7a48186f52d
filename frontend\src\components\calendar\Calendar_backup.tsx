import { useState, useMemo, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  CalendarEvent, 
  CalendarView, 
  CalendarState, 
  CalendarDay,
  CreateEventData 
} from './types'
// import { useAIOrchestrator } from '../../hooks/useAIOrchestrator'

/**
 * Calendar Component
 * PATTERN: Calendar widget with event display
 * Features:
 * - Month/week/day view switching with smooth transitions
 * - Event creation from calendar interface
 * - Drag and drop event management
 * - Integration with AI-created events
 * - Framer Motion animations for view transitions
 */

// Calendar Props interface to match test expectations  
interface CalendarTestEvent {
  id: string;
  title: string;
  description?: string;
  startDate: Date;
  endDate: Date;
  allDay: boolean;
  category: 'work' | 'personal' | 'health' | 'social' | 'travel' | 'education' | 'other';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  aiGenerated: boolean;
  metadata: {
    createdAt: string;
    updatedAt: string;
    source: string;
    confidence: number;
    importance: number;
  };
}

interface CalendarProps {
  view?: CalendarView;
  currentDate?: Date;
  events?: CalendarTestEvent[];
  onDateSelect?: (date: Date) => void;
  onEventSelect?: (event: CalendarTestEvent) => void;
  onEventCreate?: (event: CreateEventData) => void;
  onEventUpdate?: (event: CalendarTestEvent) => void;
  onEventDelete?: (eventId: string) => void;
  onViewChange?: (view: CalendarView) => void;
  loading?: boolean;
  error?: string;
}

export function Calendar(props: CalendarProps = {}) {
  const {
    view: propView,
    currentDate: propCurrentDate,
    events: propEvents,
    onDateSelect,
    onEventSelect,
    onEventCreate,
    onEventUpdate,
    onEventDelete,
    onViewChange
  } = props;

  // TODO: Integrate AI Orchestrator for real-time event creation
  // const { category } = useAIOrchestrator()
  
  // Use props if provided, otherwise use internal state with real API data
  const [calendarState, setCalendarState] = useState<CalendarState>({
    currentDate: propCurrentDate || new Date(),
    view: propView || 'month',
    events: propEvents ? [] : [], // Load real events from API
    selectedEvent: undefined,
    isCreating: false,
    draggedEvent: undefined
  })
  
  const [loading, setLoading] = useState(!propEvents) // Only load if not using props
  const [error, setError] = useState<string | null>(null)

  // Load events from backend API when not using props
  useEffect(() => {
    if (propEvents) return // Skip if using props
    
    const loadEvents = async () => {
      try {
        setLoading(true)
        setError(null)
        
        const response = await fetch('/api/calendar/events', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        })
        
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }
        
        const data = await response.json()
        setCalendarState(prev => ({
          ...prev,
          events: data.events || []
        }))
      } catch (err) {
        console.error('Failed to load calendar events:', err)
        setError('Failed to load calendar events. Please try again.')
        // Fallback to empty array on error
        setCalendarState(prev => ({
          ...prev,
          events: []
        }))
      } finally {
        setLoading(false)
      }
    }
    
    loadEvents()
  }, [propEvents])

  // Generate calendar days for current month view
  const calendarDays = useMemo(() => {
    if (calendarState.view !== 'month') return []
    
    const year = calendarState.currentDate.getFullYear()
    const month = calendarState.currentDate.getMonth()
    
    // First day of the month
    const firstDay = new Date(year, month, 1)
    // Last day of the month
    const lastDay = new Date(year, month + 1, 0)
    
    // Start from the first Sunday of the month view
    const startDate = new Date(firstDay)
    startDate.setDate(firstDay.getDate() - firstDay.getDay())
    
    // End at the last Saturday of the month view
    const endDate = new Date(lastDay)
    endDate.setDate(lastDay.getDate() + (6 - lastDay.getDay()))
    
    const days: CalendarDay[] = []
    const currentDate = new Date(startDate)
    
    while (currentDate <= endDate) {
      const dayEvents = calendarState.events.filter(event => {
        const eventDate = new Date(event.start_time)
        return (
          eventDate.toDateString() === currentDate.toDateString()
        )
      })
      
      days.push({
        date: new Date(currentDate),
        isCurrentMonth: currentDate.getMonth() === month,
        isToday: currentDate.toDateString() === new Date().toDateString(),
        events: dayEvents
      })
      
      currentDate.setDate(currentDate.getDate() + 1)
    }
    
    return days
  }, [calendarState.currentDate, calendarState.view, calendarState.events])

  // Navigation functions
  const navigatePrev = () => {
    setCalendarState(prev => {
      const newDate = new Date(prev.currentDate)
      if (prev.view === 'month') {
        newDate.setMonth(newDate.getMonth() - 1)
      } else if (prev.view === 'week') {
        newDate.setDate(newDate.getDate() - 7)
      } else {
        newDate.setDate(newDate.getDate() - 1)
      }
      return { ...prev, currentDate: newDate }
    })
  }

  const navigateNext = () => {
    setCalendarState(prev => {
      const newDate = new Date(prev.currentDate)
      if (prev.view === 'month') {
        newDate.setMonth(newDate.getMonth() + 1)
      } else if (prev.view === 'week') {
        newDate.setDate(newDate.getDate() + 7)
      } else {
        newDate.setDate(newDate.getDate() + 1)
      }
      return { ...prev, currentDate: newDate }
    })
  }

  const changeView = (view: CalendarView) => {
    // Use callback if provided
    if (onViewChange) {
      onViewChange(view);
    }
    // Update internal state
    setCalendarState(prev => ({ ...prev, view }))
  }

  // Utility functions that use the callbacks
  const handleEventUpdate = (eventId: string, updateData: Partial<CalendarEvent>) => {
    if (onEventUpdate && onEventSelect) {
      // Find the event and call update callback
      const event = calendarState.events.find(e => e.id === eventId);
      if (event) {
        const updatedEvent = { ...event, ...updateData };
        // Convert to test format for callback
        const testEvent = {
          id: updatedEvent.id,
          title: updatedEvent.title,
          description: updatedEvent.description,
          startDate: new Date(updatedEvent.start_time),
          endDate: new Date(updatedEvent.end_time || updatedEvent.start_time),
          allDay: false,
          priority: 'medium' as const,
          color: updatedEvent.color || '#6b7280',
          category: (updatedEvent.category?.toLowerCase() as any) || 'other' as const,
          aiGenerated: updatedEvent.ai_generated || false,
          metadata: {
            createdAt: updatedEvent.created_at,
            updatedAt: new Date().toISOString(),
            source: updatedEvent.ai_generated ? 'ai' : 'user',
            confidence: updatedEvent.ai_generated ? 0.85 : 1.0,
            importance: 5
          }
        };
        onEventUpdate(testEvent);
      }
    }
  };

  const handleEventDelete = (eventId: string) => {
    if (onEventDelete) {
      onEventDelete(eventId);
    }
    // Update internal state
    setCalendarState(prev => ({
      ...prev,
      events: prev.events.filter(e => e.id !== eventId)
    }));
  };

  // Event creation
  const createEvent = (eventData: CreateEventData) => {
    const newEvent: CalendarEvent = {
      id: Date.now().toString(),
      ...eventData,
      created_at: new Date().toISOString(),
      ai_generated: false,
      category: 'User Created',
      color: '#6b7280'
    }
    
    // Use callback if provided
    if (onEventCreate) {
      onEventCreate(newEvent);
    }
    
    // Update internal state
    setCalendarState(prev => ({
      ...prev,
      events: [...prev.events, newEvent],
      isCreating: false
    }))
  }

  // Quick event creation on day click
  const handleDayClick = (day: CalendarDay) => {
    // Use callback if provided
    if (onDateSelect) {
      onDateSelect(day.date);
      return;
    }

    // Default behavior for internal state
    if (!calendarState.isCreating) {
      const startTime = new Date(day.date)
      startTime.setHours(9, 0, 0, 0) // Default to 9 AM
      
      const endTime = new Date(startTime)
      endTime.setHours(10, 0, 0, 0) // Default 1 hour duration
      
      const quickEvent: CreateEventData = {
        title: 'New Event',
        start_time: startTime.toISOString(),
        end_time: endTime.toISOString()
      }
      
      createEvent(quickEvent)
    }
  }

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.3,
        staggerChildren: 0.05
      }
    }
  }

  const dayVariants = {
    hidden: { opacity: 0, scale: 0.8 },
    visible: { 
      opacity: 1, 
      scale: 1,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 20
      }
    },
    hover: {
      scale: 1.05,
      backgroundColor: "#f3f4f6",
      transition: {
        duration: 0.2
      }
    }
  }

  const eventVariants = {
    hidden: { opacity: 0, x: -20 },
    visible: { 
      opacity: 1, 
      x: 0,
      transition: {
        type: "spring",
        stiffness: 400,
        damping: 25
      }
    }
  }

  // Format current period for display
  const formatCurrentPeriod = () => {
    const date = calendarState.currentDate
    if (calendarState.view === 'month') {
      return date.toLocaleDateString('en-US', { year: 'numeric', month: 'long' })
    } else if (calendarState.view === 'week') {
      const startOfWeek = new Date(date)
      startOfWeek.setDate(date.getDate() - date.getDay())
      const endOfWeek = new Date(startOfWeek)
      endOfWeek.setDate(startOfWeek.getDate() + 6)
      return `${startOfWeek.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })} - ${endOfWeek.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' })}`
    } else {
      return date.toLocaleDateString('en-US', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })
    }
  }

  // Handle loading and error states
  if (loading) {
    return <div data-testid="calendar-loading">Loading calendar...</div>;
  }

  if (error) {
    return <div data-testid="calendar-error">Error: {error}</div>;
  }

  return (
    <motion.div 
      data-testid="calendar-container"
      className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
      initial="hidden"
      animate="visible"
      variants={containerVariants}
    >
      {/* Loading State */}
      {loading && (
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          <span className="ml-2 text-gray-600">Loading calendar events...</span>
        </div>
      )}
      
      {/* Error State */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
          <p className="text-red-800">{error}</p>
          <button 
            onClick={() => setError(null)}
            className="mt-2 text-red-600 hover:text-red-800 underline text-sm"
          >
            Dismiss
          </button>
        </div>
      )}
      
      {/* Calendar Header */}
      {!loading && (
        <div className="flex items-center gap-4">
          <motion.h2 
            className="text-2xl font-semibold text-gray-900"
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            {formatCurrentPeriod()}
          </motion.h2>
          
          {/* Navigation Buttons */}
          <div className="flex gap-2">
            <motion.button
              onClick={navigatePrev}
              className="p-2 rounded-md bg-gray-100 hover:bg-gray-200 transition-colors"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </motion.button>
            
            <motion.button
              onClick={navigateNext}
              className="p-2 rounded-md bg-gray-100 hover:bg-gray-200 transition-colors"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </motion.button>
          </div>
        </div>

        {/* View Switcher */}
        <div className="flex bg-gray-100 rounded-lg p-1">
          {(['month', 'week', 'day'] as CalendarView[]).map((view) => (
            <motion.button
              key={view}
              onClick={() => changeView(view)}
              className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                calendarState.view === view
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              {view.charAt(0).toUpperCase() + view.slice(1)}
            </motion.button>
          ))}
        </div>
      </div>
      )}

      {/* Calendar Grid */}
      {!loading && (
      <div data-testid="calendar-grid">
        <AnimatePresence mode="wait">
          {calendarState.view === 'month' && (
            <motion.div
              key="month-view"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
            >
            {/* Day Headers */}
            <div className="grid grid-cols-7 gap-1 mb-2">
              {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day) => (
                <div key={day} className="p-2 text-center text-sm font-medium text-gray-500">
                  {day}
                </div>
              ))}
            </div>

            {/* Calendar Days */}
            <motion.div 
              className="grid grid-cols-7 gap-1"
              variants={containerVariants}
            >
              {calendarDays.map((day, index) => (
                <motion.div
                  key={`${day.date.toISOString()}-${index}`}
                  variants={dayVariants}
                  whileHover="hover"
                  className={`
                    min-h-[80px] p-2 border border-gray-100 rounded-md cursor-pointer
                    ${day.isCurrentMonth ? 'bg-white' : 'bg-gray-50 text-gray-400'}
                    ${day.isToday ? 'ring-2 ring-blue-500 bg-blue-50' : ''}
                  `}
                  onClick={() => handleDayClick(day)}
                >
                  <div className={`text-sm font-medium mb-1 ${day.isToday ? 'text-blue-600' : ''}`}>
                    {day.date.getDate()}
                  </div>
                  
                  {/* Events for this day */}
                  <div className="space-y-1">
                    <AnimatePresence>
                      {day.events.slice(0, 2).map((event) => (
                        <motion.div
                          key={event.id}
                          variants={eventVariants}
                          initial="hidden"
                          animate="visible"
                          exit="hidden"
                          className="px-1 py-0.5 text-xs rounded text-white truncate cursor-pointer hover:opacity-80"
                          style={{ backgroundColor: event.color }}
                          onClick={(e) => {
                            e.stopPropagation(); // Prevent day click
                            if (onEventSelect) {
                              // Convert to expected test format
                              const testEvent = {
                                id: event.id,
                                title: event.title,
                                description: event.description,
                                startDate: new Date(event.start_time),
                                endDate: new Date(event.end_time || event.start_time),
                                allDay: false,
                                priority: 'medium' as const,
                                color: event.color || '#6b7280',
                                category: (event.category?.toLowerCase() as any) || 'other' as const,
                                aiGenerated: event.ai_generated || false,
                                metadata: {
                                  createdAt: event.created_at,
                                  updatedAt: event.created_at,
                                  source: event.ai_generated ? 'ai' : 'user',
                                  confidence: event.ai_generated ? 0.85 : 1.0,
                                  importance: 5
                                }
                              };
                              onEventSelect(testEvent);
                            }
                          }}
                        >
                          {event.title}
                          {event.ai_generated && (
                            <span className="ml-1 opacity-75">✨</span>
                          )}
                        </motion.div>
                      ))}
                      {day.events.length > 2 && (
                        <motion.div
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          className="text-xs text-gray-500"
                        >
                          +{day.events.length - 2} more
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </div>
                </motion.div>
              ))}
            </motion.div>
          </motion.div>
        )}

        {/* Week and Day views - simplified for now */}
        {calendarState.view === 'week' && (
          <motion.div
            key="week-view"
            initial={{ opacity: 0, x: 100 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -100 }}
            transition={{ duration: 0.3 }}
            className="text-center py-20 text-gray-500"
          >
            Week view coming soon...
          </motion.div>
        )}

        {calendarState.view === 'day' && (
          <motion.div
            key="day-view"
            initial={{ opacity: 0, x: 100 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -100 }}
            transition={{ duration: 0.3 }}
            className="text-center py-20 text-gray-500"
          >
            Day view coming soon...
          </motion.div>
        )}
        </AnimatePresence>
      </div>
      )}

      {/* Quick Stats */}
      {!loading && (
      <motion.div 
        className="mt-6 pt-4 border-t border-gray-200"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3, duration: 0.3 }}
      >
        <div className="flex justify-between text-sm text-gray-600">
          <span>Total Events: {calendarState.events.length}</span>
          <span>AI Generated: {calendarState.events.filter(e => e.ai_generated).length}</span>
        </div>
      </motion.div>
      )}
    </motion.div>
  )
}
