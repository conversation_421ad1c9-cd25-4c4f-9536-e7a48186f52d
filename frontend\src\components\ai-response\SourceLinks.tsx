import { motion } from 'framer-motion'

interface SourceLinksProps {
  sources: string[]
  maxSources?: number
}

/**
 * SourceLinks Component
 * PATTERN: Source attribution with external links
 * Features:
 * - Domain extraction and favicon display
 * - Animated link list
 * - Link validation and safe opening
 * - Expandable source list
 */
export function SourceLinks({ sources, maxSources = 10 }: SourceLinksProps) {
  const displayedSources = sources.slice(0, maxSources)

  const extractDomain = (url: string) => {
    try {
      const urlObj = new URL(url)
      return urlObj.hostname.replace('www.', '')
    } catch {
      return url
    }
  }

  const getFaviconUrl = (url: string) => {
    try {
      const urlObj = new URL(url)
      return `https://www.google.com/s2/favicons?domain=${urlObj.hostname}&sz=16`
    } catch {
      return null
    }
  }

  const isValidUrl = (string: string) => {
    try {
      new URL(string)
      return true
    } catch {
      return false
    }
  }

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.05
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, x: -20 },
    visible: {
      opacity: 1,
      x: 0,
      transition: {
        type: "spring",
        stiffness: 400,
        damping: 25
      }
    }
  }

  if (sources.length === 0) {
    return null
  }

  return (
    <div>
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="space-y-2"
      >
        {displayedSources.map((source, index) => {
          const isUrl = isValidUrl(source)
          const domain = isUrl ? extractDomain(source) : null
          const faviconUrl = isUrl ? getFaviconUrl(source) : null

          return (
            <motion.div
              key={`${source}-${index}`}
              variants={itemVariants}
              className="flex items-center gap-3 p-2 rounded-md hover:bg-gray-50 transition-colors"
            >
              {/* Favicon or Icon */}
              <div className="flex-shrink-0">
                {faviconUrl ? (
                  <img
                    src={faviconUrl}
                    alt={`${domain} favicon`}
                    className="w-4 h-4"
                    onError={(e) => {
                      // Fallback to generic link icon on favicon load error
                      const target = e.target as HTMLImageElement
                      target.style.display = 'none'
                      const fallback = target.nextElementSibling as HTMLElement
                      if (fallback) fallback.style.display = 'block'
                    }}
                  />
                ) : null}
                <svg
                  className={`w-4 h-4 text-gray-400 ${faviconUrl ? 'hidden' : ''}`}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"
                  />
                </svg>
              </div>

              {/* Source Link or Text */}
              <div className="flex-1 min-w-0">
                {isUrl ? (
                  <motion.a
                    href={source}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-sm text-blue-600 hover:text-blue-800 hover:underline transition-colors truncate block"
                    whileHover={{ scale: 1.01 }}
                  >
                    {domain || source}
                  </motion.a>
                ) : (
                  <span className="text-sm text-gray-700 block truncate">
                    {source}
                  </span>
                )}
              </div>

              {/* External Link Icon */}
              {isUrl && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.1 }}
                  className="flex-shrink-0"
                >
                  <svg className="w-3 h-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                  </svg>
                </motion.div>
              )}
            </motion.div>
          )
        })}
      </motion.div>

      {sources.length > maxSources && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.3 }}
          className="mt-3 pt-2 border-t border-gray-100"
        >
          <p className="text-xs text-gray-500 text-center">
            and {sources.length - maxSources} more sources...
          </p>
        </motion.div>
      )}
    </div>
  )
}
