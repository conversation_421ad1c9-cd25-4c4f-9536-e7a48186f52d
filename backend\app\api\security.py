"""
Security Health Check Endpoint

Provides comprehensive security status monitoring:
- Environment variable validation
- Security configuration status
- Rate limiting status  
- Input validation health
- Error handling status
- Security event monitoring
"""

from fastapi import APIRouter, Depends, HTTPException, Request
from fastapi.responses import <PERSON><PERSON><PERSON>esponse
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import logging

from ..config.security import SecurityConfig, get_cors_config
from ..utils.environment import validate_environment, check_environment_security, get_env_access_log
from ..utils.validation import input_sanitizer
from ..utils.error_handling import degradation_manager, get_error_summary

logger = logging.getLogger(__name__)

# Create security router
security_router = APIRouter(prefix="/security", tags=["Security"])


@security_router.get("/health")
async def security_health_check(request: Request) -> Dict[str, Any]:
    """Comprehensive security health check"""
    
    try:
        health_status = {
            "timestamp": datetime.utcnow().isoformat(),
            "overall_status": "healthy",
            "checks": {},
            "recommendations": [],
            "warnings": []
        }
        
        # 1. Environment Variables Check
        env_status = validate_environment()
        health_status["checks"]["environment"] = {
            "status": "healthy" if env_status["valid"] else "unhealthy",
            "details": env_status
        }
        
        if not env_status["valid"]:
            health_status["overall_status"] = "unhealthy"
        
        health_status["warnings"].extend(env_status.get("warnings", []))
        
        # 2. Environment Security Check
        env_security = check_environment_security()
        health_status["checks"]["environment_security"] = {
            "status": "healthy" if env_security["secure"] else "degraded",
            "details": env_security
        }
        
        if not env_security["secure"]:
            health_status["overall_status"] = "degraded"
        
        health_status["recommendations"].extend(env_security.get("recommendations", []))
        
        # 3. Rate Limiting Status
        if hasattr(request.app.state, 'security_middleware'):
            rate_limit_status = {
                "enabled": True,
                "active_limits": len(SecurityConfig.RATE_LIMITS),
                "enforcement": "active"
            }
        else:
            rate_limit_status = {
                "enabled": False,
                "active_limits": 0,
                "enforcement": "disabled"
            }
            health_status["warnings"].append("Rate limiting middleware not detected")
        
        health_status["checks"]["rate_limiting"] = {
            "status": "healthy" if rate_limit_status["enabled"] else "unhealthy",
            "details": rate_limit_status
        }
        
        # 4. Input Validation Status
        validation_status = {
            "sanitizer_active": True,
            "patterns_loaded": {
                "sql_injection": len(SecurityConfig.SQL_INJECTION_PATTERNS),
                "xss_patterns": len(SecurityConfig.XSS_PATTERNS),
                "sensitive_patterns": len(SecurityConfig.SENSITIVE_PATTERNS)
            }
        }
        
        health_status["checks"]["input_validation"] = {
            "status": "healthy",
            "details": validation_status
        }
        
        # 5. Error Handling Status
        error_handling_status = {
            "degradation_mode": degradation_manager.is_degraded(),
            "circuit_breakers": degradation_manager.get_circuit_status(),
            "disabled_features": degradation_manager.get_disabled_features()
        }
        
        if degradation_manager.is_degraded():
            health_status["overall_status"] = "degraded"
            health_status["warnings"].append("System running in degraded mode")
        
        health_status["checks"]["error_handling"] = {
            "status": "degraded" if degradation_manager.is_degraded() else "healthy",
            "details": error_handling_status
        }
        
        # 6. CORS Configuration
        cors_config = get_cors_config()
        cors_status = {
            "configured": True,
            "allowed_origins_count": len(cors_config["allowed_origins"]),
            "credentials_allowed": cors_config["allow_credentials"],
            "methods_allowed": cors_config["allowed_methods"]
        }
        
        health_status["checks"]["cors"] = {
            "status": "healthy",
            "details": cors_status
        }
        
        # 7. Security Headers Status
        security_headers_status = {
            "headers_configured": len(SecurityConfig.SECURITY_HEADERS),
            "csp_enabled": "Content-Security-Policy" in SecurityConfig.CSP_DIRECTIVES,
            "strict_transport_security": "Strict-Transport-Security" in SecurityConfig.SECURITY_HEADERS
        }
        
        health_status["checks"]["security_headers"] = {
            "status": "healthy",
            "details": security_headers_status
        }
        
        # Add overall recommendations
        if health_status["overall_status"] == "healthy":
            health_status["message"] = "All security checks passed"
        elif health_status["overall_status"] == "degraded":
            health_status["message"] = "Security checks passed with warnings"
        else:
            health_status["message"] = "Security issues detected requiring attention"
        
        return health_status
        
    except Exception as e:
        logger.error(f"Security health check failed: {e}")
        return {
            "timestamp": datetime.utcnow().isoformat(),
            "overall_status": "error",
            "message": "Security health check failed",
            "error": str(e)
        }


@security_router.get("/environment")
async def environment_status() -> Dict[str, Any]:
    """Get environment variable status"""
    
    try:
        env_status = validate_environment()
        env_security = check_environment_security()
        
        return {
            "timestamp": datetime.utcnow().isoformat(),
            "validation": env_status,
            "security": env_security,
            "protected_variables": len(SecurityConfig.PROTECTED_ENV_VARS)
        }
        
    except Exception as e:
        logger.error(f"Environment status check failed: {e}")
        raise HTTPException(status_code=500, detail="Environment status check failed")


@security_router.get("/access-log")
async def get_access_log(limit: Optional[int] = 100) -> Dict[str, Any]:
    """Get environment variable access log"""
    
    try:
        access_log = get_env_access_log()
        
        # Limit results
        if limit and len(access_log) > limit:
            access_log = access_log[-limit:]
        
        return {
            "timestamp": datetime.utcnow().isoformat(),
            "total_accesses": len(access_log),
            "access_log": access_log
        }
        
    except Exception as e:
        logger.error(f"Access log retrieval failed: {e}")
        raise HTTPException(status_code=500, detail="Access log retrieval failed")


@security_router.get("/rate-limits")
async def get_rate_limit_status(request: Request) -> Dict[str, Any]:
    """Get rate limiting status and configuration"""
    
    try:
        rate_limits = SecurityConfig.RATE_LIMITS.copy()
        
        # Get current enforcement status
        enforcement_status = {
            "middleware_active": hasattr(request.app, 'middleware_stack'),
            "total_limits": len(rate_limits),
            "configuration": rate_limits
        }
        
        return {
            "timestamp": datetime.utcnow().isoformat(),
            "status": enforcement_status
        }
        
    except Exception as e:
        logger.error(f"Rate limit status check failed: {e}")
        raise HTTPException(status_code=500, detail="Rate limit status check failed")


@security_router.get("/validation")
async def get_validation_status() -> Dict[str, Any]:
    """Get input validation configuration status"""
    
    try:
        validation_config = {
            "max_lengths": SecurityConfig.INPUT_VALIDATION,
            "security_patterns": {
                "sql_injection": len(SecurityConfig.SQL_INJECTION_PATTERNS),
                "xss_patterns": len(SecurityConfig.XSS_PATTERNS), 
                "sensitive_data": len(SecurityConfig.SENSITIVE_PATTERNS)
            },
            "sanitizer_active": hasattr(input_sanitizer, 'sanitize_text')
        }
        
        return {
            "timestamp": datetime.utcnow().isoformat(),
            "configuration": validation_config
        }
        
    except Exception as e:
        logger.error(f"Validation status check failed: {e}")
        raise HTTPException(status_code=500, detail="Validation status check failed")


@security_router.post("/test-validation")
async def test_input_validation(test_input: Dict[str, str]) -> Dict[str, Any]:
    """Test input validation with sample data"""
    
    try:
        results = {}
        
        for key, value in test_input.items():
            if key == "text":
                result = input_sanitizer.sanitize_text(value)
            elif key == "query":
                result = input_sanitizer.sanitize_query(value)
            elif key == "email":
                result = input_sanitizer.validate_email(value)
            elif key == "url":
                result = input_sanitizer.validate_url(value)
            elif key == "filename":
                result = input_sanitizer.sanitize_filename(value)
            else:
                result = input_sanitizer.sanitize_text(value)
            
            results[key] = {
                "original": value,
                "sanitized": result.sanitized_value,
                "valid": result.is_valid,
                "errors": result.errors,
                "warnings": result.warnings,
                "security_flags": result.security_flags
            }
        
        return {
            "timestamp": datetime.utcnow().isoformat(),
            "test_results": results
        }
        
    except Exception as e:
        logger.error(f"Validation test failed: {e}")
        raise HTTPException(status_code=500, detail="Validation test failed")


@security_router.get("/summary")
async def security_summary(request: Request) -> Dict[str, Any]:
    """Get security configuration summary"""
    
    try:
        summary = {
            "timestamp": datetime.utcnow().isoformat(),
            "security_features": {
                "rate_limiting": {
                    "enabled": True,
                    "endpoints_protected": len(SecurityConfig.RATE_LIMITS)
                },
                "input_validation": {
                    "enabled": True,
                    "patterns_configured": len(SecurityConfig.SQL_INJECTION_PATTERNS) + len(SecurityConfig.XSS_PATTERNS)
                },
                "security_headers": {
                    "enabled": True,
                    "headers_configured": len(SecurityConfig.SECURITY_HEADERS)
                },
                "environment_protection": {
                    "enabled": True,
                    "protected_variables": len(SecurityConfig.PROTECTED_ENV_VARS)
                },
                "error_handling": {
                    "enabled": True,
                    "degradation_management": not degradation_manager.is_degraded()
                }
            },
            "recommendations": [
                "Regularly monitor security health endpoint",
                "Review and update rate limits based on usage patterns",
                "Audit environment variable access logs",
                "Test input validation with production-like data",
                "Monitor error handling patterns for anomalies"
            ]
        }
        
        return summary
        
    except Exception as e:
        logger.error(f"Security summary failed: {e}")
        raise HTTPException(status_code=500, detail="Security summary generation failed")
