/**
 * Frontend Error Handling System - Core Types and Classes
 * 
 * Provides comprehensive error handling for the React frontend:
 * - API error handling with retry logic
 * - User-friendly error messages
 * - Error recovery strategies
 * - Offline/network error handling
 */

import { toast } from 'react-hot-toast';
import React from 'react';

// Error types matching backend
export enum ErrorCategory {
  NETWORK = 'network',
  VALIDATION = 'validation',
  AUTHENTICATION = 'authentication',
  SYSTEM = 'system',
  USER_INPUT = 'user_input',
  EXTERNAL_SERVICE = 'external_service',
}

export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

export interface ApiError {
  error: boolean;
  message: string;
  category: ErrorCategory;
  severity: ErrorSeverity;
  recoverable: boolean;
  request_id: string;
  timestamp: string;
  retry_after?: number;
  service_status?: {
    message: string;
    degraded: boolean;
    features_disabled?: string[];
  };
  debug?: {
    technical_message: string;
    error_type: string;
    context: Record<string, any>;
  };
}

// API Error Handler
export class ApiErrorHandler {
  private static retryDelays = [1000, 2000, 4000]; // Exponential backoff

  static isApiError(error: any): error is ApiError {
    return error && typeof error === 'object' && error.error === true;
  }

  static getErrorMessage(error: any): string {
    if (this.isApiError(error)) {
      return error.message;
    }
    
    if (error instanceof Error) {
      return error.message;
    }
    
    if (typeof error === 'string') {
      return error;
    }
    
    return 'An unexpected error occurred';
  }

  static getErrorSeverity(error: any): ErrorSeverity {
    if (this.isApiError(error)) {
      return error.severity;
    }
    
    return ErrorSeverity.MEDIUM;
  }

  static showErrorToast(error: any) {
    const message = this.getErrorMessage(error);
    const severity = this.getErrorSeverity(error);
    
    const toastOptions = {
      duration: severity === ErrorSeverity.CRITICAL ? 8000 : 
                severity === ErrorSeverity.HIGH ? 6000 : 4000,
      position: 'top-right' as const,
    };

    switch (severity) {
      case ErrorSeverity.CRITICAL:
        toast.error(message, { ...toastOptions, id: `error-critical-${Date.now()}` });
        break;
      case ErrorSeverity.HIGH:
        toast.error(message, { ...toastOptions, id: `error-high-${Date.now()}` });
        break;
      case ErrorSeverity.MEDIUM:
        toast(message, { ...toastOptions, icon: '⚠️', id: `error-medium-${Date.now()}` });
        break;
      case ErrorSeverity.LOW:
        toast(message, { ...toastOptions, icon: 'ℹ️', id: `error-low-${Date.now()}` });
        break;
    }
  }

  static async handleApiCall<T>(
    apiCall: () => Promise<T>,
    options: {
      showToast?: boolean;
      retryable?: boolean;
      onError?: (error: any) => void;
      fallbackValue?: T;
    } = {}
  ): Promise<T | undefined> {
    const { showToast = true, retryable = true, onError, fallbackValue } = options;

    let lastError: any;
    const maxRetries = retryable ? this.retryDelays.length : 0;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        const result = await apiCall();
        return result;
      } catch (error: any) {
        lastError = error;

        // Don't retry on certain error types
        if (this.isApiError(error)) {
          if (!error.recoverable || 
              error.category === ErrorCategory.AUTHENTICATION ||
              error.category === ErrorCategory.VALIDATION) {
            break;
          }

          // Respect retry_after header
          if (error.retry_after && attempt < maxRetries) {
            await this.delay(error.retry_after * 1000);
            continue;
          }
        }

        // Retry with exponential backoff
        if (attempt < maxRetries && retryable) {
          await this.delay(this.retryDelays[attempt]);
          continue;
        }

        break;
      }
    }

    // All retries exhausted or non-retryable error
    if (showToast) {
      this.showErrorToast(lastError);
    }

    if (onError) {
      onError(lastError);
    }

    return fallbackValue;
  }

  private static delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  static handleNetworkError() {
    toast.error('Network connection lost. Please check your internet connection.', {
      id: 'network-error',
      duration: Infinity,
    });
  }

  static handleOfflineMode(isOffline: boolean) {
    if (isOffline) {
      toast('You are currently offline. Some features may not work.', {
        id: 'offline-mode',
        icon: '📱',
        duration: Infinity,
      });
    } else {
      toast.success('Connection restored!', {
        id: 'online-mode',
        duration: 3000,
      });
      toast.dismiss('offline-mode');
    }
  }
}

// Hook for handling online/offline status
export function useNetworkStatus() {
  const [isOnline, setIsOnline] = React.useState(navigator.onLine);

  React.useEffect(() => {
    function handleOnline() {
      setIsOnline(true);
      ApiErrorHandler.handleOfflineMode(false);
    }

    function handleOffline() {
      setIsOnline(false);
      ApiErrorHandler.handleOfflineMode(true);
    }

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  return isOnline;
}

// Hook for API calls with automatic error handling
export function useApiCall() {
  return React.useCallback(ApiErrorHandler.handleApiCall, []);
}

// Global error handler setup
export function setupGlobalErrorHandling() {
  // Handle unhandled promise rejections
  window.addEventListener('unhandledrejection', (event) => {
    console.error('Unhandled promise rejection:', event.reason);
    
    if (ApiErrorHandler.isApiError(event.reason)) {
      ApiErrorHandler.showErrorToast(event.reason);
    } else {
      toast.error('An unexpected error occurred');
    }
    
    event.preventDefault();
  });

  // Handle global errors
  window.addEventListener('error', (event) => {
    console.error('Global error:', event.error);
    toast.error('An unexpected error occurred');
  });
}

export default {
  ApiErrorHandler,
  useNetworkStatus,
  useApiCall,
  setupGlobalErrorHandling,
};
