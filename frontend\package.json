{"name": "ai-powered-dashboard", "private": true, "version": "1.0.0", "description": "AI-Powered Dashboard with Visual Transparency & Smooth Animations", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "preview": "vite preview", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "typecheck": "tsc --noEmit"}, "dependencies": {"@hookform/resolvers": "^3.9.0", "axios": "^1.10.0", "clsx": "^2.1.1", "framer-motion": "^11.5.4", "lucide-react": "^0.441.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.53.0", "react-hot-toast": "^2.4.1", "react-router-dom": "^6.26.2", "tailwind-merge": "^2.5.2", "zod": "^3.23.8", "zustand": "^4.5.5"}, "devDependencies": {"@testing-library/jest-dom": "^6.5.0", "@testing-library/react": "^16.0.1", "@testing-library/user-event": "^14.5.2", "@types/jest": "^29.5.12", "@types/node": "^22.5.4", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@typescript-eslint/eslint-plugin": "^8.6.0", "@typescript-eslint/parser": "^8.6.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.20", "eslint": "^9.10.0", "eslint-plugin-react-hooks": "^5.1.0-rc-fb9a90fa48-20240614", "eslint-plugin-react-refresh": "^0.4.11", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.4.47", "prettier": "^3.3.3", "tailwindcss": "^3.4.12", "typescript": "^5.6.2", "vite": "^5.4.6"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}