import React from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useAppStore } from '@/stores/appStore'
import { useAnimations } from '@/hooks/useAnimations'
import { 
  Brain, 
  Search, 
  Calendar, 
  CheckSquare, 
  Loader2, 
  Sparkles,
  CheckCircle2,
  AlertCircle,
  Zap
} from 'lucide-react'

/**
 * Processing step interface
 */
interface ProcessingStep {
  id: string
  label: string
  description: string
  icon: React.ElementType
  status: 'pending' | 'active' | 'completed' | 'error'
  progress?: number
}

/**
 * Visual Feedback System - Animation Orchestration following research/framer-motion patterns
 * 
 * Features:
 * - Implementation of EXACT mermaid diagram workflow
 * - Step-by-step visual indicators (analyzing, categorizing, processing)
 * - Smooth transitions between states
 * - Progress indicators and completion states
 * - Following framer-motion research for spring physics
 */
const VisualFeedback: React.FC = () => {
  const { isProcessing, currentOperation, progressPercentage } = useAppStore()
  const { 
    currentStep, 
    processingSteps, 
    showSuccessAnimation,
    showErrorAnimation
  } = useAnimations()

  // Default processing steps based on the mermaid diagram workflow
  const defaultSteps: ProcessingStep[] = [
    {
      id: 'analyze',
      label: 'Analyzing Input',
      description: 'Understanding your request with AI',
      icon: Brain,
      status: currentStep === 0 ? 'active' : currentStep > 0 ? 'completed' : 'pending',
    },
    {
      id: 'categorize',
      label: 'Categorizing',
      description: 'Determining if this is a task, event, or question',
      icon: Sparkles,
      status: currentStep === 1 ? 'active' : currentStep > 1 ? 'completed' : 'pending',
    },
    {
      id: 'route',
      label: 'Routing to Tools',
      description: 'Selecting the right AI tools for processing',
      icon: Zap,
      status: currentStep === 2 ? 'active' : currentStep > 2 ? 'completed' : 'pending',
    },
    {
      id: 'process',
      label: 'Processing',
      description: 'Executing through appropriate tools',
      icon: currentStep === 3 && processingSteps.some(step => step.id === 'calendar') ? Calendar : 
             currentStep === 3 && processingSteps.some(step => step.id === 'tasks') ? CheckSquare : 
             Search,
      status: currentStep === 3 ? 'active' : currentStep > 3 ? 'completed' : 'pending',
    },
  ]

  const activeSteps = processingSteps.length > 0 ? 
    processingSteps.map((step, index) => ({
      ...defaultSteps[index],
      ...step
    })) : defaultSteps

  // Don't render if not processing and no recent activity
  if (!isProcessing && currentStep === -1 && !showSuccessAnimation && !showErrorAnimation) {
    return null
  }

  return (
    <AnimatePresence mode="wait">
      {(isProcessing || currentStep >= 0 || showSuccessAnimation || showErrorAnimation) && (
        <motion.div
          initial={{ opacity: 0, y: 20, scale: 0.95 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          exit={{ opacity: 0, y: -20, scale: 0.95 }}
          transition={{
            type: 'spring',
            stiffness: 300,
            damping: 30,
          }}
          className="w-full max-w-2xl mx-auto mt-8"
        >
          {/* Success Animation */}
          <AnimatePresence>
            {showSuccessAnimation && (
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 1.2 }}
                transition={{
                  type: 'spring',
                  stiffness: 400,
                  damping: 20,
                }}
                className="text-center mb-6"
              >
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{
                    type: 'spring',
                    stiffness: 500,
                    damping: 15,
                    delay: 0.1,
                  }}
                  className="inline-flex items-center justify-center w-16 h-16 bg-accent-green/10 rounded-full mb-4"
                >
                  <CheckCircle2 className="w-8 h-8 text-accent-green" />
                </motion.div>
                <motion.h3
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.2 }}
                  className="text-xl font-semibold text-text-primary mb-2"
                >
                  Successfully Processed!
                </motion.h3>
                <motion.p
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.3 }}
                  className="text-text-secondary"
                >
                  Your request has been intelligently categorized and processed.
                </motion.p>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Error Animation */}
          <AnimatePresence>
            {showErrorAnimation && (
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 1.2 }}
                transition={{
                  type: 'spring',
                  stiffness: 400,
                  damping: 20,
                }}
                className="text-center mb-6"
              >
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1, rotate: [0, -5, 5, 0] }}
                  transition={{
                    type: 'spring',
                    stiffness: 500,
                    damping: 15,
                    delay: 0.1,
                  }}
                  className="inline-flex items-center justify-center w-16 h-16 bg-accent-red/10 rounded-full mb-4"
                >
                  <AlertCircle className="w-8 h-8 text-accent-red" />
                </motion.div>
                <motion.h3
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.2 }}
                  className="text-xl font-semibold text-text-primary mb-2"
                >
                  Processing Failed
                </motion.h3>
                <motion.p
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.3 }}
                  className="text-text-secondary"
                >
                  There was an issue processing your request. Please try again.
                </motion.p>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Processing Steps */}
          <AnimatePresence>
            {(isProcessing || currentStep >= 0) && !showSuccessAnimation && !showErrorAnimation && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="card-primary p-6"
              >
                {/* Header */}
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-accent-blue/10 rounded-lg flex items-center justify-center">
                      <Loader2 className="w-5 h-5 text-accent-blue animate-spin" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-text-primary">AI Processing</h3>
                      <p className="text-text-secondary text-sm">
                        {currentOperation || 'Analyzing your input...'}
                      </p>
                    </div>
                  </div>
                  
                  {/* Overall Progress */}
                  <div className="text-right">
                    <div className="text-text-primary font-medium text-sm">
                      {Math.round(progressPercentage)}%
                    </div>
                    <div className="text-text-muted text-xs">
                      Step {Math.max(0, currentStep + 1)} of {activeSteps.length}
                    </div>
                  </div>
                </div>

                {/* Progress Bar */}
                <div className="mb-6">
                  <div className="h-2 bg-background-hover rounded-full overflow-hidden">
                    <motion.div
                      initial={{ width: 0 }}
                      animate={{ width: `${progressPercentage}%` }}
                      transition={{
                        type: 'spring',
                        stiffness: 100,
                        damping: 15,
                      }}
                      className="h-full bg-gradient-to-r from-accent-blue to-accent-purple"
                    />
                  </div>
                </div>

                {/* Processing Steps */}
                <div className="space-y-4">
                  {activeSteps.map((step, index) => {
                    const isActive = step.status === 'active'
                    const isCompleted = step.status === 'completed'
                    const isPending = step.status === 'pending'
                    const isError = step.status === 'error'

                    return (
                      <motion.div
                        key={step.id}
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{
                          delay: index * 0.1,
                          type: 'spring',
                          stiffness: 300,
                          damping: 30,
                        }}
                        className={`flex items-center gap-4 p-4 rounded-xl border transition-all duration-300 ${
                          isActive 
                            ? 'bg-accent-blue/5 border-accent-blue/20' 
                            : isCompleted 
                            ? 'bg-accent-green/5 border-accent-green/20' 
                            : isError
                            ? 'bg-accent-red/5 border-accent-red/20'
                            : 'bg-background-hover/30 border-border-primary'
                        }`}
                      >
                        {/* Step Icon */}
                        <motion.div
                          animate={
                            isActive 
                              ? { scale: [1, 1.1, 1], rotate: [0, 5, -5, 0] }
                              : { scale: 1, rotate: 0 }
                          }
                          transition={{
                            duration: isActive ? 2 : 0.3,
                            repeat: isActive ? Infinity : 0,
                            ease: 'easeInOut',
                          }}
                          className={`flex items-center justify-center w-12 h-12 rounded-xl ${
                            isActive 
                              ? 'bg-accent-blue text-white' 
                              : isCompleted 
                              ? 'bg-accent-green text-white' 
                              : isError
                              ? 'bg-accent-red text-white'
                              : 'bg-background-secondary text-text-muted'
                          }`}
                        >
                          {isCompleted ? (
                            <CheckCircle2 className="w-6 h-6" />
                          ) : isError ? (
                            <AlertCircle className="w-6 h-6" />
                          ) : (
                            <step.icon className="w-6 h-6" />
                          )}
                        </motion.div>

                        {/* Step Content */}
                        <div className="flex-1">
                          <div className={`font-medium ${
                            isActive || isCompleted 
                              ? 'text-text-primary' 
                              : 'text-text-secondary'
                          }`}>
                            {step.label}
                          </div>
                          <div className="text-text-muted text-sm">
                            {step.description}
                          </div>
                        </div>

                        {/* Step Status Indicator */}
                        <div className="flex items-center">
                          {isActive && (
                            <motion.div
                              animate={{ rotate: 360 }}
                              transition={{
                                duration: 1,
                                repeat: Infinity,
                                ease: 'linear',
                              }}
                            >
                              <Loader2 className="w-5 h-5 text-accent-blue" />
                            </motion.div>
                          )}
                          {isCompleted && (
                            <motion.div
                              initial={{ scale: 0 }}
                              animate={{ scale: 1 }}
                              transition={{
                                type: 'spring',
                                stiffness: 500,
                                damping: 20,
                              }}
                            >
                              <CheckCircle2 className="w-5 h-5 text-accent-green" />
                            </motion.div>
                          )}
                          {isPending && (
                            <div className="w-3 h-3 bg-text-muted/30 rounded-full" />
                          )}
                          {isError && (
                            <motion.div
                              initial={{ scale: 0 }}
                              animate={{ scale: 1 }}
                              transition={{
                                type: 'spring',
                                stiffness: 500,
                                damping: 20,
                              }}
                            >
                              <AlertCircle className="w-5 h-5 text-accent-red" />
                            </motion.div>
                          )}
                        </div>
                      </motion.div>
                    )
                  })}
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>
      )}
    </AnimatePresence>
  )
}

export default VisualFeedback
