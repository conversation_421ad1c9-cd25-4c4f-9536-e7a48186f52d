import { create } from 'zustand'
import { persist } from 'zustand/middleware'

/**
 * Global application state interface
 */
export interface AppState {
  // Initialization state
  isInitialized: boolean
  initializationError: string | null

  // Theme and preferences
  theme: 'dark' | 'light' | 'system'
  sidebarCollapsed: boolean
  animationsEnabled: boolean
  
  // User preferences
  preferences: {
    defaultView: 'dashboard' | 'tasks' | 'events' | 'search'
    autoSaveInterval: number
    notificationsEnabled: boolean
    soundEnabled: boolean
    compactMode: boolean
  }

  // Connection state
  backendConnected: boolean
  websocketConnected: boolean
  lastSyncTime: Date | null

  // Processing state
  isProcessing: boolean
  currentOperation: string | null
  progressPercentage: number

  // Error handling
  errors: Array<{
    id: string
    message: string
    timestamp: Date
    severity: 'error' | 'warning' | 'info'
    context?: string
  }>

  // Actions
  initialize: () => Promise<void>
  setTheme: (theme: 'dark' | 'light' | 'system') => void
  toggleSidebar: () => void
  setAnimationsEnabled: (enabled: boolean) => void
  updatePreferences: (preferences: Partial<AppState['preferences']>) => void
  setBackendConnected: (connected: boolean) => void
  setWebsocketConnected: (connected: boolean) => void
  updateSyncTime: () => void
  setProcessing: (processing: boolean, operation?: string, progress?: number) => void
  addError: (message: string, severity?: 'error' | 'warning' | 'info', context?: string) => void
  removeError: (id: string) => void
  clearErrors: () => void
  reset: () => void
}

/**
 * Default application state
 */
const defaultState = {
  isInitialized: false,
  initializationError: null,
  theme: 'dark' as const,
  sidebarCollapsed: false,
  animationsEnabled: true,
  preferences: {
    defaultView: 'dashboard' as const,
    autoSaveInterval: 30000, // 30 seconds
    notificationsEnabled: true,
    soundEnabled: true,
    compactMode: false,
  },
  backendConnected: false,
  websocketConnected: false,
  lastSyncTime: null,
  isProcessing: false,
  currentOperation: null,
  progressPercentage: 0,
  errors: [],
}

/**
 * Global application store using Zustand with persistence
 */
export const useAppStore = create<AppState>()(
  persist(
    (set, get) => ({
      ...defaultState,

      initialize: async () => {
        try {
          set({ isProcessing: true, currentOperation: 'Initializing application...' })
          
          // Initialize application state
          // This could include loading user settings, checking backend status, etc.
          await new Promise(resolve => setTimeout(resolve, 1000)) // Simulate initialization
          
          // Check system preferences for theme
          const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
          const { theme } = get()
          
          if (theme === 'system') {
            document.documentElement.classList.toggle('dark', systemPrefersDark)
          } else {
            document.documentElement.classList.toggle('dark', theme === 'dark')
          }
          
          set({ 
            isInitialized: true, 
            isProcessing: false, 
            currentOperation: null,
            initializationError: null,
            lastSyncTime: new Date()
          })
          
        } catch (error) {
          console.error('Failed to initialize application:', error)
          set({ 
            initializationError: error instanceof Error ? error.message : 'Unknown initialization error',
            isProcessing: false,
            currentOperation: null
          })
        }
      },

      setTheme: (theme) => {
        set({ theme })
        
        // Apply theme to document
        if (theme === 'system') {
          const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
          document.documentElement.classList.toggle('dark', systemPrefersDark)
        } else {
          document.documentElement.classList.toggle('dark', theme === 'dark')
        }
      },

      toggleSidebar: () => {
        set(state => ({ sidebarCollapsed: !state.sidebarCollapsed }))
      },

      setAnimationsEnabled: (enabled) => {
        set({ animationsEnabled: enabled })
        
        // Apply reduced motion preference to document
        if (!enabled) {
          document.documentElement.classList.add('reduce-motion')
        } else {
          document.documentElement.classList.remove('reduce-motion')
        }
      },

      updatePreferences: (newPreferences) => {
        set(state => ({
          preferences: { ...state.preferences, ...newPreferences }
        }))
      },

      setBackendConnected: (connected) => {
        set({ backendConnected: connected })
        if (connected) {
          set({ lastSyncTime: new Date() })
        }
      },

      setWebsocketConnected: (connected) => {
        set({ websocketConnected: connected })
      },

      updateSyncTime: () => {
        set({ lastSyncTime: new Date() })
      },

      setProcessing: (processing, operation, progress = 0) => {
        set({ 
          isProcessing: processing, 
          currentOperation: operation || null,
          progressPercentage: progress
        })
      },

      addError: (message, severity = 'error', context) => {
        const error = {
          id: crypto.randomUUID(),
          message,
          timestamp: new Date(),
          severity,
          context,
        }
        
        set(state => ({ 
          errors: [...state.errors, error]
        }))
        
        // Auto-remove info messages after 5 seconds
        if (severity === 'info') {
          setTimeout(() => {
            get().removeError(error.id)
          }, 5000)
        }
      },

      removeError: (id) => {
        set(state => ({ 
          errors: state.errors.filter(error => error.id !== id)
        }))
      },

      clearErrors: () => {
        set({ errors: [] })
      },

      reset: () => {
        set(defaultState)
      },
    }),
    {
      name: 'ai-dashboard-app-state',
      partialize: (state) => ({
        theme: state.theme,
        sidebarCollapsed: state.sidebarCollapsed,
        animationsEnabled: state.animationsEnabled,
        preferences: state.preferences,
      }),
    }
  )
)
