# Task 30: Testing Suite - Backend API
# Comprehensive pytest test suite for FastAPI backend and Mirascope agents

import pytest
import pytest_asyncio
from httpx import AsyncClient
from fastapi.testclient import TestClient
from unittest.mock import AsyncMock, MagicMock, patch, Mock
import asyncio
import json
from typing import AsyncGenerator, Dict, Any
from datetime import datetime, timedelta

# Import app components
from backend.app.main import app
from backend.app.models.pydantic_models import UserInput, CategoryDecision
from backend.app.config.settings import get_settings

# Test client setup
@pytest.fixture
def client():
    """Synchronous HTTP client for testing FastAPI endpoints."""
    return TestClient(app)

@pytest_asyncio.fixture
async def async_client() -> AsyncGenerator[AsyncClient, None]:
    """Async HTTP client for testing FastAPI endpoints."""
    async with Async<PERSON>lient(app=app, base_url="http://test") as client:
        yield client

@pytest.fixture
def mock_llm_response():
    """Mock LLM response for testing agent behavior."""
    return {
        "category": "calendar",
        "confidence": 0.95,
        "reasoning": "User is asking about scheduling an event",
        "suggested_actions": [
            {
                "type": "create_event",
                "parameters": {
                    "title": "Team Meeting",
                    "start_time": "2024-01-20T14:00:00Z",
                    "duration": 60
                }
            }
        ]
    }

@pytest.fixture
def mock_openrouter_client():
    """Mock OpenRouter client for testing."""
    mock_client = MagicMock()
    mock_client.chat.completions.create = AsyncMock()
    return mock_client

@pytest.fixture
def sample_task_data():
    """Sample task data for testing."""
    return {
        "title": "Test Task",
        "description": "Test task description",
        "priority": "high",
        "due_date": "2025-07-25",
        "category": "Development",
        "completed": False
    }

@pytest.fixture
def sample_event_data():
    """Sample event data for testing."""
    return {
        "title": "Test Meeting",
        "description": "Test meeting description",
        "start_time": "2025-07-22T14:00:00Z",
        "end_time": "2025-07-22T15:00:00Z",
        "location": "Conference Room A",
        "category": "Work"
    }

class TestHealthEndpoints:
    """Test health and status endpoints."""
    
    def test_health_check(self, client):
        """Test the health check endpoint."""
        response = client.get("/health")
        assert response.status_code == 200
        data = response.json()
        assert "status" in data
        assert data["status"] == "healthy"
        assert "timestamp" in data

    @pytest.mark.asyncio
    async def test_health_check_async(self, async_client):
        """Test the health check endpoint asynchronously."""
        response = await async_client.get("/health")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"

class TestCoreAPIEndpoints:
    """Test core API endpoints for processing user input."""
    
    @pytest.mark.asyncio
    async def test_process_endpoint_task_creation(self, async_client, sample_task_data):
        """Test processing user input for task creation."""
        user_input = {
            "text": "Create a task to finish the AI dashboard by Friday",
            "mode": "auto"
        }
        
        with patch('backend.app.agents.orchestrator.AIOrchestrator.process_input') as mock_process:
            mock_process.return_value = {
                "category": "task",
                "confidence": 0.95,
                "result": {
                    "task_created": True,
                    "task_data": sample_task_data
                }
            }
            
            response = await async_client.post("/api/process", json=user_input)
            assert response.status_code == 200
            
            data = response.json()
            assert data["category"] == "task"
            assert data["confidence"] == 0.95
            assert "result" in data
            mock_process.assert_called_once()

    @pytest.mark.asyncio
    async def test_process_endpoint_calendar_creation(self, async_client, sample_event_data):
        """Test processing user input for calendar event creation."""
        user_input = {
            "text": "Schedule a team meeting tomorrow at 2 PM",
            "mode": "auto"
        }
        
        with patch('backend.app.agents.orchestrator.AIOrchestrator.process_input') as mock_process:
            mock_process.return_value = {
                "category": "calendar",
                "confidence": 0.92,
                "result": {
                    "event_created": True,
                    "event_data": sample_event_data
                }
            }
            
            response = await async_client.post("/api/process", json=user_input)
            assert response.status_code == 200
            
            data = response.json()
            assert data["category"] == "calendar"
            assert data["confidence"] == 0.92
            mock_process.assert_called_once()

    @pytest.mark.asyncio
    async def test_process_endpoint_ai_question(self, async_client):
        """Test processing user input for AI questions."""
        user_input = {
            "text": "What are the best practices for React performance?",
            "mode": "auto"
        }
        
        with patch('backend.app.agents.orchestrator.AIOrchestrator.process_input') as mock_process:
            mock_process.return_value = {
                "category": "ai_question",
                "confidence": 0.88,
                "result": {
                    "answer": "React performance best practices include...",
                    "sources": ["React docs", "Performance guides"]
                }
            }
            
            response = await async_client.post("/api/process", json=user_input)
            assert response.status_code == 200
            
            data = response.json()
            assert data["category"] == "ai_question"
            assert "answer" in data["result"]

    @pytest.mark.asyncio
    async def test_process_endpoint_validation(self, async_client):
        """Test input validation for process endpoint."""
        # Test missing text field
        response = await async_client.post("/api/process", json={"mode": "auto"})
        assert response.status_code == 422
        
        # Test empty text
        response = await async_client.post("/api/process", json={"text": "", "mode": "auto"})
        assert response.status_code == 400
        
        # Test invalid mode
        response = await async_client.post("/api/process", json={"text": "test", "mode": "invalid"})
        assert response.status_code == 422

class TestTaskAPI:
    """Test task-related API endpoints."""
    
    @pytest.mark.asyncio
    async def test_get_tasks(self, async_client):
        """Test getting all tasks."""
        with patch('backend.app.core.database.get_tasks') as mock_get_tasks:
            mock_get_tasks.return_value = [
                {
                    "id": 1,
                    "title": "Test Task",
                    "description": "Test description",
                    "completed": False,
                    "created_at": datetime.utcnow()
                }
            ]
            
            response = await async_client.get("/api/tasks")
            assert response.status_code == 200
            
            data = response.json()
            assert "tasks" in data
            assert len(data["tasks"]) == 1
            assert data["tasks"][0]["title"] == "Test Task"

    @pytest.mark.asyncio
    async def test_create_task(self, async_client, sample_task_data):
        """Test creating a new task."""
        with patch('backend.app.core.database.create_task') as mock_create_task:
            mock_create_task.return_value = {
                "id": 1,
                **sample_task_data,
                "created_at": datetime.utcnow()
            }
            
            response = await async_client.post("/api/tasks", json=sample_task_data)
            assert response.status_code == 201
            
            data = response.json()
            assert data["task"]["title"] == sample_task_data["title"]
            assert data["task"]["id"] == 1

    @pytest.mark.asyncio
    async def test_update_task(self, async_client):
        """Test updating an existing task."""
        task_id = 1
        update_data = {"completed": True}
        
        with patch('backend.app.core.database.update_task') as mock_update_task:
            mock_update_task.return_value = {
                "id": task_id,
                "title": "Test Task",
                "completed": True
            }
            
            response = await async_client.patch(f"/api/tasks/{task_id}", json=update_data)
            assert response.status_code == 200
            
            data = response.json()
            assert data["task"]["completed"] == True
            mock_update_task.assert_called_once_with(task_id, update_data)

    @pytest.mark.asyncio
    async def test_delete_task(self, async_client):
        """Test deleting a task."""
        task_id = 1
        
        with patch('backend.app.core.database.delete_task') as mock_delete_task:
            mock_delete_task.return_value = True
            
            response = await async_client.delete(f"/api/tasks/{task_id}")
            assert response.status_code == 200
            
            data = response.json()
            assert data["deleted"] == True
            mock_delete_task.assert_called_once_with(task_id)

class TestCalendarAPI:
    """Test calendar-related API endpoints."""
    
    @pytest.mark.asyncio
    async def test_get_events(self, async_client):
        """Test getting all calendar events."""
        with patch('backend.app.core.database.get_events') as mock_get_events:
            mock_get_events.return_value = [
                {
                    "id": 1,
                    "title": "Test Event",
                    "start_time": datetime.utcnow(),
                    "end_time": datetime.utcnow() + timedelta(hours=1),
                    "created_at": datetime.utcnow()
                }
            ]
            
            response = await async_client.get("/api/calendar/events")
            assert response.status_code == 200
            
            data = response.json()
            assert "events" in data
            assert len(data["events"]) == 1

    @pytest.mark.asyncio
    async def test_create_event(self, async_client, sample_event_data):
        """Test creating a new calendar event."""
        with patch('backend.app.core.database.create_event') as mock_create_event:
            mock_create_event.return_value = {
                "id": 1,
                **sample_event_data,
                "created_at": datetime.utcnow()
            }
            
            response = await async_client.post("/api/calendar/events", json=sample_event_data)
            assert response.status_code == 201
            
            data = response.json()
            assert data["event"]["title"] == sample_event_data["title"]

class TestWebSocketEndpoints:
    """Test WebSocket functionality."""
    
    @pytest.mark.asyncio
    async def test_websocket_connection(self, async_client):
        """Test WebSocket connection and basic communication."""
        with patch('backend.app.main.websocket_manager.connect') as mock_connect:
            with patch('backend.app.main.websocket_manager.send_personal_message') as mock_send:
                mock_connect.return_value = None
                mock_send.return_value = None
                
                # Test connection establishment
                with async_client.websocket_connect("/ws") as websocket:
                    # Send test message
                    test_message = {"type": "test", "data": "hello"}
                    await websocket.send_json(test_message)
                    
                    # Verify connection was established
                    mock_connect.assert_called_once()

class TestAIOrchestrator:
    """Test AI Orchestrator functionality."""
    
    @pytest.mark.asyncio
    async def test_orchestrator_task_processing(self, mock_openrouter_client):
        """Test AI Orchestrator task processing."""
        from backend.app.agents.orchestrator import AIOrchestrator
        
        # Mock LLM response for task categorization
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message = Mock()
        mock_response.choices[0].message.content = json.dumps({
            "category": "task",
            "confidence": 0.95,
            "reasoning": "User wants to create a task",
            "extracted_data": {
                "title": "Test Task",
                "priority": "high"
            }
        })
        
        mock_openrouter_client.chat.completions.create.return_value = mock_response
        
        with patch('backend.app.agents.orchestrator.get_openrouter_client', return_value=mock_openrouter_client):
            orchestrator = AIOrchestrator()
            user_input = UserInput(text="Create a high priority task to test the system", mode="auto")
            
            result = await orchestrator.process_input(user_input)
            
            assert result["category"] == "task"
            assert result["confidence"] == 0.95
            assert "extracted_data" in result

    @pytest.mark.asyncio 
    async def test_orchestrator_calendar_processing(self, mock_openrouter_client):
        """Test AI Orchestrator calendar processing."""
        from backend.app.agents.orchestrator import AIOrchestrator
        
        # Mock LLM response for calendar categorization
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message = Mock()
        mock_response.choices[0].message.content = json.dumps({
            "category": "calendar",
            "confidence": 0.92,
            "reasoning": "User wants to schedule an event",
            "extracted_data": {
                "title": "Team Meeting",
                "start_time": "2025-07-22T14:00:00Z"
            }
        })
        
        mock_openrouter_client.chat.completions.create.return_value = mock_response
        
        with patch('backend.app.agents.orchestrator.get_openrouter_client', return_value=mock_openrouter_client):
            orchestrator = AIOrchestrator()
            user_input = UserInput(text="Schedule a team meeting tomorrow at 2 PM", mode="auto")
            
            result = await orchestrator.process_input(user_input)
            
            assert result["category"] == "calendar"
            assert result["confidence"] == 0.92

class TestTools:
    """Test individual tools functionality."""
    
    @pytest.mark.asyncio
    async def test_task_tool(self):
        """Test TaskTool functionality."""
        from backend.app.tools.task_tool import TaskTool, CreateTaskInput
        
        tool = TaskTool()
        task_input = CreateTaskInput(
            title="Test Task",
            description="Test Description",
            priority="high",
            due_date="2025-07-25"
        )
        
        with patch('backend.app.core.database.create_task') as mock_create:
            mock_create.return_value = {"id": 1, "title": "Test Task"}
            
            result = await tool.call(task_input)
            
            assert "task_created" in result
            assert result["task_created"] == True

    @pytest.mark.asyncio
    async def test_calendar_tool(self):
        """Test CalendarTool functionality."""
        from backend.app.tools.calendar_tool import CalendarTool, CreateEventInput
        
        tool = CalendarTool()
        event_input = CreateEventInput(
            title="Test Event",
            start_time="2025-07-22T14:00:00Z",
            end_time="2025-07-22T15:00:00Z"
        )
        
        with patch('backend.app.core.database.create_event') as mock_create:
            mock_create.return_value = {"id": 1, "title": "Test Event"}
            
            result = await tool.call(event_input)
            
            assert "event_created" in result
            assert result["event_created"] == True

    @pytest.mark.asyncio
    async def test_web_search_tool(self):
        """Test WebSearchTool functionality."""
        from backend.app.tools.web_search_tool import WebSearchTool, WebSearchInput
        
        tool = WebSearchTool()
        search_input = WebSearchInput(query="React performance best practices")
        
        # Mock the search API response
        with patch('httpx.AsyncClient.get') as mock_get:
            mock_response = Mock()
            mock_response.json.return_value = {
                "results": [
                    {
                        "title": "React Performance Guide",
                        "url": "https://example.com",
                        "snippet": "Best practices for React performance..."
                    }
                ]
            }
            mock_get.return_value = mock_response
            
            result = await tool.call(search_input)
            
            assert "search_results" in result
            assert len(result["search_results"]) > 0

class TestErrorHandling:
    """Test error handling and edge cases."""
    
    @pytest.mark.asyncio
    async def test_invalid_json_input(self, async_client):
        """Test handling of invalid JSON input."""
        response = await async_client.post(
            "/api/process", 
            data="invalid json",
            headers={"Content-Type": "application/json"}
        )
        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_missing_required_fields(self, async_client):
        """Test handling of missing required fields."""
        response = await async_client.post("/api/tasks", json={})
        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_database_error_handling(self, async_client):
        """Test handling of database errors."""
        with patch('backend.app.core.database.get_tasks', side_effect=Exception("Database error")):
            response = await async_client.get("/api/tasks")
            assert response.status_code == 500

class TestSecurity:
    """Test security-related functionality."""
    
    @pytest.mark.asyncio
    async def test_rate_limiting(self, async_client):
        """Test rate limiting functionality."""
        # Make multiple rapid requests
        responses = []
        for _ in range(60):  # Exceed rate limit
            response = await async_client.get("/health")
            responses.append(response.status_code)
        
        # Should have some rate-limited responses
        assert any(status == 429 for status in responses[-10:])

    @pytest.mark.asyncio
    async def test_input_validation(self, async_client):
        """Test input validation and sanitization."""
        malicious_input = {
            "text": "<script>alert('xss')</script>",
            "mode": "auto"
        }
        
        response = await async_client.post("/api/process", json=malicious_input)
        # Should not return 500 error - should handle gracefully
        assert response.status_code != 500

class TestPerformance:
    """Test performance-related functionality."""
    
    @pytest.mark.asyncio
    async def test_concurrent_requests(self, async_client):
        """Test handling of concurrent requests."""
        async def make_request():
            return await async_client.get("/health")
        
        # Make 10 concurrent requests
        tasks = [make_request() for _ in range(10)]
        responses = await asyncio.gather(*tasks)
        
        # All should succeed
        for response in responses:
            assert response.status_code == 200

    @pytest.mark.asyncio
    async def test_large_payload_handling(self, async_client):
        """Test handling of large payloads."""
        large_text = "A" * 10000  # 10KB text
        
        response = await async_client.post("/api/process", json={
            "text": large_text,
            "mode": "auto"
        })
        
        # Should handle gracefully (either process or return appropriate error)
        assert response.status_code in [200, 413, 422]

if __name__ == "__main__":
    pytest.main([__file__, "-v", "--tb=short"])
