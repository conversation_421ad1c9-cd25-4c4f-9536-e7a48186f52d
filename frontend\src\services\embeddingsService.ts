/**
 * Embeddings Service  
 * PATTERN: Ollama embeddings integration
 * Features:
 * - Model management (download, load, unload)
 * - Batch embedding processing
 * - Vector storage and retrieval
 * - Performance optimization
 */

export interface EmbeddingModel {
  name: string
  size: number
  status: 'available' | 'downloading' | 'loaded' | 'error'
}

export interface EmbeddingRequest {
  text: string
  model?: string
}

export interface EmbeddingResponse {
  embedding: number[]
  model: string
  dimensions: number
}

export interface DocumentEmbedding {
  id: string
  text: string
  embedding: number[]
  metadata?: Record<string, any>
  timestamp: number
}

class EmbeddingsService {
  private baseUrl: string
  private defaultModel = 'nomic-embed-text'
  private cache: Map<string, EmbeddingResponse> = new Map()
  private isConnected = false

  constructor() {
    this.baseUrl = import.meta.env.VITE_OLLAMA_BASE_URL || 'http://localhost:11434'
    this.checkConnection()
  }

  /**
   * Check if Ollama service is available
   */
  async checkConnection(): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/api/tags`)
      this.isConnected = response.ok
      return this.isConnected
    } catch (error) {
      console.error('[Embeddings] Connection check failed:', error)
      this.isConnected = false
      return false
    }
  }

  /**
   * Get list of available models
   */
  async getAvailableModels(): Promise<EmbeddingModel[]> {
    try {
      if (!this.isConnected) {
        await this.checkConnection()
      }

      const response = await fetch(`${this.baseUrl}/api/tags`)
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()
      
      return data.models?.map((model: any) => ({
        name: model.name,
        size: model.size || 0,
        status: 'available' as const
      })) || []
    } catch (error) {
      console.error('[Embeddings] Failed to get models:', error)
      return []
    }
  }

  /**
   * Pull/download a model
   */
  async pullModel(modelName: string, onProgress?: (progress: number) => void): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/api/pull`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ name: modelName, stream: true }),
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const reader = response.body?.getReader()
      if (!reader) {
        throw new Error('No response body')
      }

      let totalSize = 0
      let downloadedSize = 0

      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        const chunk = new TextDecoder().decode(value)
        const lines = chunk.split('\n').filter(line => line.trim())

        for (const line of lines) {
          try {
            const data = JSON.parse(line)
            
            if (data.total) {
              totalSize = data.total
            }
            
            if (data.completed) {
              downloadedSize = data.completed
            }

            if (totalSize > 0 && onProgress) {
              const progress = (downloadedSize / totalSize) * 100
              onProgress(Math.min(progress, 100))
            }

            if (data.status === 'success') {
              return true
            }
          } catch (parseError) {
            // Ignore invalid JSON lines
          }
        }
      }

      return true
    } catch (error) {
      console.error('[Embeddings] Failed to pull model:', error)
      return false
    }
  }

  /**
   * Generate embedding for text
   */
  async generateEmbedding(request: EmbeddingRequest): Promise<EmbeddingResponse> {
    const model = request.model || this.defaultModel
    const cacheKey = `${model}:${request.text}`

    // Check cache first
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey)!
    }

    try {
      if (!this.isConnected) {
        await this.checkConnection()
        if (!this.isConnected) {
          throw new Error('Ollama service not available')
        }
      }

      const response = await fetch(`${this.baseUrl}/api/embeddings`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model,
          prompt: request.text,
        }),
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()
      
      const result: EmbeddingResponse = {
        embedding: data.embedding,
        model,
        dimensions: data.embedding.length,
      }

      // Cache the result
      this.cache.set(cacheKey, result)

      return result
    } catch (error) {
      console.error('[Embeddings] Failed to generate embedding:', error)
      throw error
    }
  }

  /**
   * Generate embeddings for multiple texts
   */
  async generateBatchEmbeddings(
    texts: string[],
    model?: string,
    onProgress?: (completed: number, total: number) => void
  ): Promise<EmbeddingResponse[]> {
    const results: EmbeddingResponse[] = []
    
    for (let i = 0; i < texts.length; i++) {
      try {
        const embedding = await this.generateEmbedding({
          text: texts[i],
          model,
        })
        results.push(embedding)
        
        if (onProgress) {
          onProgress(i + 1, texts.length)
        }
      } catch (error) {
        console.error(`[Embeddings] Failed to generate embedding for text ${i}:`, error)
        // Continue with other texts
      }
    }
    
    return results
  }

  /**
   * Calculate cosine similarity between two embeddings
   */
  calculateSimilarity(embedding1: number[], embedding2: number[]): number {
    if (embedding1.length !== embedding2.length) {
      throw new Error('Embeddings must have the same dimensions')
    }

    let dotProduct = 0
    let norm1 = 0
    let norm2 = 0

    for (let i = 0; i < embedding1.length; i++) {
      dotProduct += embedding1[i] * embedding2[i]
      norm1 += embedding1[i] * embedding1[i]
      norm2 += embedding2[i] * embedding2[i]
    }

    const magnitude = Math.sqrt(norm1) * Math.sqrt(norm2)
    return magnitude === 0 ? 0 : dotProduct / magnitude
  }

  /**
   * Find most similar documents to a query
   */
  findSimilarDocuments(
    queryEmbedding: number[],
    documents: DocumentEmbedding[],
    topK = 5,
    threshold = 0.7
  ): Array<DocumentEmbedding & { similarity: number }> {
    const similarities = documents.map(doc => ({
      ...doc,
      similarity: this.calculateSimilarity(queryEmbedding, doc.embedding)
    }))

    return similarities
      .filter(doc => doc.similarity >= threshold)
      .sort((a, b) => b.similarity - a.similarity)
      .slice(0, topK)
  }

  /**
   * Store document embedding (in memory for now)
   */
  private documentStore: Map<string, DocumentEmbedding> = new Map()

  async storeDocumentEmbedding(
    id: string,
    text: string,
    metadata?: Record<string, any>
  ): Promise<DocumentEmbedding> {
    const embeddingResponse = await this.generateEmbedding({ text })
    
    const document: DocumentEmbedding = {
      id,
      text,
      embedding: embeddingResponse.embedding,
      metadata,
      timestamp: Date.now(),
    }

    this.documentStore.set(id, document)
    return document
  }

  /**
   * Search stored documents
   */
  async searchDocuments(
    query: string,
    topK = 5,
    threshold = 0.7
  ): Promise<Array<DocumentEmbedding & { similarity: number }>> {
    const queryEmbeddingResponse = await this.generateEmbedding({ text: query })
    const documents = Array.from(this.documentStore.values())
    
    return this.findSimilarDocuments(
      queryEmbeddingResponse.embedding,
      documents,
      topK,
      threshold
    )
  }

  /**
   * Get stored document by ID
   */
  getDocument(id: string): DocumentEmbedding | null {
    return this.documentStore.get(id) || null
  }

  /**
   * Remove stored document
   */
  removeDocument(id: string): boolean {
    return this.documentStore.delete(id)
  }

  /**
   * Get all stored documents
   */
  getAllDocuments(): DocumentEmbedding[] {
    return Array.from(this.documentStore.values())
  }

  /**
   * Clear all stored documents
   */
  clearDocuments(): void {
    this.documentStore.clear()
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): {
    size: number
    hitRate: number
    memoryUsage: string
  } {
    const hitCount = this.cache.size // Simplified metric
    const totalRequests = hitCount + 1 // Simplified metric
    
    // Estimate memory usage
    const memoryUsage = Array.from(this.cache.values())
      .reduce((total, embedding) => total + embedding.embedding.length * 8, 0) // 8 bytes per float64
    
    const formatBytes = (bytes: number): string => {
      if (bytes === 0) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    }

    return {
      size: this.cache.size,
      hitRate: (hitCount / totalRequests) * 100,
      memoryUsage: formatBytes(memoryUsage)
    }
  }

  /**
   * Clear embedding cache
   */
  clearCache(): void {
    this.cache.clear()
  }

  /**
   * Test model availability
   */
  async testModel(modelName: string): Promise<boolean> {
    try {
      await this.generateEmbedding({
        text: 'test',
        model: modelName
      })
      return true
    } catch (error) {
      console.error(`[Embeddings] Model ${modelName} test failed:`, error)
      return false
    }
  }

  /**
   * Get service status
   */
  getStatus(): {
    connected: boolean
    defaultModel: string
    cacheSize: number
    documentCount: number
  } {
    return {
      connected: this.isConnected,
      defaultModel: this.defaultModel,
      cacheSize: this.cache.size,
      documentCount: this.documentStore.size
    }
  }
}

// Export singleton instance
export const embeddingsService = new EmbeddingsService()
export default embeddingsService
