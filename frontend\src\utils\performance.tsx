// Performance Optimization Utilities for Frontend
// PATTERN: Code splitting and lazy loading optimizations
// Features:
// - React.memo and useMemo for expensive operations
// - Framer Motion GPU acceleration optimizations  
// - Bundle size optimization
// - Service worker for caching

import { memo, useMemo, useCallback, lazy, Suspense, useState, useEffect, useRef } from 'react';
import type { ComponentType, ReactElement, ReactNode } from 'react';

/**
 * Higher-order component for React.memo with custom comparison
 */
export function withMemoization<P extends object>(
  Component: ComponentType<P>,
  compareProps?: (prevProps: P, nextProps: P) => boolean
) {
  const MemoizedComponent = memo(Component, compareProps);
  MemoizedComponent.displayName = `Memoized(${Component.displayName || Component.name})`;
  return MemoizedComponent;
}

/**
 * Lazy loading wrapper with loading fallback
 */
export function withLazyLoading<P extends object>(
  importFunc: () => Promise<{ default: ComponentType<P> }>,
  fallbackElement?: ReactElement
): ComponentType<P> {
  const fallback = fallbackElement || <div className="animate-pulse bg-gray-200 rounded h-32" />;
  const LazyComponent = lazy(importFunc);
  
  return (props: P) => (
    <Suspense fallback={fallback}>
      <LazyComponent {...props} />
    </Suspense>
  );
}

/**
 * Framer Motion GPU acceleration utilities
 */
export const motionOptimizations = {
  // Force GPU acceleration
  gpuAccelerated: {
    transform: 'translate3d(0, 0, 0)',
    willChange: 'transform',
  },
  
  // Optimized spring configurations
  springs: {
    gentle: {
      type: "spring" as const,
      stiffness: 300,
      damping: 30,
    },
    snappy: {
      type: "spring" as const,
      stiffness: 400,
      damping: 25,
    },
    bouncy: {
      type: "spring" as const,
      stiffness: 500,
      damping: 20,
    },
  },
  
  // Transition presets for 60fps animations
  transitions: {
    smooth: { duration: 0.3, ease: [0.25, 0.46, 0.45, 0.94] },
    quick: { duration: 0.15, ease: [0.25, 0.46, 0.45, 0.94] },
    slow: { duration: 0.5, ease: [0.25, 0.46, 0.45, 0.94] },
  },
};

/**
 * Debounced value hook for performance
 */
export function useDebouncedValue<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);
  
  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);
    
    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);
  
  return debouncedValue;
}

/**
 * Throttled callback hook
 */
export function useThrottledCallback<T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T {
  const lastRun = useRef<number>(Date.now());
  
  return useCallback(
    ((...args: Parameters<T>) => {
      if (Date.now() - lastRun.current >= delay) {
        callback(...args);
        lastRun.current = Date.now();
      }
    }) as T,
    [callback, delay]
  );
}

/**
 * Virtualization helper for large lists
 */
export interface VirtualizedListProps {
  items: any[];
  itemHeight: number;
  containerHeight: number;
  renderItem: (item: any, index: number) => ReactNode;
  overscan?: number;
}

export function useVirtualizedList({
  items,
  itemHeight,
  containerHeight,
  renderItem,
  overscan = 5,
}: VirtualizedListProps) {
  const [scrollTop, setScrollTop] = useState(0);
  
  const visibleItems = useMemo(() => {
    const startIndex = Math.floor(scrollTop / itemHeight);
    const endIndex = Math.min(
      startIndex + Math.ceil(containerHeight / itemHeight) + overscan,
      items.length - 1
    );
    
    return {
      startIndex: Math.max(0, startIndex - overscan),
      endIndex,
      items: items.slice(
        Math.max(0, startIndex - overscan),
        endIndex + 1
      ),
    };
  }, [items, itemHeight, containerHeight, scrollTop, overscan]);
  
  const handleScroll = useThrottledCallback((e: Event) => {
    setScrollTop((e.target as HTMLElement).scrollTop);
  }, 16); // 60fps
  
  return {
    visibleItems,
    handleScroll,
    totalHeight: items.length * itemHeight,
    offsetY: visibleItems.startIndex * itemHeight,
  };
}

/**
 * Image lazy loading hook with intersection observer
 */
export function useLazyImage(src: string, placeholder?: string) {
  const [imageSrc, setImageSrc] = useState<string>(placeholder || '');
  const [isLoaded, setIsLoaded] = useState(false);
  const imgRef = useRef<HTMLImageElement>(null);
  
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setImageSrc(src);
          observer.unobserve(entry.target);
        }
      },
      { threshold: 0.1 }
    );
    
    if (imgRef.current) {
      observer.observe(imgRef.current);
    }
    
    return () => observer.disconnect();
  }, [src]);
  
  const handleLoad = useCallback(() => {
    setIsLoaded(true);
  }, []);
  
  return { imageSrc, isLoaded, imgRef, handleLoad };
}

/**
 * Bundle analyzer helper (development only)
 */
export const bundleAnalysis = {
  // Log component render counts
  logRender: (componentName: string) => {
    if (process.env.NODE_ENV === 'development') {
      console.log(`🔄 ${componentName} rendered at ${new Date().toISOString()}`);
    }
  },
  
  // Measure component render time
  measureRender: <P extends object>(Component: ComponentType<P>) => {
    return (props: P) => {
      const start = performance.now();
      const result = Component(props);
      const end = performance.now();
      
      if (process.env.NODE_ENV === 'development') {
        console.log(`⏱️ ${Component.name} rendered in ${(end - start).toFixed(2)}ms`);
      }
      
      return result;
    };
  },
};

/**
 * Service Worker registration for caching
 */
export function registerServiceWorker() {
  if ('serviceWorker' in navigator && process.env.NODE_ENV === 'production') {
    window.addEventListener('load', () => {
      navigator.serviceWorker.register('/sw.js')
        .then((registration) => {
          console.log('SW registered: ', registration);
        })
        .catch((registrationError) => {
          console.log('SW registration failed: ', registrationError);
        });
    });
  }
}
