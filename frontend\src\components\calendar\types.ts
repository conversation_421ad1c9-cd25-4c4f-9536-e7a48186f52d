/**
 * Calendar Types
 * Shared types for calendar components
 */

export interface CalendarEvent {
  id: string
  title: string
  description?: string
  start_time: string // ISO string
  end_time?: string // ISO string
  location?: string
  created_at: string
  ai_generated?: boolean
  category?: string
  color?: string
}

export type CalendarView = 'month' | 'week' | 'day'

export interface CalendarState {
  currentDate: Date
  view: CalendarView
  events: CalendarEvent[]
  selectedEvent?: CalendarEvent
  isCreating: boolean
  draggedEvent?: CalendarEvent
}

export interface CalendarFilters {
  showOnlyAIGenerated?: boolean
  categories?: string[]
  dateRange?: {
    start: Date
    end: Date
  }
}

export interface CreateEventData {
  title: string
  description?: string
  start_time: string
  end_time?: string
  location?: string
}

export interface CalendarDay {
  date: Date
  isCurrentMonth: boolean
  isToday: boolean
  events: CalendarEvent[]
}
