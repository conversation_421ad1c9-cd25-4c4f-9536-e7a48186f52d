"""
Background Task Processor - Async Performance Optimization
PATTERN: High-performance background task processing for AI operations
Features:
- Async task queue with priority levels
- Batch processing for embeddings and database operations
- Cache warming and cleanup tasks
- Memory optimization and garbage collection
- Performance monitoring and metrics collection
"""

import asyncio
import logging
import time
from typing import Dict, Any, List, Optional, Callable, Awaitable
from dataclasses import dataclass, field
from enum import Enum
import heapq
from datetime import datetime, timedelta
import json
from concurrent.futures import ThreadPoolExecutor

from app.config.settings import get_settings, PERFORMANCE_CONSTANTS

logger = logging.getLogger(__name__)
settings = get_settings()

class TaskPriority(Enum):
    """Task priority levels for background processing."""
    CRITICAL = 1  # Real-time AI responses
    HIGH = 2      # User-facing operations
    MEDIUM = 3    # Background processing
    LOW = 4       # Maintenance tasks

@dataclass
class BackgroundTask:
    """Represents a background task with priority and scheduling."""
    id: str
    priority: TaskPriority
    func: Callable[..., Awaitable[Any]]
    args: tuple = field(default_factory=tuple)
    kwargs: dict = field(default_factory=dict)
    scheduled_at: datetime = field(default_factory=datetime.utcnow)
    max_retries: int = 3
    retry_count: int = 0
    timeout: Optional[float] = None
    created_at: datetime = field(default_factory=datetime.utcnow)
    
    def __lt__(self, other):
        """Comparison for priority queue ordering."""
        return (self.priority.value, self.scheduled_at) < (other.priority.value, other.scheduled_at)

class BackgroundTaskProcessor:
    """High-performance background task processor with async optimization."""
    
    def __init__(self, max_workers: int = None):
        self.max_workers = max_workers or settings.task_worker_count
        
        # Task queues by priority
        self.task_queue: List[BackgroundTask] = []
        self.queue_lock = asyncio.Lock()
        
        # Worker management
        self.workers: List[asyncio.Task] = []
        self.is_running = False
        self.shutdown_event = asyncio.Event()
        
        # Performance metrics
        self.metrics = {
            "tasks_processed": 0,
            "tasks_failed": 0,
            "tasks_retried": 0,
            "avg_processing_time": 0.0,
            "queue_size": 0,
            "active_workers": 0,
            "memory_usage_mb": 0.0
        }
        
        # Task processing times for performance analysis
        self.processing_times: Dict[str, List[float]] = {}
        
        # Thread pool for CPU-bound tasks
        self.thread_pool = ThreadPoolExecutor(max_workers=2)
    
    async def start(self):
        """Start the background task processor."""
        if self.is_running:
            return
        
        self.is_running = True
        self.shutdown_event.clear()
        
        # Start worker tasks
        for i in range(self.max_workers):
            worker = asyncio.create_task(self._worker(f"worker-{i}"))
            self.workers.append(worker)
        
        # Start maintenance tasks
        asyncio.create_task(self._metrics_collector())
        asyncio.create_task(self._cache_warmer())
        asyncio.create_task(self._memory_optimizer())
        
        logger.info(f"Background task processor started with {self.max_workers} workers")
    
    async def stop(self):
        """Stop the background task processor gracefully."""
        if not self.is_running:
            return
        
        logger.info("Stopping background task processor...")
        
        self.is_running = False
        self.shutdown_event.set()
        
        # Wait for workers to finish current tasks
        if self.workers:
            await asyncio.gather(*self.workers, return_exceptions=True)
        
        # Shutdown thread pool
        self.thread_pool.shutdown(wait=True)
        
        logger.info("Background task processor stopped")
    
    async def submit_task(
        self, 
        func: Callable[..., Awaitable[Any]], 
        *args,
        priority: TaskPriority = TaskPriority.MEDIUM,
        delay: float = 0,
        max_retries: int = 3,
        timeout: Optional[float] = None,
        task_id: Optional[str] = None,
        **kwargs
    ) -> str:
        """Submit a task for background processing."""
        
        if not task_id:
            task_id = f"task_{int(time.time() * 1000)}_{len(self.task_queue)}"
        
        # Calculate scheduled time
        scheduled_at = datetime.utcnow()
        if delay > 0:
            scheduled_at += timedelta(seconds=delay)
        
        task = BackgroundTask(
            id=task_id,
            priority=priority,
            func=func,
            args=args,
            kwargs=kwargs,
            scheduled_at=scheduled_at,
            max_retries=max_retries,
            timeout=timeout
        )
        
        # Add to priority queue
        async with self.queue_lock:
            heapq.heappush(self.task_queue, task)
            self.metrics["queue_size"] = len(self.task_queue)
        
        logger.debug(f"Submitted task {task_id} with priority {priority.name}")
        return task_id
    
    async def submit_batch_processing(
        self, 
        items: List[Any], 
        batch_func: Callable[[List[Any]], Awaitable[Any]],
        batch_size: int = None,
        priority: TaskPriority = TaskPriority.MEDIUM
    ) -> List[str]:
        """Submit items for batch processing with optimal batch sizes."""
        
        if batch_size is None:
            batch_size = PERFORMANCE_CONSTANTS["EMBEDDING_BATCH_SIZE"]
        
        # Split items into batches
        batches = [items[i:i + batch_size] for i in range(0, len(items), batch_size)]
        
        task_ids = []
        for i, batch in enumerate(batches):
            task_id = await self.submit_task(
                batch_func,
                batch,
                priority=priority,
                task_id=f"batch_{int(time.time())}_{i}"
            )
            task_ids.append(task_id)
        
        return task_ids
    
    async def _worker(self, worker_id: str):
        """Worker coroutine that processes tasks from the queue."""
        logger.info(f"Worker {worker_id} started")
        
        while self.is_running:
            try:
                # Get next task from queue
                task = await self._get_next_task()
                
                if not task:
                    # No tasks available, wait a bit
                    await asyncio.sleep(0.1)
                    continue
                
                # Check if task should be executed now
                if task.scheduled_at > datetime.utcnow():
                    # Put task back and wait
                    async with self.queue_lock:
                        heapq.heappush(self.task_queue, task)
                    await asyncio.sleep(0.5)
                    continue
                
                # Process the task
                await self._process_task(task, worker_id)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Worker {worker_id} error: {e}")
                await asyncio.sleep(1)  # Prevent busy loop on persistent errors
        
        logger.info(f"Worker {worker_id} stopped")
    
    async def _get_next_task(self) -> Optional[BackgroundTask]:
        """Get the next highest priority task from the queue."""
        async with self.queue_lock:
            if self.task_queue:
                task = heapq.heappop(self.task_queue)
                self.metrics["queue_size"] = len(self.task_queue)
                return task
        return None
    
    async def _process_task(self, task: BackgroundTask, worker_id: str):
        """Process a single background task with error handling and metrics."""
        start_time = time.time()
        self.metrics["active_workers"] += 1
        
        try:
            logger.debug(f"Worker {worker_id} processing task {task.id}")
            
            # Execute task with timeout
            if task.timeout:
                result = await asyncio.wait_for(
                    task.func(*task.args, **task.kwargs),
                    timeout=task.timeout
                )
            else:
                result = await task.func(*task.args, **task.kwargs)
            
            # Update success metrics
            processing_time = time.time() - start_time
            self._update_processing_metrics(task, processing_time, success=True)
            
            logger.debug(f"Task {task.id} completed in {processing_time:.3f}s")
            
        except asyncio.TimeoutError:
            logger.warning(f"Task {task.id} timed out after {task.timeout}s")
            await self._handle_task_failure(task, "timeout")
            
        except Exception as e:
            logger.error(f"Task {task.id} failed: {e}")
            await self._handle_task_failure(task, str(e))
            
        finally:
            self.metrics["active_workers"] -= 1
    
    async def _handle_task_failure(self, task: BackgroundTask, error_message: str):
        """Handle task failure with retry logic."""
        task.retry_count += 1
        
        if task.retry_count <= task.max_retries:
            # Exponential backoff for retry
            delay = 2 ** task.retry_count  # 2, 4, 8 seconds
            task.scheduled_at = datetime.utcnow() + timedelta(seconds=delay)
            
            # Re-queue the task
            async with self.queue_lock:
                heapq.heappush(self.task_queue, task)
                self.metrics["queue_size"] = len(self.task_queue)
            
            self.metrics["tasks_retried"] += 1
            logger.info(f"Task {task.id} retrying ({task.retry_count}/{task.max_retries}) in {delay}s")
        else:
            # Max retries exceeded
            self.metrics["tasks_failed"] += 1
            logger.error(f"Task {task.id} failed permanently after {task.retry_count} retries: {error_message}")
    
    def _update_processing_metrics(self, task: BackgroundTask, processing_time: float, success: bool):
        """Update processing metrics for performance analysis."""
        if success:
            self.metrics["tasks_processed"] += 1
            
            # Update average processing time
            total_tasks = self.metrics["tasks_processed"]
            current_avg = self.metrics["avg_processing_time"]
            self.metrics["avg_processing_time"] = (
                (current_avg * (total_tasks - 1) + processing_time) / total_tasks
            )
            
            # Track per-function processing times
            func_name = task.func.__name__
            if func_name not in self.processing_times:
                self.processing_times[func_name] = []
            
            self.processing_times[func_name].append(processing_time)
            
            # Keep only last 100 times for each function
            if len(self.processing_times[func_name]) > 100:
                self.processing_times[func_name] = self.processing_times[func_name][-100:]
    
    async def _metrics_collector(self):
        """Background task to collect and log performance metrics."""
        while self.is_running:
            try:
                await asyncio.sleep(60)  # Collect metrics every minute
                
                # Update memory usage
                import psutil
                process = psutil.Process()
                self.metrics["memory_usage_mb"] = process.memory_info().rss / 1024 / 1024
                
                # Log performance summary
                logger.info(
                    f"Task processor metrics: "
                    f"Processed: {self.metrics['tasks_processed']}, "
                    f"Failed: {self.metrics['tasks_failed']}, "
                    f"Queue: {self.metrics['queue_size']}, "
                    f"Avg time: {self.metrics['avg_processing_time']:.3f}s, "
                    f"Memory: {self.metrics['memory_usage_mb']:.1f}MB"
                )
                
            except Exception as e:
                logger.error(f"Metrics collection error: {e}")
    
    async def _cache_warmer(self):
        """Background task to warm up caches with commonly used data."""
        while self.is_running:
            try:
                await asyncio.sleep(1800)  # Run every 30 minutes
                
                # This would implement cache warming logic
                # For now, just a placeholder
                logger.debug("Cache warming cycle completed")
                
            except Exception as e:
                logger.error(f"Cache warming error: {e}")
    
    async def _memory_optimizer(self):
        """Background task to optimize memory usage."""
        while self.is_running:
            try:
                await asyncio.sleep(PERFORMANCE_CONSTANTS["GC_COLLECTION_INTERVAL"])
                
                # Force garbage collection if memory usage is high
                import gc
                import psutil
                
                process = psutil.Process()
                memory_usage_mb = process.memory_info().rss / 1024 / 1024
                
                if memory_usage_mb > settings.max_memory_usage_mb:
                    collected = gc.collect()
                    logger.info(f"Memory optimization: collected {collected} objects, usage: {memory_usage_mb:.1f}MB")
                
            except Exception as e:
                logger.error(f"Memory optimization error: {e}")
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get comprehensive performance statistics."""
        # Calculate per-function average processing times
        func_avg_times = {}
        for func_name, times in self.processing_times.items():
            if times:
                func_avg_times[func_name] = {
                    "avg_time_seconds": round(sum(times) / len(times), 3),
                    "min_time_seconds": round(min(times), 3),
                    "max_time_seconds": round(max(times), 3),
                    "sample_count": len(times)
                }
        
        return {
            **self.metrics,
            "is_running": self.is_running,
            "worker_count": len(self.workers),
            "function_performance": func_avg_times,
            "queue_health": "healthy" if self.metrics["queue_size"] < 100 else "congested"
        }

# Global background task processor instance
background_processor = BackgroundTaskProcessor()
