// AIResponsePanel Component Tests - Typewriter Animation and Interaction Testing
import React from 'react';
import { render, screen, waitFor, act } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { AIResponsePanel } from '../../../frontend/src/components/ai-response/AIResponsePanel';
import { mockAIResponse } from '../setup';
import type { AIResponse } from '../../../frontend/src/types';

// Mock hooks and utilities
jest.mock('../../../frontend/src/hooks/useTypewriter', () => ({
  useTypewriter: (text: string, speed: number) => ({
    displayText: text,
    isComplete: true,
    start: jest.fn(),
    reset: jest.fn(),
  }),
}));

describe('AIResponsePanel Component', () => {
  const mockResponse: AIResponse = mockAIResponse({
    content: 'This is a test AI response with detailed information.',
    type: 'analysis',
    confidence: 0.92,
    sources: [
      {
        title: 'Source 1',
        url: 'https://example.com/source1',
        snippet: 'Relevant information from source 1',
        relevance: 0.8,
        type: 'web',
      },
      {
        title: 'Source 2',
        url: 'https://example.com/source2',
        snippet: 'Additional context from source 2',
        relevance: 0.9,
        type: 'document',
      },
    ],
  });

  const defaultProps = {
    response: mockResponse,
    isStreaming: false,
    onCopy: jest.fn(),
    onRefine: jest.fn(),
    onSourceClick: jest.fn(),
    className: '',
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders AI response panel with content', () => {
    render(<AIResponsePanel {...defaultProps} />);
    
    expect(screen.getByTestId('ai-response-panel')).toBeInTheDocument();
    expect(screen.getByText('This is a test AI response with detailed information.')).toBeInTheDocument();
  });

  it('displays response type badge', () => {
    render(<AIResponsePanel {...defaultProps} />);
    
    expect(screen.getByTestId('response-type-badge')).toBeInTheDocument();
    expect(screen.getByText('Analysis')).toBeInTheDocument();
  });

  it('shows confidence indicator with correct percentage', () => {
    render(<AIResponsePanel {...defaultProps} />);
    
    const confidenceIndicator = screen.getByTestId('confidence-indicator');
    expect(confidenceIndicator).toBeInTheDocument();
    expect(confidenceIndicator).toHaveTextContent('92%');
  });

  it('renders source links when sources are provided', () => {
    render(<AIResponsePanel {...defaultProps} />);
    
    expect(screen.getByText('Source 1')).toBeInTheDocument();
    expect(screen.getByText('Source 2')).toBeInTheDocument();
    
    const sourceLinks = screen.getAllByTestId('source-link');
    expect(sourceLinks).toHaveLength(2);
  });

  it('handles source link clicks', async () => {
    const user = userEvent.setup();
    render(<AIResponsePanel {...defaultProps} />);
    
    const sourceLink = screen.getByText('Source 1');
    await user.click(sourceLink);
    
    expect(defaultProps.onSourceClick).toHaveBeenCalledWith(mockResponse.sources[0]);
  });

  it('displays copy button and handles copy action', async () => {
    const user = userEvent.setup();
    render(<AIResponsePanel {...defaultProps} />);
    
    const copyButton = screen.getByTestId('copy-button');
    expect(copyButton).toBeInTheDocument();
    
    await user.click(copyButton);
    expect(defaultProps.onCopy).toHaveBeenCalledWith(mockResponse.content);
  });

  it('shows refine button and handles refine action', async () => {
    const user = userEvent.setup();
    render(<AIResponsePanel {...defaultProps} />);
    
    const refineButton = screen.getByTestId('refine-button');
    expect(refineButton).toBeInTheDocument();
    
    await user.click(refineButton);
    expect(defaultProps.onRefine).toHaveBeenCalledWith(mockResponse.id);
  });

  it('displays streaming indicator when response is streaming', () => {
    const streamingProps = {
      ...defaultProps,
      isStreaming: true,
    };
    
    render(<AIResponsePanel {...streamingProps} />);
    
    expect(screen.getByTestId('streaming-indicator')).toBeInTheDocument();
    expect(screen.getByText('AI is thinking...')).toBeInTheDocument();
  });

  it('shows typewriter cursor during streaming', () => {
    const streamingProps = {
      ...defaultProps,
      isStreaming: true,
    };
    
    render(<AIResponsePanel {...streamingProps} />);
    
    expect(screen.getByTestId('typewriter-cursor')).toBeInTheDocument();
  });

  it('expands and collapses source details', async () => {
    const user = userEvent.setup();
    render(<AIResponsePanel {...defaultProps} />);
    
    const expandButton = screen.getByTestId('expand-sources');
    await user.click(expandButton);
    
    await waitFor(() => {
      expect(screen.getByText('Relevant information from source 1')).toBeInTheDocument();
      expect(screen.getByText('Additional context from source 2')).toBeInTheDocument();
    });
    
    // Collapse sources
    await user.click(expandButton);
    
    await waitFor(() => {
      expect(screen.queryByText('Relevant information from source 1')).not.toBeInTheDocument();
    });
  });

  it('displays response metadata including model and processing time', () => {
    render(<AIResponsePanel {...defaultProps} />);
    
    const metadata = screen.getByTestId('response-metadata');
    expect(metadata).toBeInTheDocument();
    expect(metadata).toHaveTextContent('gpt-4');
    expect(metadata).toHaveTextContent('1.5s');
  });

  it('shows token usage information', () => {
    render(<AIResponsePanel {...defaultProps} />);
    
    const tokenUsage = screen.getByTestId('token-usage');
    expect(tokenUsage).toBeInTheDocument();
    expect(tokenUsage).toHaveTextContent('75 tokens');
  });

  it('handles different response types with appropriate styling', () => {
    const responseTypes = ['text', 'code', 'analysis', 'summary', 'creative'] as const;
    
    responseTypes.forEach(type => {
      const { unmount } = render(
        <AIResponsePanel 
          {...defaultProps} 
          response={{ ...mockResponse, type }}
        />
      );
      
      const panel = screen.getByTestId('ai-response-panel');
      expect(panel).toHaveClass(`response-type-${type}`);
      
      unmount();
    });
  });

  it('applies confidence-based styling for low confidence responses', () => {
    const lowConfidenceResponse = {
      ...mockResponse,
      confidence: 0.3,
    };
    
    render(<AIResponsePanel {...defaultProps} response={lowConfidenceResponse} />);
    
    const panel = screen.getByTestId('ai-response-panel');
    expect(panel).toHaveClass('low-confidence');
  });

  it('renders without sources when none are provided', () => {
    const responseWithoutSources = {
      ...mockResponse,
      sources: [],
    };
    
    render(<AIResponsePanel {...defaultProps} response={responseWithoutSources} />);
    
    expect(screen.queryByTestId('sources-section')).not.toBeInTheDocument();
  });

  it('supports keyboard navigation for interactive elements', async () => {
    const user = userEvent.setup();
    render(<AIResponsePanel {...defaultProps} />);
    
    // Tab through interactive elements
    await user.tab();
    expect(screen.getByTestId('copy-button')).toHaveFocus();
    
    await user.tab();
    expect(screen.getByTestId('refine-button')).toHaveFocus();
    
    await user.tab();
    expect(screen.getByText('Source 1')).toHaveFocus();
  });

  it('handles long content with proper text overflow', () => {
    const longContentResponse = {
      ...mockResponse,
      content: 'This is a very long AI response that should be handled properly with text overflow and proper line breaks. '.repeat(20),
    };
    
    render(<AIResponsePanel {...defaultProps} response={longContentResponse} />);
    
    const contentElement = screen.getByTestId('response-content');
    expect(contentElement).toHaveClass('content-overflow');
  });

  it('displays warning indicators for filtered content', () => {
    const filteredResponse = {
      ...mockResponse,
      metadata: {
        ...mockResponse.metadata,
        contentFiltered: true,
        warnings: ['Content may be sensitive'],
      },
    };
    
    render(<AIResponsePanel {...defaultProps} response={filteredResponse} />);
    
    expect(screen.getByTestId('content-warning')).toBeInTheDocument();
    expect(screen.getByText('Content may be sensitive')).toBeInTheDocument();
  });

  it('shows relevance scores for sources', () => {
    render(<AIResponsePanel {...defaultProps} />);
    
    const relevanceScores = screen.getAllByTestId('source-relevance');
    expect(relevanceScores).toHaveLength(2);
    expect(relevanceScores[0]).toHaveTextContent('80%');
    expect(relevanceScores[1]).toHaveTextContent('90%');
  });

  it('handles source type icons appropriately', () => {
    render(<AIResponsePanel {...defaultProps} />);
    
    const webSourceIcon = screen.getByTestId('source-icon-web');
    const documentSourceIcon = screen.getByTestId('source-icon-document');
    
    expect(webSourceIcon).toBeInTheDocument();
    expect(documentSourceIcon).toBeInTheDocument();
  });

  it('implements proper ARIA labels for accessibility', () => {
    render(<AIResponsePanel {...defaultProps} />);
    
    const panel = screen.getByTestId('ai-response-panel');
    expect(panel).toHaveAttribute('role', 'article');
    expect(panel).toHaveAttribute('aria-label', 'AI Response');
    
    const copyButton = screen.getByTestId('copy-button');
    expect(copyButton).toHaveAttribute('aria-label', 'Copy response to clipboard');
  });

  it('supports custom className prop', () => {
    const customProps = {
      ...defaultProps,
      className: 'custom-panel-class',
    };
    
    render(<AIResponsePanel {...customProps} />);
    
    const panel = screen.getByTestId('ai-response-panel');
    expect(panel).toHaveClass('custom-panel-class');
  });

  it('handles response animation state correctly', async () => {
    const { rerender } = render(<AIResponsePanel {...defaultProps} />);
    
    // Test entrance animation
    const panel = screen.getByTestId('ai-response-panel');
    expect(panel).toHaveClass('animate-fade-in');
    
    // Test streaming state change
    rerender(<AIResponsePanel {...defaultProps} isStreaming={true} />);
    
    await waitFor(() => {
      expect(panel).toHaveClass('streaming-active');
    });
  });

  it('displays cost information when available', () => {
    const responseWithCost = {
      ...mockResponse,
      usage: {
        ...mockResponse.usage,
        cost: 0.0025,
      },
    };
    
    render(<AIResponsePanel {...defaultProps} response={responseWithCost} />);
    
    const costInfo = screen.getByTestId('response-cost');
    expect(costInfo).toBeInTheDocument();
    expect(costInfo).toHaveTextContent('$0.0025');
  });
});
