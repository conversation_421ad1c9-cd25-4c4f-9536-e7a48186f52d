import { useState } from 'react'
import { motion } from 'framer-motion'

interface APIConfig {
  provider: 'openrouter' | 'openai' | 'custom'
  apiKey: string
  baseUrl?: string
  model: string
}

/**
 * APISettings Component
 * PATTERN: API configuration with secure key handling
 * Features:
 * - Masked API key inputs for security
 * - Provider selection (OpenRouter, OpenAI, Custom)
 * - Model selection per provider
 * - Connection testing
 * - Form validation
 */
export function APISettings() {
  const [config, setConfig] = useState<APIConfig>({
    provider: 'openrouter',
    apiKey: '',
    model: 'meta-llama/llama-3.1-8b-instruct:free'
  })
  
  const [showApiKey, setShowApiKey] = useState(false)
  const [isTestingConnection, setIsTestingConnection] = useState(false)
  const [connectionStatus, setConnectionStatus] = useState<'idle' | 'success' | 'error'>('idle')

  const providers = [
    {
      id: 'openrouter' as const,
      name: '<PERSON>Router',
      description: 'Access multiple AI models through OpenRouter',
      models: [
        'meta-llama/llama-3.1-8b-instruct:free',
        'meta-llama/llama-3.1-70b-instruct:free',
        'microsoft/wizardlm-2-8x22b:free',
        'openchat/openchat-7b:free'
      ]
    },
    {
      id: 'openai' as const,
      name: 'OpenAI',
      description: 'Direct OpenAI API integration',
      models: [
        'gpt-4o',
        'gpt-4o-mini',
        'gpt-3.5-turbo',
        'gpt-4-turbo'
      ]
    },
    {
      id: 'custom' as const,
      name: 'Custom',
      description: 'Custom API endpoint',
      models: []
    }
  ]

  const selectedProvider = providers.find(p => p.id === config.provider)

  const handleProviderChange = (provider: APIConfig['provider']) => {
    setConfig(prev => ({
      ...prev,
      provider,
      model: providers.find(p => p.id === provider)?.models[0] || ''
    }))
  }

  const handleTestConnection = async () => {
    setIsTestingConnection(true)
    setConnectionStatus('idle')

    try {
      // Simulate API test - replace with actual test call
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // Mock success/failure based on API key presence
      if (config.apiKey.length > 10) {
        setConnectionStatus('success')
      } else {
        setConnectionStatus('error')
      }
    } catch (error) {
      setConnectionStatus('error')
    } finally {
      setIsTestingConnection(false)
    }
  }

  const handleSave = () => {
    // TODO: Save to backend/storage
    console.log('Saving API configuration:', config)
  }

  const maskApiKey = (key: string) => {
    if (key.length <= 8) return key
    return key.slice(0, 4) + '•'.repeat(key.length - 8) + key.slice(-4)
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="space-y-6"
    >
      {/* Provider Selection */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">AI Provider</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {providers.map((provider) => (
            <motion.div
              key={provider.id}
              className={`
                relative border-2 rounded-lg p-4 cursor-pointer transition-all
                ${config.provider === provider.id
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200 hover:border-gray-300'
                }
              `}
              onClick={() => handleProviderChange(provider.id)}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <div className="flex items-center justify-between mb-2">
                <h4 className="font-medium text-gray-900">{provider.name}</h4>
                {config.provider === provider.id && (
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    className="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center"
                  >
                    <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  </motion.div>
                )}
              </div>
              <p className="text-sm text-gray-600">{provider.description}</p>
            </motion.div>
          ))}
        </div>
      </div>

      {/* API Configuration */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">API Configuration</h3>
        
        <div className="space-y-4">
          {/* API Key */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              API Key
            </label>
            <div className="relative">
              <input
                type={showApiKey ? 'text' : 'password'}
                value={config.apiKey}
                onChange={(e) => setConfig(prev => ({ ...prev, apiKey: e.target.value }))}
                placeholder={`Enter your ${selectedProvider?.name} API key`}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent pr-10"
              />
              <button
                type="button"
                onClick={() => setShowApiKey(!showApiKey)}
                className="absolute inset-y-0 right-0 pr-3 flex items-center"
              >
                {showApiKey ? (
                  <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                  </svg>
                ) : (
                  <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                )}
              </button>
            </div>
            {config.apiKey && (
              <p className="mt-1 text-xs text-gray-500">
                Current key: {maskApiKey(config.apiKey)}
              </p>
            )}
          </div>

          {/* Custom Base URL */}
          {config.provider === 'custom' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Base URL
              </label>
              <input
                type="url"
                value={config.baseUrl || ''}
                onChange={(e) => setConfig(prev => ({ ...prev, baseUrl: e.target.value }))}
                placeholder="https://api.example.com/v1"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          )}

          {/* Model Selection */}
          {selectedProvider && selectedProvider.models.length > 0 && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Model
              </label>
              <select
                value={config.model}
                onChange={(e) => setConfig(prev => ({ ...prev, model: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                {selectedProvider.models.map((model) => (
                  <option key={model} value={model}>
                    {model}
                  </option>
                ))}
              </select>
            </div>
          )}
        </div>
      </div>

      {/* Connection Test */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Connection Test</h3>
        
        <div className="flex items-center gap-4">
          <motion.button
            onClick={handleTestConnection}
            disabled={!config.apiKey || isTestingConnection}
            className="px-4 py-2 bg-blue-600 text-white rounded-md font-medium hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
            whileHover={{ scale: config.apiKey && !isTestingConnection ? 1.02 : 1 }}
            whileTap={{ scale: config.apiKey && !isTestingConnection ? 0.98 : 1 }}
          >
            {isTestingConnection ? (
              <div className="flex items-center gap-2">
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                  className="w-4 h-4 border-2 border-white border-t-transparent rounded-full"
                />
                Testing...
              </div>
            ) : (
              'Test Connection'
            )}
          </motion.button>

          {/* Status Indicator */}
          {connectionStatus !== 'idle' && (
            <motion.div
              initial={{ opacity: 0, scale: 0 }}
              animate={{ opacity: 1, scale: 1 }}
              className={`flex items-center gap-2 px-3 py-1 rounded-full text-sm font-medium ${
                connectionStatus === 'success'
                  ? 'bg-green-100 text-green-800'
                  : 'bg-red-100 text-red-800'
              }`}
            >
              {connectionStatus === 'success' ? (
                <>
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  Connected successfully
                </>
              ) : (
                <>
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                  Connection failed
                </>
              )}
            </motion.div>
          )}
        </div>
      </div>

      {/* Save Button */}
      <motion.div className="flex justify-end">
        <motion.button
          onClick={handleSave}
          className="px-6 py-3 bg-green-600 text-white rounded-md font-medium hover:bg-green-700 transition-colors"
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          Save API Settings
        </motion.button>
      </motion.div>
    </motion.div>
  )
}
