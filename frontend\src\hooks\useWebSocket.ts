import { useState, useRef, useCallback, useEffect } from 'react'
import { useAppStore } from '@/stores/appStore'

/**
 * WebSocket configuration
 */
const WS_CONFIG = {
  url: import.meta.env.VITE_WS_URL || 'ws://localhost:8000/ws',
  RECONNECT_DELAY: 3000,
  MAX_RECONNECT_ATTEMPTS: 5,
  HEARTBEAT_INTERVAL: 30000,
  CONNECTION_TIMEOUT: 10000,
}

/**
 * WebSocket message types
 */
export type WebSocketMessage = {
  type: 'processing_update' | 'result' | 'error' | 'heartbeat' | 'status'
  data: any
  timestamp: string
}

/**
 * WebSocket hook for real-time communication with backend
 */
export const useWebSocket = () => {
  const [isConnected, setIsConnected] = useState(false)
  const [isConnecting, setIsConnecting] = useState(false)
  const wsRef = useRef<WebSocket | null>(null)
  const reconnectAttempts = useRef(0)
  const heartbeatInterval = useRef<NodeJS.Timeout | null>(null)
  
  const { 
    setWebsocketConnected, 
    addError, 
    setProcessing,
    updateSyncTime 
  } = useAppStore()

  /**
   * Send message to WebSocket server
   */
  const sendMessage = useCallback((message: WebSocketMessage) => {
    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
      try {
        wsRef.current.send(JSON.stringify(message))
        return true
      } catch (error) {
        console.error('Failed to send WebSocket message:', error)
        addError('Failed to send message to server', 'error', 'websocket')
        return false
      }
    }
    return false
  }, [addError])

  /**
   * Handle incoming WebSocket messages
   */
  const handleMessage = useCallback((event: MessageEvent) => {
    try {
      const message: WebSocketMessage = JSON.parse(event.data)
      
      switch (message.type) {
        case 'processing_update':
          setProcessing(
            true, 
            message.data.operation, 
            message.data.progress
          )
          break
          
        case 'result':
          setProcessing(false)
          updateSyncTime()
          // Handle result data - this would typically update relevant stores
          console.log('Received result:', message.data)
          break
          
        case 'error':
          setProcessing(false)
          addError(
            message.data.message || 'Server error occurred', 
            'error', 
            message.data.context || 'websocket'
          )
          break
          
        case 'heartbeat':
          // Respond to heartbeat
          sendMessage({
            type: 'heartbeat',
            data: { timestamp: new Date().toISOString() },
            timestamp: new Date().toISOString()
          })
          break
          
        case 'status':
          updateSyncTime()
          break
          
        default:
          console.warn('Unknown WebSocket message type:', message.type)
      }
    } catch (error) {
      console.error('Failed to parse WebSocket message:', error)
      addError('Invalid message from server', 'warning', 'websocket')
    }
  }, [setProcessing, updateSyncTime, addError, sendMessage])

  /**
   * Setup heartbeat mechanism
   */
  const setupHeartbeat = useCallback(() => {
    if (heartbeatInterval.current) {
      clearInterval(heartbeatInterval.current)
    }
    
    heartbeatInterval.current = setInterval(() => {
      sendMessage({
        type: 'heartbeat',
        data: { timestamp: new Date().toISOString() },
        timestamp: new Date().toISOString()
      })
    }, WS_CONFIG.HEARTBEAT_INTERVAL)
  }, [sendMessage])

  /**
   * Connect to WebSocket server
   */
  const connect = useCallback(() => {
    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
      return // Already connected
    }

    if (isConnecting) {
      return // Already attempting to connect
    }

    setIsConnecting(true)

    try {
      wsRef.current = new WebSocket(WS_CONFIG.url)

      wsRef.current.onopen = () => {
        console.log('WebSocket connected')
        setIsConnected(true)
        setIsConnecting(false)
        setWebsocketConnected(true)
        updateSyncTime()
        reconnectAttempts.current = 0
        setupHeartbeat()
        
        // Send initial connection message
        sendMessage({
          type: 'status',
          data: { status: 'connected', clientId: crypto.randomUUID() },
          timestamp: new Date().toISOString()
        })
      }

      wsRef.current.onmessage = handleMessage

      wsRef.current.onclose = (event: CloseEvent) => {
        console.log('WebSocket disconnected:', event.code, event.reason)
        setIsConnected(false)
        setIsConnecting(false)
        setWebsocketConnected(false)
        
        if (heartbeatInterval.current) {
          clearInterval(heartbeatInterval.current)
          heartbeatInterval.current = null
        }

        // Attempt reconnection if not manually closed
        if (event.code !== 1000 && reconnectAttempts.current < WS_CONFIG.MAX_RECONNECT_ATTEMPTS) {
          setTimeout(() => {
            reconnectAttempts.current++
            console.log(`Attempting to reconnect (${reconnectAttempts.current}/${WS_CONFIG.MAX_RECONNECT_ATTEMPTS})`)
            connect()
          }, WS_CONFIG.RECONNECT_DELAY)
        } else if (reconnectAttempts.current >= WS_CONFIG.MAX_RECONNECT_ATTEMPTS) {
          addError(
            'Failed to reconnect to server after multiple attempts',
            'error',
            'websocket'
          )
        }
      }

      wsRef.current.onerror = (error: Event) => {
        console.error('WebSocket error:', error)
        setIsConnecting(false)
        addError('WebSocket connection error', 'error', 'websocket')
      }

    } catch (error) {
      console.error('Failed to create WebSocket connection:', error)
      setIsConnecting(false)
      addError('Failed to connect to server', 'error', 'websocket')
    }
  }, [isConnecting, setWebsocketConnected, updateSyncTime, setupHeartbeat, sendMessage, handleMessage, addError])

  /**
   * Disconnect from WebSocket server
   */
  const disconnect = useCallback(() => {
    if (heartbeatInterval.current) {
      clearInterval(heartbeatInterval.current)
      heartbeatInterval.current = null
    }

    if (wsRef.current) {
      wsRef.current.close(1000, 'Client disconnect')
      wsRef.current = null
    }

    setIsConnected(false)
    setIsConnecting(false)
    setWebsocketConnected(false)
    reconnectAttempts.current = 0
  }, [setWebsocketConnected])

  /**
   * Reconnect to WebSocket server
   */
  const reconnect = useCallback(() => {
    disconnect()
    setTimeout(connect, 1000)
  }, [disconnect, connect])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      disconnect()
    }
  }, [disconnect])

  return {
    isConnected,
    isConnecting,
    connect,
    disconnect,
    reconnect,
    sendMessage,
  }
}
