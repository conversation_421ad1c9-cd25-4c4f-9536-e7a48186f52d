"""
Enhanced Security Middleware for API Protection

PATTERN: Comprehensive security middleware with advanced protection
Features:
- Advanced rate limiting with burst control
- Input sanitization and validation
- Security headers injection
- Request/response monitoring
- Threat detection and blocking
"""

import re
import json
import time
import hashlib
import logging
from typing import Dict, Any, List, Optional, Set, Tuple
from collections import defaultdict, deque
from datetime import datetime, timedelta
from ipaddress import ip_address, ip_network

from fastapi import Request, Response, HTTPException, status
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import JSONResponse

from ..config.security import SecurityConfig, get_csp_header_value, mask_sensitive_data
from ..utils.validation import input_sanitizer

logger = logging.getLogger(__name__)

class SecurityMiddleware(BaseHTTPMiddleware):
    """Comprehensive security middleware with threat detection"""
    
    def __init__(self, app, environment: str = "production"):
        super().__init__(app)
        self.security_config = SecurityConfig()
        self.environment = environment
        
        # Threat detection
        self.suspicious_ips: Set[str] = set()
        self.blocked_ips: Set[str] = set()
        self.failed_attempts: Dict[str, List[datetime]] = defaultdict(list)
        
        # Request monitoring
        self.request_patterns: Dict[str, deque] = defaultdict(lambda: deque(maxlen=100))
        self.anomaly_scores: Dict[str, float] = defaultdict(float)
        
        # Compile patterns for performance
        self._compile_security_patterns()
        
        logger.info(f"Security middleware initialized for {environment}")

    def _compile_security_patterns(self):
        """Compile regex patterns for better performance"""
        self.sql_patterns = [re.compile(pattern, re.IGNORECASE) for pattern in self.security_config.SQL_INJECTION_PATTERNS]
        self.xss_patterns = [re.compile(pattern, re.IGNORECASE) for pattern in self.security_config.XSS_PATTERNS]
        self.sensitive_patterns = [re.compile(pattern) for pattern in self.security_config.SENSITIVE_PATTERNS]

    async def dispatch(self, request: Request, call_next):
        """Main security middleware dispatcher"""
        start_time = time.time()
        client_ip = self._get_client_ip(request)
        
        try:
            # 1. IP Blocking Check
            if await self._is_blocked_ip(client_ip):
                return self._create_security_response("IP blocked due to suspicious activity", 403)
            
            # 2. Request Validation
            validation_result = await self._validate_request(request)
            if not validation_result["valid"]:
                await self._log_security_event("request_validation_failed", client_ip, validation_result)
                return self._create_security_response(validation_result["message"], 400)
            
            # 3. Threat Detection
            threat_score = await self._calculate_threat_score(request, client_ip)
            if threat_score > 0.8:
                await self._handle_high_threat(client_ip, threat_score)
                return self._create_security_response("Request blocked by threat detection", 403)
            
            # 4. Process Request
            response = await call_next(request)
            
            # 5. Add Security Headers
            self._add_security_headers(response)
            
            # 6. Log Request
            processing_time = time.time() - start_time
            await self._log_request(request, response, client_ip, processing_time, threat_score)
            
            return response
            
        except Exception as e:
            logger.error(f"Security middleware error: {e}")
            await self._log_security_event("middleware_error", client_ip, {"error": str(e)})
            # Continue processing even if security checks fail
            response = await call_next(request)
            self._add_security_headers(response)
            return response

    def _get_client_ip(self, request: Request) -> str:
        """Extract client IP considering various proxy headers"""
        # Check for various proxy headers
        headers_to_check = [
            "CF-Connecting-IP",  # Cloudflare
            "X-Forwarded-For",   # Standard proxy header
            "X-Real-IP",         # Nginx
            "X-Client-IP",       # Alternative
            "X-Cluster-Client-IP"  # AWS ALB
        ]
        
        for header in headers_to_check:
            if header in request.headers:
                ip = request.headers[header].split(",")[0].strip()
                if self._is_valid_ip(ip):
                    return ip
        
        # Fallback to direct client IP
        return request.client.host if request.client else "unknown"

    def _is_valid_ip(self, ip: str) -> bool:
        """Validate IP address format"""
        try:
            ip_address(ip)
            return True
        except ValueError:
            return False

    async def _is_blocked_ip(self, ip: str) -> bool:
        """Check if IP is blocked"""
        if ip in self.blocked_ips:
            return True
        
        # Check for too many failed attempts
        if ip in self.failed_attempts:
            recent_failures = [
                attempt for attempt in self.failed_attempts[ip]
                if attempt > datetime.now() - timedelta(minutes=15)
            ]
            if len(recent_failures) > 10:  # More than 10 failures in 15 minutes
                self.blocked_ips.add(ip)
                await self._log_security_event("ip_auto_blocked", ip, {"failure_count": len(recent_failures)})
                return True
        
        return False

    async def _validate_request(self, request: Request) -> Dict[str, Any]:
        """Comprehensive request validation"""
        validation_errors = []
        
        # 1. URL Length Check
        if len(str(request.url)) > self.security_config.INPUT_VALIDATION["max_url_length"]:
            validation_errors.append("URL too long")
        
        # 2. Method Validation
        allowed_methods = {"GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS", "HEAD"}
        if request.method not in allowed_methods:
            validation_errors.append(f"Method {request.method} not allowed")
        
        # 3. Header Validation
        header_validation = await self._validate_headers(request.headers)
        if not header_validation["valid"]:
            validation_errors.extend(header_validation["errors"])
        
        # 4. Body Validation (for applicable methods)
        if request.method in {"POST", "PUT", "PATCH"}:
            body_validation = await self._validate_request_body(request)
            if not body_validation["valid"]:
                validation_errors.extend(body_validation["errors"])
        
        return {
            "valid": len(validation_errors) == 0,
            "errors": validation_errors,
            "message": "; ".join(validation_errors) if validation_errors else "Valid"
        }

    async def _validate_headers(self, headers) -> Dict[str, Any]:
        """Validate request headers for security issues"""
        errors = []
        
        # Headers that should not be checked for SQL injection (standard HTTP headers)
        safe_headers = {
            "accept", "accept-encoding", "accept-language", "user-agent", 
            "host", "connection", "content-type", "content-length",
            "cache-control", "pragma", "sec-fetch-dest", "sec-fetch-mode",
            "sec-fetch-site", "upgrade-insecure-requests"
        }
        
        # Check for suspicious headers
        suspicious_headers = {
            "X-Forwarded-Host", "X-Forwarded-Server", "X-ProxyUser-Ip"
        }
        
        for header_name, header_value in headers.items():
            # Skip SQL injection check for standard HTTP headers
            if header_name.lower() not in safe_headers:
                # Check for SQL injection in headers
                if any(pattern.search(header_value) for pattern in self.sql_patterns):
                    errors.append(f"SQL injection detected in header {header_name}")
            
            # Check for XSS in headers (but not in Accept header as it can contain *)
            if header_name.lower() not in {"accept", "accept-encoding"}:
                if any(pattern.search(header_value) for pattern in self.xss_patterns):
                    errors.append(f"XSS attempt detected in header {header_name}")
            
            # Check header length
            if len(header_value) > 8192:  # 8KB limit per header
                errors.append(f"Header {header_name} too long")
        
        return {
            "valid": len(errors) == 0,
            "errors": errors
        }

    async def _validate_request_body(self, request: Request) -> Dict[str, Any]:
        """Validate request body for security threats"""
        errors = []
        
        try:
            # Get request body
            body = await request.body()
            
            if len(body) > 10 * 1024 * 1024:  # 10MB limit
                errors.append("Request body too large")
                return {"valid": False, "errors": errors}
            
            # Try to parse as text for validation
            try:
                body_text = body.decode('utf-8')
            except UnicodeDecodeError:
                # Binary data - skip text-based validation
                return {"valid": True, "errors": []}
            
            # Check for SQL injection
            if any(pattern.search(body_text) for pattern in self.sql_patterns):
                errors.append("SQL injection detected in request body")
            
            # Check for XSS
            if any(pattern.search(body_text) for pattern in self.xss_patterns):
                errors.append("XSS attempt detected in request body")
            
            # Check for sensitive data exposure
            sensitive_matches = []
            for pattern in self.sensitive_patterns:
                matches = pattern.findall(body_text)
                if matches:
                    sensitive_matches.extend(matches)
            
            if sensitive_matches:
                await self._log_security_event("sensitive_data_in_request", 
                                               self._get_client_ip(request), 
                                               {"matches": len(sensitive_matches)})
        
        except Exception as e:
            logger.error(f"Body validation error: {e}")
            # Don't block on validation errors
        
        return {
            "valid": len(errors) == 0,
            "errors": errors
        }

    async def _calculate_threat_score(self, request: Request, client_ip: str) -> float:
        """Calculate threat score based on request characteristics"""
        score = 0.0
        
        # 1. IP reputation
        if client_ip in self.suspicious_ips:
            score += 0.3
        
        # 2. Request rate analysis
        current_time = time.time()
        self.request_patterns[client_ip].append(current_time)
        
        # Count recent requests (last 60 seconds)
        recent_requests = [
            t for t in self.request_patterns[client_ip] 
            if current_time - t < 60
        ]
        
        if len(recent_requests) > 100:  # More than 100 requests per minute
            score += 0.4
        elif len(recent_requests) > 60:
            score += 0.2
        
        # 3. User agent analysis
        user_agent = request.headers.get("User-Agent", "")
        if not user_agent or len(user_agent) < 10:
            score += 0.2
        elif "bot" in user_agent.lower() and "googlebot" not in user_agent.lower():
            score += 0.1
        
        # 4. Request path analysis
        path = request.url.path
        suspicious_paths = ["/admin", "/wp-admin", "/.env", "/config", "/api/v1/admin"]
        if any(susp_path in path for susp_path in suspicious_paths):
            score += 0.3
        
        # 5. Method and content type mismatch
        content_type = request.headers.get("Content-Type", "")
        if request.method == "POST" and not content_type:
            score += 0.1
        
        return min(score, 1.0)  # Cap at 1.0

    async def _handle_high_threat(self, client_ip: str, threat_score: float):
        """Handle high threat score requests"""
        self.suspicious_ips.add(client_ip)
        
        # Auto-block if threat score is very high
        if threat_score > 0.9:
            self.blocked_ips.add(client_ip)
        
        await self._log_security_event("high_threat_detected", client_ip, {
            "threat_score": threat_score,
            "action": "blocked" if threat_score > 0.9 else "monitored"
        })

    def _add_security_headers(self, response: Response):
        """Add security headers to response"""
        # Add configured security headers
        for header_name, header_value in self.security_config.SECURITY_HEADERS.items():
            response.headers[header_name] = header_value
        
        # Add Content Security Policy
        csp_header = get_csp_header_value(self.environment)
        response.headers["Content-Security-Policy"] = csp_header
        
        # Add custom headers
        response.headers["X-Security-Level"] = self.environment
        response.headers["X-Request-ID"] = self._generate_request_id()
    
    def _generate_request_id(self) -> str:
        """Generate unique request ID for tracking"""
        return hashlib.md5(f"{time.time()}{hash(time.time())}".encode()).hexdigest()[:16]

    def _create_security_response(self, message: str, status_code: int) -> JSONResponse:
        """Create standardized security response"""
        return JSONResponse(
            content={
                "error": True,
                "message": message,
                "category": "security",
                "severity": "high",
                "timestamp": datetime.utcnow().isoformat(),
                "request_id": self._generate_request_id()
            },
            status_code=status_code
        )

    async def _log_security_event(self, event_type: str, client_ip: str, details: Dict[str, Any]):
        """Log security events for monitoring"""
        event = {
            "timestamp": datetime.utcnow().isoformat(),
            "event_type": event_type,
            "client_ip": client_ip,
            "details": details,
            "environment": self.environment
        }
        
        # Mask sensitive data before logging
        masked_details = mask_sensitive_data(json.dumps(details))
        
        logger.warning(f"Security Event: {event_type} from {client_ip} - {masked_details}")
        
        # TODO: Send to external security monitoring system
        # await send_to_security_monitoring(event)

    async def _log_request(self, request: Request, response: Response, 
                          client_ip: str, processing_time: float, threat_score: float):
        """Log request for monitoring and analysis"""
        if self.environment == "production":
            # Only log essential info in production
            log_data = {
                "method": request.method,
                "path": request.url.path,
                "status": response.status_code,
                "processing_time": processing_time,
                "threat_score": threat_score,
                "client_ip": client_ip
            }
        else:
            # More detailed logging in development
            log_data = {
                "method": request.method,
                "url": str(request.url),
                "headers": dict(request.headers),
                "status": response.status_code,
                "processing_time": processing_time,
                "threat_score": threat_score,
                "client_ip": client_ip
            }
        
        # Log with appropriate level based on status code and threat score
        if response.status_code >= 500 or threat_score > 0.5:
            logger.warning(f"High priority request: {json.dumps(log_data)}")
        else:
            logger.info(f"Request: {request.method} {request.url.path} - {response.status_code}")

def setup_security_middleware(app, environment: str = "production"):
    """Setup comprehensive security middleware"""
    # Add security middleware
    app.add_middleware(SecurityMiddleware, environment=environment)
    
    # Add security state to app for health checks
    app.state.security_middleware = True
    app.state.security_environment = environment
    
    logger.info(f"Security middleware configured for {environment} environment")
