import { useAIOrchestrator } from './useAIOrchestrator'

/**
 * WebSocket hook that wraps the AI Orchestrator's WebSocket functionality
 * for simpler usage in App.tsx
 */
export const useWebSocket = () => {
  const orchestrator = useAIOrchestrator()
  
  return {
    connect: orchestrator.connect,
    disconnect: orchestrator.disconnect,
    isConnected: orchestrator.isConnected,
    isConnecting: orchestrator.isConnecting,
    isReconnecting: orchestrator.isReconnecting,
    hasError: orchestrator.hasError,
    connectionState: orchestrator.connectionState,
    lastMessage: orchestrator.lastMessage,
  }
}
