/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        // Modern black/grey/white theme
        primary: {
          50: '#f8f9fa',
          100: '#f1f3f4',
          200: '#e8eaed',
          300: '#dadce0',
          400: '#bdc1c6',
          500: '#9aa0a6',
          600: '#80868b',
          700: '#5f6368',
          800: '#3c4043',
          900: '#202124',
          950: '#131416',
        },
        background: {
          primary: '#131416',
          secondary: '#1f1f23',
          tertiary: '#2a2a2e',
          hover: '#35353a',
          active: '#42424a',
        },
        text: {
          primary: '#ffffff',
          secondary: '#b3b3b3',
          muted: '#7a7a7a',
          disabled: '#4a4a4a',
        },
        accent: {
          blue: '#4285f4',
          green: '#34a853',
          yellow: '#fbbc05',
          red: '#ea4335',
          purple: '#9c27b0',
          pink: '#e91e63',
        },
        border: {
          primary: '#35353a',
          secondary: '#2a2a2e',
          focus: '#4285f4',
          error: '#ea4335',
        }
      },
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
        mono: ['JetBrains Mono', 'Monaco', 'Consolas', 'monospace'],
      },
      animation: {
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'slide-up': 'slideUp 0.3s ease-out',
        'slide-down': 'slideDown 0.3s ease-out',
        'scale-in': 'scaleIn 0.2s ease-out',
        'bounce-subtle': 'bounceSubtle 0.6s ease-in-out',
        'pulse-soft': 'pulseSoft 2s ease-in-out infinite',
        'shimmer': 'shimmer 2s linear infinite',
        'spin-slow': 'spin 3s linear infinite',
        'wiggle': 'wiggle 1s ease-in-out infinite',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { transform: 'translateY(10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        slideDown: {
          '0%': { transform: 'translateY(-10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        scaleIn: {
          '0%': { transform: 'scale(0.95)', opacity: '0' },
          '100%': { transform: 'scale(1)', opacity: '1' },
        },
        bounceSubtle: {
          '0%, 20%, 53%, 80%, 100%': { transform: 'translate3d(0,0,0)' },
          '40%, 43%': { transform: 'translate3d(0, -8px, 0)' },
          '70%': { transform: 'translate3d(0, -4px, 0)' },
          '90%': { transform: 'translate3d(0, -2px, 0)' },
        },
        pulseSoft: {
          '0%, 100%': { opacity: '1' },
          '50%': { opacity: '0.8' },
        },
        shimmer: {
          '0%': { transform: 'translateX(-100%)' },
          '100%': { transform: 'translateX(100%)' },
        },
        wiggle: {
          '0%, 100%': { transform: 'rotate(-3deg)' },
          '50%': { transform: 'rotate(3deg)' },
        },
      },
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
      },
      borderRadius: {
        'xl': '0.75rem',
        '2xl': '1rem',
        '3xl': '1.5rem',
      },
      boxShadow: {
        'soft': '0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04)',
        'medium': '0 4px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
        'large': '0 10px 50px -12px rgba(0, 0, 0, 0.25)',
        'inner-soft': 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.06)',
        'glow': '0 0 20px rgba(66, 133, 244, 0.15)',
        'glow-lg': '0 0 40px rgba(66, 133, 244, 0.15)',
        'calendar': '0 4px 20px -2px rgba(0, 0, 0, 0.08), 0 2px 8px -2px rgba(0, 0, 0, 0.04)',
        'panel': '0 8px 30px -6px rgba(0, 0, 0, 0.12), 0 4px 16px -4px rgba(0, 0, 0, 0.05)',
        'floating': '0 20px 60px -12px rgba(0, 0, 0, 0.25), 0 8px 30px -8px rgba(0, 0, 0, 0.12)',
      },
      backdropBlur: {
        'xs': '2px',
      },
      transitionTimingFunction: {
        'spring': 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
        'smooth': 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',
      },
      willChange: {
        'transform-opacity': 'transform, opacity',
        'auto': 'auto',
      },
    },
  },
  plugins: [
    // Component-specific utility classes
    function({ addUtilities }) {
      const newUtilities = {
        // Calendar component utilities
        '.calendar-cell': {
          '@apply relative w-10 h-10 flex items-center justify-center rounded-lg transition-all duration-200 hover:bg-gray-100 dark:hover:bg-gray-800': {},
        },
        '.calendar-event': {
          '@apply absolute top-0 left-0 right-0 bg-blue-500 text-white text-xs rounded px-1 py-0.5 truncate': {},
        },
        '.calendar-today': {
          '@apply bg-blue-500 text-white font-semibold': {},
        },
        
        // AI Response Panel utilities
        '.ai-response-container': {
          '@apply bg-white dark:bg-gray-900 rounded-xl shadow-panel border border-gray-200 dark:border-gray-700 overflow-hidden': {},
        },
        '.typewriter-cursor': {
          '@apply inline-block w-0.5 h-5 bg-blue-500 animate-pulse': {},
        },
        '.source-link': {
          '@apply inline-flex items-center text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 transition-colors': {},
        },
        
        // Settings component utilities
        '.settings-tab': {
          '@apply px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200': {},
        },
        '.settings-tab-active': {
          '@apply bg-blue-500 text-white shadow-medium': {},
        },
        '.settings-tab-inactive': {
          '@apply text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-800': {},
        },
        
        // Form utilities
        '.form-input': {
          '@apply w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all': {},
        },
        '.form-label': {
          '@apply block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1': {},
        },
        
        // Animation utilities for Framer Motion
        '.motion-safe': {
          '@apply transform-gpu will-change-transform-opacity': {},
        },
        '.motion-reduce': {
          '@apply transition-none': {},
        },
        
        // Loading states
        '.loading-skeleton': {
          '@apply animate-pulse bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 dark:from-gray-700 dark:via-gray-600 dark:to-gray-700 bg-[length:200%_100%] animate-shimmer': {},
        },
        
        // Glass morphism effects
        '.glass': {
          '@apply bg-white/80 dark:bg-gray-900/80 backdrop-blur-md border border-white/20 dark:border-gray-700/20': {},
        },
        '.glass-panel': {
          '@apply glass shadow-large rounded-xl': {},
        },
        
        // Interactive states
        '.interactive': {
          '@apply cursor-pointer select-none transition-all duration-200 active:scale-95 hover:scale-105': {},
        },
        '.interactive-subtle': {
          '@apply cursor-pointer transition-all duration-200 hover:bg-gray-50 dark:hover:bg-gray-800/50': {},
        },
      }
      
      addUtilities(newUtilities)
    }
  ],
}
