2025-07-21 19:44:20,767 - __main__ - INFO - Setting up test environment...
2025-07-21 19:44:21,615 - httpx - DEBUG - load_ssl_context verify=True cert=None trust_env=True http2=False
2025-07-21 19:44:21,633 - httpx - DEBUG - load_verify_locations cafile='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\certifi\\cacert.pem'
2025-07-21 19:44:22,160 - httpcore.connection - DEBUG - connect_tcp.started host='localhost' port=8000 local_address=None timeout=5.0 socket_options=None
2025-07-21 19:44:24,443 - httpcore.connection - DEBUG - connect_tcp.failed exception=ConnectError(OSError('All connection attempts failed'))
2025-07-21 19:44:24,451 - __main__ - ERROR - Backend check failed: All connection attempts failed
2025-07-21 19:44:24,452 - httpx - DEBUG - load_ssl_context verify=True cert=None trust_env=True http2=False
2025-07-21 19:44:24,453 - httpx - DEBUG - load_verify_locations cafile='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\certifi\\cacert.pem'
2025-07-21 19:44:24,755 - httpcore.connection - DEBUG - connect_tcp.started host='localhost' port=3000 local_address=None timeout=5.0 socket_options=None
2025-07-21 19:44:27,033 - httpcore.connection - DEBUG - connect_tcp.failed exception=ConnectError(OSError('All connection attempts failed'))
2025-07-21 19:44:27,033 - __main__ - ERROR - Frontend check failed: All connection attempts failed
2025-07-21 19:44:27,034 - __main__ - INFO - Environment setup completed
2025-07-21 19:44:27,034 - __main__ - INFO - Starting comprehensive integration testing...
2025-07-21 19:44:27,034 - __main__ - INFO - Running test suites: comprehensive, security
2025-07-21 19:44:27,035 - __main__ - INFO - Running test suite: comprehensive
2025-07-21 19:44:27,035 - __main__ - INFO - Description: Complete user workflow and performance testing
2025-07-21 19:44:27,204 - __main__ - ERROR - Test suite comprehensive failed: No module named 'playwright'
2025-07-21 19:44:27,206 - __main__ - INFO - Suite comprehensive completed - Success: False, Score: 0.0
2025-07-21 19:44:27,206 - __main__ - INFO - Running test suite: security
2025-07-21 19:44:27,206 - __main__ - INFO - Description: Security vulnerability and protection testing
2025-07-21 19:44:27,805 - __main__ - ERROR - Test suite security failed: No module named 'app'
2025-07-21 19:44:27,807 - __main__ - INFO - Suite security completed - Success: False, Score: 0.0
2025-07-21 19:44:27,809 - __main__ - ERROR - Test runner failed: ' font-family'
