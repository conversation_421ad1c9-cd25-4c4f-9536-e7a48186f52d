import { useState } from 'react'
import { motion } from 'framer-motion'

/**
 * Task filter options
 */
export interface TaskFilters {
  category?: string
  priority?: string
  completed?: boolean
  search?: string
}

interface TaskFiltersProps {
  filters: TaskFilters
  onFiltersChange: (filters: TaskFilters) => void
  categories: string[]
  taskCount: number
  totalCount: number
}

/**
 * TaskFilters Component
 * PATTERN: Filter functionality with smooth animations
 * Features:
 * - Search input with real-time filtering
 * - Category, priority, and completion status filters
 * - Clear filters functionality
 * - Animated filter badges
 */
export function TaskFiltersComponent({ 
  filters, 
  onFiltersChange, 
  categories, 
  taskCount, 
  totalCount 
}: TaskFiltersProps) {
  const [isExpanded, setIsExpanded] = useState(false)

  // Clear all filters
  const clearFilters = () => {
    onFiltersChange({})
  }

  // Count active filters
  const activeFiltersCount = Object.values(filters).filter(Boolean).length

  return (
    <motion.div
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-gray-800 border border-gray-700 rounded-lg p-4 mb-6"
    >
      {/* Filter Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-3">
          <h3 className="text-lg font-medium text-white">Filters</h3>
          {activeFiltersCount > 0 && (
            <motion.span
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              className="bg-blue-600 text-white text-xs px-2 py-1 rounded-full"
            >
              {activeFiltersCount}
            </motion.span>
          )}
        </div>
        
        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-400">
            {taskCount} of {totalCount} tasks
          </span>
          
          {activeFiltersCount > 0 && (
            <motion.button
              onClick={clearFilters}
              whileTap={{ scale: 0.95 }}
              className="text-xs px-3 py-1 bg-gray-700 hover:bg-gray-600 text-gray-300 rounded"
            >
              Clear All
            </motion.button>
          )}
          
          <motion.button
            onClick={() => setIsExpanded(!isExpanded)}
            whileTap={{ scale: 0.95 }}
            className="p-1 text-gray-400 hover:text-white"
          >
            <motion.div
              animate={{ rotate: isExpanded ? 180 : 0 }}
              transition={{ duration: 0.2 }}
            >
              ▼
            </motion.div>
          </motion.button>
        </div>
      </div>

      {/* Active Filter Badges */}
      {activeFiltersCount > 0 && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          className="flex flex-wrap gap-2 mb-4"
        >
          {filters.search && (
            <motion.span
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              className="bg-blue-600 text-white text-xs px-2 py-1 rounded-full flex items-center gap-1"
            >
              Search: "{filters.search}"
              <button 
                onClick={() => onFiltersChange({ ...filters, search: undefined })}
                className="ml-1 hover:text-red-300"
              >
                ×
              </button>
            </motion.span>
          )}
          
          {filters.category && (
            <motion.span
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              className="bg-green-600 text-white text-xs px-2 py-1 rounded-full flex items-center gap-1"
            >
              Category: {filters.category}
              <button 
                onClick={() => onFiltersChange({ ...filters, category: undefined })}
                className="ml-1 hover:text-red-300"
              >
                ×
              </button>
            </motion.span>
          )}
          
          {filters.priority && (
            <motion.span
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              className="bg-orange-600 text-white text-xs px-2 py-1 rounded-full flex items-center gap-1"
            >
              Priority: {filters.priority}
              <button 
                onClick={() => onFiltersChange({ ...filters, priority: undefined })}
                className="ml-1 hover:text-red-300"
              >
                ×
              </button>
            </motion.span>
          )}
          
          {filters.completed !== undefined && (
            <motion.span
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              className="bg-purple-600 text-white text-xs px-2 py-1 rounded-full flex items-center gap-1"
            >
              Status: {filters.completed ? 'Completed' : 'Active'}
              <button 
                onClick={() => onFiltersChange({ ...filters, completed: undefined })}
                className="ml-1 hover:text-red-300"
              >
                ×
              </button>
            </motion.span>
          )}
        </motion.div>
      )}

      {/* Expanded Filter Controls */}
      <motion.div
        initial={{ height: isExpanded ? 'auto' : 0, opacity: isExpanded ? 1 : 0 }}
        animate={{ height: isExpanded ? 'auto' : 0, opacity: isExpanded ? 1 : 0 }}
        transition={{ duration: 0.3 }}
        className="overflow-hidden"
      >
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Search Input */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Search
            </label>
            <input
              type="text"
              placeholder="Search tasks..."
              value={filters.search || ''}
              onChange={(e) => onFiltersChange({ ...filters, search: e.target.value || undefined })}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white placeholder-gray-400 focus:border-blue-500"
            />
          </div>
          
          {/* Category Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Category
            </label>
            <select
              value={filters.category || ''}
              onChange={(e) => onFiltersChange({ ...filters, category: e.target.value || undefined })}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white focus:border-blue-500"
            >
              <option value="">All Categories</option>
              {categories.map(cat => (
                <option key={cat} value={cat}>{cat}</option>
              ))}
            </select>
          </div>
          
          {/* Priority Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Priority
            </label>
            <select
              value={filters.priority || ''}
              onChange={(e) => onFiltersChange({ ...filters, priority: e.target.value || undefined })}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white focus:border-blue-500"
            >
              <option value="">All Priorities</option>
              <option value="urgent">🔴 Urgent</option>
              <option value="high">🟠 High</option>
              <option value="medium">🟡 Medium</option>
              <option value="low">🟢 Low</option>
            </select>
          </div>
          
          {/* Status Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Status
            </label>
            <select
              value={filters.completed === undefined ? '' : filters.completed.toString()}
              onChange={(e) => {
                const value = e.target.value
                onFiltersChange({ 
                  ...filters, 
                  completed: value === '' ? undefined : value === 'true'
                })
              }}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white focus:border-blue-500"
            >
              <option value="">All Tasks</option>
              <option value="false">⚡ Active</option>
              <option value="true">✅ Completed</option>
            </select>
          </div>
        </div>
      </motion.div>
    </motion.div>
  )
}
