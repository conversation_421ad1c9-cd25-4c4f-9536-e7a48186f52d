"""
Performance Middleware - Backend Performance Optimization
PATTERN: Async middleware for monitoring and optimization
Features:
- Request timing and metrics collection
- Memory usage monitoring
- Database query optimization
- Cache hit/miss tracking
- Background task performance monitoring
"""

import time
import asyncio
import logging
import psutil
from typing import Dict, Any
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from datetime import datetime, timedelta
import json

from app.config.settings import get_settings, PERFORMANCE_CONSTANTS

logger = logging.getLogger(__name__)
settings = get_settings()

class PerformanceMiddleware(BaseHTTPMiddleware):
    """Middleware for comprehensive performance monitoring and optimization."""
    
    def __init__(self, app):
        super().__init__(app)
        self.metrics = {
            "requests_total": 0,
            "requests_active": 0,
            "response_times": [],
            "memory_usage": [],
            "cache_stats": {"hits": 0, "misses": 0},
            "db_queries": {"total": 0, "avg_time": 0.0},
            "websocket_connections": 0,
            "background_tasks": 0
        }
        self.last_cleanup = datetime.utcnow()
        
    async def dispatch(self, request: Request, call_next):
        """Process request with performance monitoring and optimization."""
        start_time = time.time()
        
        # Track active requests
        self.metrics["requests_active"] += 1
        self.metrics["requests_total"] += 1
        
        # Check if performance cleanup is needed
        await self._check_performance_cleanup()
        
        try:
            # Add performance headers to request
            request.state.start_time = start_time
            request.state.request_id = f"req_{int(start_time * 1000)}"
            
            # Process the request
            response = await call_next(request)
            
            # Calculate response time
            response_time = time.time() - start_time
            
            # Update metrics
            await self._update_metrics(response_time, request, response)
            
            # Add performance headers to response
            response.headers["X-Response-Time"] = str(round(response_time * 1000, 2))
            response.headers["X-Request-ID"] = request.state.request_id
            response.headers["X-Memory-Usage"] = str(await self._get_memory_usage())
            
            return response
            
        except Exception as e:
            logger.error(f"Performance middleware error: {e}")
            raise
        finally:
            self.metrics["requests_active"] -= 1
    
    async def _update_metrics(self, response_time: float, request: Request, response: Response):
        """Update performance metrics with request data."""
        # Track response times (keep last 100 for rolling average)
        self.metrics["response_times"].append(response_time)
        if len(self.metrics["response_times"]) > 100:
            self.metrics["response_times"] = self.metrics["response_times"][-100:]
        
        # Track memory usage every 10 requests
        if self.metrics["requests_total"] % 10 == 0:
            memory_usage = await self._get_memory_usage()
            self.metrics["memory_usage"].append(memory_usage)
            if len(self.metrics["memory_usage"]) > 50:
                self.metrics["memory_usage"] = self.metrics["memory_usage"][-50:]
        
        # Log slow requests (>5 seconds)
        if response_time > 5.0:
            logger.warning(f"Slow request detected: {request.url.path} took {response_time:.2f}s")
    
    async def _check_performance_cleanup(self):
        """Check if performance cleanup is needed based on thresholds."""
        current_time = datetime.utcnow()
        
        # Cleanup every 5 minutes
        if (current_time - self.last_cleanup).total_seconds() > 300:
            await self._performance_cleanup()
            self.last_cleanup = current_time
    
    async def _performance_cleanup(self):
        """Perform performance cleanup and optimization."""
        try:
            # Memory cleanup if usage is high
            memory_usage = await self._get_memory_usage()
            if memory_usage > settings.max_memory_usage_mb:
                logger.warning(f"High memory usage detected: {memory_usage}MB")
                
                # Trigger garbage collection
                import gc
                collected = gc.collect()
                logger.info(f"Garbage collection freed {collected} objects")
            
            # Log performance metrics
            avg_response_time = (
                sum(self.metrics["response_times"]) / len(self.metrics["response_times"])
                if self.metrics["response_times"] else 0
            )
            
            logger.info(f"Performance metrics: "
                       f"Requests: {self.metrics['requests_total']}, "
                       f"Active: {self.metrics['requests_active']}, "
                       f"Avg Response Time: {avg_response_time:.3f}s, "
                       f"Memory: {memory_usage}MB")
            
        except Exception as e:
            logger.error(f"Performance cleanup error: {e}")
    
    async def _get_memory_usage(self) -> float:
        """Get current memory usage in MB."""
        try:
            process = psutil.Process()
            return process.memory_info().rss / 1024 / 1024  # Convert to MB
        except Exception:
            return 0.0
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get current performance metrics."""
        avg_response_time = (
            sum(self.metrics["response_times"]) / len(self.metrics["response_times"])
            if self.metrics["response_times"] else 0
        )
        
        avg_memory = (
            sum(self.metrics["memory_usage"]) / len(self.metrics["memory_usage"])
            if self.metrics["memory_usage"] else 0
        )
        
        return {
            "requests_total": self.metrics["requests_total"],
            "requests_active": self.metrics["requests_active"],
            "avg_response_time_ms": round(avg_response_time * 1000, 2),
            "avg_memory_usage_mb": round(avg_memory, 2),
            "cache_hit_ratio": (
                self.metrics["cache_stats"]["hits"] / 
                (self.metrics["cache_stats"]["hits"] + self.metrics["cache_stats"]["misses"])
                if (self.metrics["cache_stats"]["hits"] + self.metrics["cache_stats"]["misses"]) > 0
                else 0
            ),
            "websocket_connections": self.metrics["websocket_connections"],
            "background_tasks": self.metrics["background_tasks"]
        }


class AsyncPerformanceOptimizer:
    """Async performance optimization utilities for AI processing."""
    
    def __init__(self):
        self.task_queue = asyncio.Queue(maxsize=PERFORMANCE_CONSTANTS["MAX_CONCURRENT_TASKS"])
        self.processing_times = {}
        self.cache_stats = {"hits": 0, "misses": 0}
    
    async def optimize_llm_call(self, func, *args, **kwargs):
        """Optimize LLM API calls with caching and retry logic."""
        cache_key = self._generate_cache_key(func.__name__, args, kwargs)
        
        # Check cache first
        cached_result = await self._get_from_cache(cache_key)
        if cached_result is not None:
            self.cache_stats["hits"] += 1
            return cached_result
        
        self.cache_stats["misses"] += 1
        
        # Execute with retry logic and performance monitoring
        start_time = time.time()
        
        for attempt in range(settings.llm_max_retries):
            try:
                result = await func(*args, **kwargs)
                
                # Cache successful result
                await self._set_cache(cache_key, result)
                
                # Track performance
                execution_time = time.time() - start_time
                self.processing_times[func.__name__] = execution_time
                
                return result
                
            except Exception as e:
                if attempt == settings.llm_max_retries - 1:
                    logger.error(f"LLM call failed after {settings.llm_max_retries} attempts: {e}")
                    raise
                
                # Exponential backoff
                await asyncio.sleep(PERFORMANCE_CONSTANTS["LLM_RETRY_BACKOFF"][attempt])
        
        raise Exception("LLM call failed after all retries")
    
    async def optimize_embedding_batch(self, texts: list, batch_size: int = None):
        """Optimize embedding generation with batching and concurrency."""
        if batch_size is None:
            batch_size = PERFORMANCE_CONSTANTS["EMBEDDING_BATCH_SIZE"]
        
        # Split into batches for optimal performance
        batches = [texts[i:i + batch_size] for i in range(0, len(texts), batch_size)]
        
        # Process batches concurrently with controlled concurrency
        semaphore = asyncio.Semaphore(PERFORMANCE_CONSTANTS["LLM_CONCURRENT_REQUESTS"])
        
        async def process_batch(batch):
            async with semaphore:
                # This would call the actual embedding service
                await asyncio.sleep(0.1)  # Placeholder for embedding generation
                return {"batch": batch, "embeddings": []}  # Placeholder result
        
        results = await asyncio.gather(*[process_batch(batch) for batch in batches])
        
        # Combine results
        all_embeddings = []
        for result in results:
            all_embeddings.extend(result.get("embeddings", []))
        
        return all_embeddings
    
    async def optimize_database_query(self, query_func, *args, **kwargs):
        """Optimize database queries with connection pooling and caching."""
        start_time = time.time()
        
        try:
            # Use connection pool for optimal performance
            result = await query_func(*args, **kwargs)
            
            # Track query performance
            execution_time = time.time() - start_time
            if execution_time > 1.0:  # Log slow queries
                logger.warning(f"Slow database query: {query_func.__name__} took {execution_time:.2f}s")
            
            return result
            
        except Exception as e:
            logger.error(f"Database query error in {query_func.__name__}: {e}")
            raise
    
    def _generate_cache_key(self, func_name: str, args: tuple, kwargs: dict) -> str:
        """Generate cache key for function call."""
        import hashlib
        
        # Create deterministic hash from function name and arguments
        content = json.dumps({
            "func": func_name,
            "args": str(args),
            "kwargs": sorted(kwargs.items()) if kwargs else {}
        }, sort_keys=True, default=str)
        
        return hashlib.md5(content.encode()).hexdigest()
    
    async def _get_from_cache(self, key: str):
        """Get value from cache (placeholder - implement with Redis in production)."""
        # Placeholder for cache implementation
        return None
    
    async def _set_cache(self, key: str, value: Any):
        """Set value in cache (placeholder - implement with Redis in production)."""
        # Placeholder for cache implementation
        pass
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics."""
        avg_processing_times = {
            func: round(time_val, 3)
            for func, time_val in self.processing_times.items()
        }
        
        return {
            "processing_times": avg_processing_times,
            "cache_stats": self.cache_stats,
            "queue_size": self.task_queue.qsize(),
            "max_concurrent_tasks": PERFORMANCE_CONSTANTS["MAX_CONCURRENT_TASKS"]
        }


# Global performance optimizer instance
performance_optimizer = AsyncPerformanceOptimizer()
