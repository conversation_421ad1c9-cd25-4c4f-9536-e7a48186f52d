"""
Task 13: API Routes - Core Endpoints

FastAPI router implementation with comprehensive endpoints for:
- POST /api/process-input - main orchestration endpoint
- GET/POST /api/tasks - task management endpoints  
- GET/POST /api/events - calendar endpoints
- GET /api/settings - configuration endpoints
- Proper error handling and validation
"""

from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field, validator
from typing import List, Dict, Any, Optional, Union
from datetime import datetime, date
from enum import Enum
import logging
import asyncio
import json

from ..config.settings import get_settings
from ..agents.orchestrator import AIOrchestrator
from ..tools.database_search_tool import HybridDatabaseSearchTool, HybridSearchInput
from ..tools.calendar_tool import CalendarTool, CreateEventInput, UpdateEventInput
from ..tools.task_tool import TaskTool, CreateTaskInput, UpdateTaskInput, SearchTasksInput
from ..tools.web_search_tool import WebSearchTool, WebSearchInput
from ..services.embedding_service import EmbeddingService
from ..utils.validation import sanitize_text, sanitize_query, validate_integer, validate_json
from ..utils.error_handling import DashboardError, ErrorCategory, ErrorSeverity

# Configure logging
logger = logging.getLogger(__name__)

# Create FastAPI router
router = APIRouter()

# Get settings
settings = get_settings()

# Global tool instances (will be initialized on startup)
orchestrator = AIOrchestrator()
database_tool = HybridDatabaseSearchTool()
calendar_tool = CalendarTool()
task_tool = TaskTool()
web_search_tool = WebSearchTool()
embedding_service = EmbeddingService()


# =============================================================================
# PYDANTIC MODELS
# =============================================================================

class ProcessingMode(str, Enum):
    """Processing modes for input."""
    AUTO = "auto"
    TASK = "task"
    EVENT = "event"
    QUESTION = "question"
    SEARCH = "search"

class Priority(str, Enum):
    """Task/Event priority levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    URGENT = "urgent"

class TaskStatus(str, Enum):
    """Task status options."""
    TODO = "todo"
    IN_PROGRESS = "in_progress"
    DONE = "done"
    CANCELLED = "cancelled"

# Request Models
class ProcessInputRequest(BaseModel):
    """Request model for main orchestration endpoint."""
    input_text: str = Field(..., description="User input text to process")
    mode: ProcessingMode = Field(
        default=ProcessingMode.AUTO,
        description="Processing mode (auto-detect or specific)"
    )
    context: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Additional context for processing"
    )
    include_embeddings: bool = Field(
        default=True,
        description="Include embedding analysis in response"
    )
    include_search: bool = Field(
        default=True,
        description="Include web search for relevant information"
    )
    
    @validator('input_text')
    def validate_input_text(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError("Input text cannot be empty")
        
        # Use security validation
        result = sanitize_text(v, max_length=5000)
        if not result.is_valid:
            errors = ", ".join(result.errors + result.security_flags)
            raise ValueError(f"Invalid input: {errors}")
        
        return result.sanitized_value
        if len(v) > 5000:
            raise ValueError("Input text too long (max 5000 characters)")
        return v.strip()

class TaskCreateRequest(BaseModel):
    """Request model for creating tasks."""
    title: str = Field(..., description="Task title")
    description: Optional[str] = Field(default="", description="Task description")
    priority: Priority = Field(default=Priority.MEDIUM, description="Task priority")
    status: TaskStatus = Field(default=TaskStatus.TODO, description="Task status")
    due_date: Optional[datetime] = Field(default=None, description="Due date")
    tags: List[str] = Field(default=[], description="Task tags")
    category: Optional[str] = Field(default=None, description="Task category")

class TaskUpdateRequest(BaseModel):
    """Request model for updating tasks."""
    task_id: int = Field(..., description="Task ID to update")
    title: Optional[str] = Field(default=None, description="Task title")
    description: Optional[str] = Field(default=None, description="Task description")
    priority: Optional[Priority] = Field(default=None, description="Task priority")
    status: Optional[TaskStatus] = Field(default=None, description="Task status")
    due_date: Optional[datetime] = Field(default=None, description="Due date")
    tags: Optional[List[str]] = Field(default=None, description="Task tags")
    category: Optional[str] = Field(default=None, description="Task category")

class EventCreateRequest(BaseModel):
    """Request model for creating events."""
    title: str = Field(..., description="Event title")
    description: Optional[str] = Field(default="", description="Event description")
    start_date: datetime = Field(..., description="Event start date and time")
    end_date: Optional[datetime] = Field(default=None, description="Event end date and time")
    location: Optional[str] = Field(default="", description="Event location")
    attendees: List[str] = Field(default=[], description="Event attendees")
    priority: Priority = Field(default=Priority.MEDIUM, description="Event priority")
    category: Optional[str] = Field(default=None, description="Event category")

class EventUpdateRequest(BaseModel):
    """Request model for updating events."""
    event_id: int = Field(..., description="Event ID to update")
    title: Optional[str] = Field(default=None, description="Event title")
    description: Optional[str] = Field(default=None, description="Event description")
    start_date: Optional[datetime] = Field(default=None, description="Event start date and time")
    end_date: Optional[datetime] = Field(default=None, description="Event end date and time")
    location: Optional[str] = Field(default=None, description="Event location")
    attendees: Optional[List[str]] = Field(default=None, description="Event attendees")
    priority: Optional[Priority] = Field(default=None, description="Event priority")
    category: Optional[str] = Field(default=None, description="Event category")

class SearchRequest(BaseModel):
    """Request model for search operations."""
    query: str = Field(..., description="Search query")
    search_type: str = Field(
        default="hybrid",
        description="Search type: database, web, or hybrid"
    )
    limit: int = Field(default=10, ge=1, le=100, description="Maximum results")
    include_embeddings: bool = Field(
        default=True,
        description="Include embedding-based similarity search"
    )

# Response Models
class ProcessInputResponse(BaseModel):
    """Response model for input processing."""
    success: bool
    processing_mode: str
    classification: Dict[str, Any]
    suggested_actions: List[Dict[str, Any]]
    context_analysis: Optional[Dict[str, Any]] = None
    web_search_results: Optional[List[Dict[str, Any]]] = None
    embedding_analysis: Optional[Dict[str, Any]] = None
    execution_time: float
    timestamp: datetime

class TaskResponse(BaseModel):
    """Response model for task operations."""
    success: bool
    task_id: Optional[int] = None
    task: Optional[Dict[str, Any]] = None
    tasks: Optional[List[Dict[str, Any]]] = None
    message: str
    timestamp: datetime

class EventResponse(BaseModel):
    """Response model for event operations."""
    success: bool
    event_id: Optional[int] = None
    event: Optional[Dict[str, Any]] = None
    events: Optional[List[Dict[str, Any]]] = None
    message: str
    timestamp: datetime

class SearchResponse(BaseModel):
    """Response model for search operations."""
    success: bool
    query: str
    search_type: str
    results: List[Dict[str, Any]]
    total_found: int
    execution_time: float
    timestamp: datetime

class SettingsResponse(BaseModel):
    """Response model for settings."""
    success: bool
    settings: Dict[str, Any]
    timestamp: datetime


# =============================================================================
# DEPENDENCY FUNCTIONS
# =============================================================================

async def get_database_tool() -> HybridDatabaseSearchTool:
    """Dependency to get database search tool."""
    return database_tool

async def get_calendar_tool() -> CalendarTool:
    """Dependency to get calendar tool."""
    return calendar_tool

async def get_task_tool() -> TaskTool:
    """Dependency to get task management tool."""
    return task_tool

async def get_web_search_tool() -> WebSearchTool:
    """Dependency to get web search tool."""
    return web_search_tool

async def get_embedding_service() -> EmbeddingService:
    """Dependency to get embedding service."""
    return embedding_service


# =============================================================================
# HELPER FUNCTIONS
# =============================================================================

async def classify_input(input_text: str) -> Dict[str, Any]:
    """
    Classify input text using AI orchestrator to determine the most likely intent.
    Uses advanced AI classification with confidence scoring.
    """
    try:
        # Use the AI orchestrator for intelligent classification
        ai_classification = await orchestrator.categorize_input(input_text)
        
        # Extract the category from AI response
        category = ai_classification.get('category', 'question')
        confidence = ai_classification.get('confidence', 0.8) * 100  # Convert to percentage
        reasoning = ai_classification.get('reasoning', '')
        
        # Map AI categories to our system categories
        category_mapping = {
            'task_management': 'task',
            'calendar_event': 'event', 
            'web_search': 'search',
            'question_answer': 'question',
            'database_search': 'search'
        }
        
        primary_type = category_mapping.get(category, 'question')
        
        # Generate capability scores based on AI analysis
        scores = {
            'task': 0.8 if primary_type == 'task' else 0.2,
            'event': 0.8 if primary_type == 'event' else 0.2,
            'question': 0.8 if primary_type == 'question' else 0.3,
            'search': 0.8 if primary_type == 'search' else 0.4
        }
        
        # Enhance with keyword detection for fallback
        text_lower = input_text.lower()
        
        # Task indicators
        task_indicators = any(word in text_lower for word in [
            'task', 'todo', 'do', 'complete', 'finish', 'deadline', 'project'
        ])
        
        # Event indicators  
        event_indicators = any(word in text_lower for word in [
            'meeting', 'event', 'schedule', 'calendar', 'appointment', 'remind'
        ])
        
        # Question indicators
        question_indicators = any(word in text_lower for word in [
            'what', 'how', 'why', 'when', 'where', 'who', '?'
        ])
        
        # Search indicators
        search_indicators = any(word in text_lower for word in [
            'search', 'find', 'look for', 'research', 'information'
        ])
        
        return {
            'primary_type': primary_type,
            'confidence': round(confidence, 2),
            'scores': scores,
            'ai_reasoning': reasoning,
            'suggestions': {
                'can_be_task': primary_type == 'task' or task_indicators,
                'can_be_event': primary_type == 'event' or event_indicators,
                'needs_search': primary_type == 'search' or question_indicators or search_indicators,
                'action_required': primary_type in ['task', 'event']
            }
        }
        
    except Exception as e:
        logger.error(f"AI classification failed: {e}")
        # Fallback to simple keyword-based classification
        return await _fallback_classify_input(input_text)


async def _fallback_classify_input(input_text: str) -> Dict[str, Any]:
    """
    Fallback classification method using keyword matching.
    Used when AI classification fails.
    """
    text_lower = input_text.lower()
    
    # Task keywords
    task_keywords = [
        'task', 'todo', 'do', 'complete', 'finish', 'work on', 'need to',
        'should', 'must', 'have to', 'deadline', 'project', 'assignment'
    ]
    
    # Event keywords  
    event_keywords = [
        'meeting', 'event', 'schedule', 'calendar', 'appointment', 'call',
        'conference', 'date', 'time', 'when', 'remind me', 'plan'
    ]
    
    # Question keywords
    question_keywords = [
        'what', 'how', 'why', 'when', 'where', 'who', 'which', 'can',
        'could', 'would', 'should', 'is', 'are', 'do', 'does', '?'
    ]
    
    # Search keywords
    search_keywords = [
        'search', 'find', 'look for', 'research', 'information', 'data',
        'results', 'show me', 'get me', 'fetch', 'retrieve'
    ]
    
    # Calculate confidence scores
    task_score = sum(1 for keyword in task_keywords if keyword in text_lower)
    event_score = sum(1 for keyword in event_keywords if keyword in text_lower)
    question_score = sum(1 for keyword in question_keywords if keyword in text_lower)
    search_score = sum(1 for keyword in search_keywords if keyword in text_lower)
    
    # Determine primary classification
    scores = {
        'task': task_score,
        'event': event_score,
        'question': question_score,
        'search': search_score
    }
    
    primary_type = max(scores.keys(), key=lambda k: scores[k])
    max_score = scores[primary_type]
    
    # If no clear classification, default to question
    if max_score == 0:
        primary_type = 'question'
        max_score = 1
    
    # Calculate confidence as percentage
    total_score = sum(scores.values()) or 1
    confidence = (max_score / total_score) * 100
    
    return {
        'primary_type': primary_type,
        'confidence': round(confidence, 2),
        'scores': scores,
        'ai_reasoning': 'Fallback keyword-based classification',
        'suggestions': {
            'can_be_task': task_score > 0,
            'can_be_event': event_score > 0,
            'needs_search': question_score > 0 or search_score > 0,
            'action_required': task_score > 0 or event_score > 0
        }
    }

def generate_suggested_actions(
    input_text: str, 
    classification: Dict[str, Any]
) -> List[Dict[str, Any]]:
    """Generate suggested actions based on input classification."""
    actions = []
    primary_type = classification['primary_type']
    suggestions = classification['suggestions']
    
    if primary_type == 'task' or suggestions['can_be_task']:
        actions.append({
            'type': 'create_task',
            'title': 'Create Task',
            'description': 'Convert this input into a task',
            'confidence': classification['confidence'] if primary_type == 'task' else 50,
            'data': {
                'title': input_text[:100] + '...' if len(input_text) > 100 else input_text,
                'priority': 'medium',
                'status': 'todo'
            }
        })
    
    if primary_type == 'event' or suggestions['can_be_event']:
        actions.append({
            'type': 'create_event',
            'title': 'Create Event',
            'description': 'Convert this input into a calendar event',
            'confidence': classification['confidence'] if primary_type == 'event' else 40,
            'data': {
                'title': input_text[:100] + '...' if len(input_text) > 100 else input_text,
                'priority': 'medium'
            }
        })
    
    if suggestions['needs_search']:
        actions.append({
            'type': 'web_search',
            'title': 'Search Web',
            'description': 'Find relevant information online',
            'confidence': classification['confidence'] if primary_type == 'search' else 60,
            'data': {
                'query': input_text,
                'count': 5,
                'summary': True
            }
        })
    
    actions.append({
        'type': 'database_search',
        'title': 'Search Database',
        'description': 'Search existing tasks and events',
        'confidence': 70,
        'data': {
            'query': input_text,
            'search_type': 'hybrid',
            'limit': 10
        }
    })
    
    # Sort by confidence
    actions.sort(key=lambda x: x['confidence'], reverse=True)
    return actions


# =============================================================================
# API ENDPOINTS
# =============================================================================

@router.post("/process-input", response_model=ProcessInputResponse)
async def process_input(
    request: ProcessInputRequest,
    background_tasks: BackgroundTasks,
    db_tool: HybridDatabaseSearchTool = Depends(get_database_tool),
    web_tool: WebSearchTool = Depends(get_web_search_tool),
    embedding_svc: EmbeddingService = Depends(get_embedding_service)
):
    """
    Main orchestration endpoint for processing user input.
    
    This endpoint:
    1. Analyzes and classifies the input
    2. Generates suggested actions
    3. Optionally performs web search
    4. Optionally performs embedding analysis
    5. Returns comprehensive analysis results
    """
    start_time = datetime.now()
    
    try:
        logger.info(f"Processing input: {request.input_text[:100]}...")
        
        # Step 1: Classify input
        if request.mode == ProcessingMode.AUTO:
            classification = await classify_input(request.input_text)
            processing_mode = classification['primary_type']
        else:
            processing_mode = request.mode.value
            classification = {
                'primary_type': processing_mode,
                'confidence': 100.0,
                'scores': {processing_mode: 1},
                'suggestions': {'needs_search': True, 'action_required': True}
            }
        
        # Step 2: Generate suggested actions
        suggested_actions = generate_suggested_actions(request.input_text, classification)
        
        # Step 3: Context analysis (database search)
        context_analysis = None
        try:
            db_search_input = HybridSearchInput(
                query=request.input_text,
                search_type="all",  # Valid values: 'tasks', 'events', 'inputs', 'all'
                search_mode="hybrid",  # This specifies the search strategy
                limit=5,
                include_metadata=True
            )
            db_result = await db_tool.process_search_request(db_search_input)
            context_analysis = {
                'related_items_found': len(db_result.get('results', [])),
                'results': db_result.get('results', [])[:3],  # Top 3 for context
                'search_success': db_result.get('success', False)
            }
        except Exception as e:
            logger.error(f"Context analysis failed: {e}")
            context_analysis = {'error': str(e)}
        
        # Step 4: Web search (if requested and needed)
        web_search_results = None
        if request.include_search and classification.get('suggestions', {}).get('needs_search'):
            try:
                web_search_input = WebSearchInput(
                    query=request.input_text,
                    count=3,
                    summary=True,
                    freshness="oneWeek"
                )
                web_result = await web_tool.search_web(web_search_input)
                if web_result.get('success'):
                    web_search_results = web_result.get('results', [])
            except Exception as e:
                logger.error(f"Web search failed: {e}")
        
        # Step 5: Embedding analysis (if requested)
        embedding_analysis = None
        if request.include_embeddings:
            try:
                embedding_result = await embedding_svc.generate_embeddings(request.input_text)
                if embedding_result.get('success'):
                    embedding_analysis = {
                        'embedding_generated': True,
                        'model_used': embedding_result.get('result', {}).get('model'),
                        'embedding_dimension': len(embedding_result.get('result', {}).get('embedding', [])),
                        'processing_time': embedding_result.get('result', {}).get('processing_time')
                    }
            except Exception as e:
                logger.error(f"Embedding analysis failed: {e}")
                embedding_analysis = {'error': str(e)}
        
        # Calculate execution time
        execution_time = (datetime.now() - start_time).total_seconds()
        
        logger.info(f"Input processing completed in {execution_time:.3f}s")
        
        return ProcessInputResponse(
            success=True,
            processing_mode=processing_mode,
            classification=classification,
            suggested_actions=suggested_actions,
            context_analysis=context_analysis,
            web_search_results=web_search_results,
            embedding_analysis=embedding_analysis,
            execution_time=execution_time,
            timestamp=datetime.now()
        )
        
    except Exception as e:
        logger.error(f"Input processing failed: {e}")
        execution_time = (datetime.now() - start_time).total_seconds()
        
        return ProcessInputResponse(
            success=False,
            processing_mode="error",
            classification={'error': str(e)},
            suggested_actions=[],
            execution_time=execution_time,
            timestamp=datetime.now()
        )


# =============================================================================
# TASK MANAGEMENT ENDPOINTS
# =============================================================================

@router.get("/tasks", response_model=TaskResponse)
async def get_tasks(
    limit: int = 50,
    status: Optional[TaskStatus] = None,
    priority: Optional[Priority] = None,
    category: Optional[str] = None,
    task_tool: TaskTool = Depends(get_task_tool)
):
    """Get tasks with optional filtering."""
    try:
        # Build filter criteria
        filters = {}
        if status:
            filters['status'] = status.value
        if priority:
            filters['priority'] = priority.value
        if category:
            filters['category'] = category
            
        search_input = SearchTasksInput(
            limit=limit,
            **filters  # Unpack the filters
        )
        
        result = await task_tool.search_tasks(search_input)
        
        return TaskResponse(
            success=result.get('success', False),
            tasks=result.get('tasks', []),
            message=result.get('message', 'Tasks retrieved successfully'),
            timestamp=datetime.now()
        )
        
    except Exception as e:
        logger.error(f"Failed to get tasks: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/tasks", response_model=TaskResponse)
async def create_task(
    request: TaskCreateRequest,
    task_tool: TaskTool = Depends(get_task_tool)
):
    """Create a new task."""
    try:
        task_input = CreateTaskInput(
            title=request.title,
            description=request.description,
            priority=request.priority.value,
            status=request.status.value,
            due_date=request.due_date,
            tags=request.tags,
            category=request.category
        )
        
        result = await task_tool.create_task(task_input)
        
        return TaskResponse(
            success=result.get('success', False),
            task_id=result.get('task_id'),
            task=result.get('task'),
            message=result.get('message', 'Task created successfully'),
            timestamp=datetime.now()
        )
        
    except Exception as e:
        logger.error(f"Failed to create task: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.put("/tasks/{task_id}", response_model=TaskResponse)
async def update_task(
    task_id: int,
    request: TaskUpdateRequest,
    task_tool: TaskTool = Depends(get_task_tool)
):
    """Update an existing task."""
    try:
        # Build update data
        update_data = {"id": task_id}
        if request.title is not None:
            update_data["title"] = request.title
        if request.description is not None:
            update_data["description"] = request.description
        if request.priority is not None:
            update_data["priority"] = request.priority.value
        if request.status is not None:
            update_data["status"] = request.status.value
        if request.due_date is not None:
            update_data["due_date"] = request.due_date
        if request.tags is not None:
            update_data["tags"] = request.tags
        if request.category is not None:
            update_data["category"] = request.category
        
        task_update_input = UpdateTaskInput(
            task_id=task_id,
            **update_data  # Unpack the update data
        )
        
        result = await task_tool.update_task(task_update_input)
        
        return TaskResponse(
            success=result.get('success', False),
            task_id=task_id,
            task=result.get('task'),
            message=result.get('message', 'Task updated successfully'),
            timestamp=datetime.now()
        )
        
    except Exception as e:
        logger.error(f"Failed to update task {task_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/tasks/{task_id}", response_model=TaskResponse)
async def delete_task(
    task_id: int,
    task_tool: TaskTool = Depends(get_task_tool)
):
    """Delete a task."""
    try:
        # Call delete method directly with task_id
        result = await task_tool.delete_task(task_id)
        
        return TaskResponse(
            success=result.get('success', False),
            task_id=task_id,
            message=result.get('message', 'Task deleted successfully'),
            timestamp=datetime.now()
        )
        
    except Exception as e:
        logger.error(f"Failed to delete task {task_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# =============================================================================
# CALENDAR/EVENT MANAGEMENT ENDPOINTS
# =============================================================================

@router.get("/events", response_model=EventResponse)
async def get_events(
    start_date: Optional[date] = None,
    end_date: Optional[date] = None,
    limit: int = 50,
    category: Optional[str] = None,
    calendar_tool: CalendarTool = Depends(get_calendar_tool)
):
    """Get events with optional date range filtering."""
    try:
        event_input = CreateEventInput(
            action="list",
            limit=limit,
            start_date=datetime.combine(start_date, datetime.min.time()) if start_date else None,
            end_date=datetime.combine(end_date, datetime.max.time()) if end_date else None,
            filters={'category': category} if category else {}
        )
        
        result = await calendar_tool.process_event_request(event_input)
        
        return EventResponse(
            success=result.get('success', False),
            events=result.get('events', []),
            message=result.get('message', 'Events retrieved successfully'),
            timestamp=datetime.now()
        )
        
    except Exception as e:
        logger.error(f"Failed to get events: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/events", response_model=EventResponse)
async def create_event(
    request: EventCreateRequest,
    calendar_tool: CalendarTool = Depends(get_calendar_tool)
):
    """Create a new event."""
    try:
        event_input = CreateEventInput(
            action="create",
            title=request.title,
            description=request.description,
            start_date=request.start_date,
            end_date=request.end_date,
            location=request.location,
            attendees=request.attendees,
            priority=request.priority.value,
            category=request.category
        )
        
        result = await calendar_tool.process_event_request(event_input)
        
        return EventResponse(
            success=result.get('success', False),
            event_id=result.get('event_id'),
            event=result.get('event'),
            message=result.get('message', 'Event created successfully'),
            timestamp=datetime.now()
        )
        
    except Exception as e:
        logger.error(f"Failed to create event: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.put("/events/{event_id}", response_model=EventResponse)
async def update_event(
    event_id: int,
    request: EventUpdateRequest,
    calendar_tool: CalendarTool = Depends(get_calendar_tool)
):
    """Update an existing event."""
    try:
        # Build update data
        update_data = {"id": event_id}
        if request.title is not None:
            update_data["title"] = request.title
        if request.description is not None:
            update_data["description"] = request.description
        if request.start_date is not None:
            update_data["start_date"] = request.start_date
        if request.end_date is not None:
            update_data["end_date"] = request.end_date
        if request.location is not None:
            update_data["location"] = request.location
        if request.attendees is not None:
            update_data["attendees"] = request.attendees
        if request.priority is not None:
            update_data["priority"] = request.priority.value
        if request.category is not None:
            update_data["category"] = request.category
        
        event_update_input = UpdateEventInput(
            event_id=event_id,
            updates=update_data
        )
        
        result = await calendar_tool.update_event(event_update_input)
        
        return EventResponse(
            success=result.get('success', False),
            event_id=event_id,
            event=result.get('event'),
            message=result.get('message', 'Event updated successfully'),
            timestamp=datetime.now()
        )
        
    except Exception as e:
        logger.error(f"Failed to update event {event_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/events/{event_id}", response_model=EventResponse)
async def delete_event(
    event_id: int,
    calendar_tool: CalendarTool = Depends(get_calendar_tool)
):
    """Delete an event."""
    try:
        event_input = CreateEventInput(
            action="delete",
            event_id=event_id
        )
        
        result = await calendar_tool.process_event_request(event_input)
        
        return EventResponse(
            success=result.get('success', False),
            event_id=event_id,
            message=result.get('message', 'Event deleted successfully'),
            timestamp=datetime.now()
        )
        
    except Exception as e:
        logger.error(f"Failed to delete event {event_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# =============================================================================
# SEARCH ENDPOINTS
# =============================================================================

@router.post("/search", response_model=SearchResponse)
async def search(
    request: SearchRequest,
    db_tool: HybridDatabaseSearchTool = Depends(get_database_tool),
    web_tool: WebSearchTool = Depends(get_web_search_tool)
):
    """Perform search across database and/or web."""
    start_time = datetime.now()
    
    try:
        results = []
        total_found = 0
        
        if request.search_type in ["database", "hybrid"]:
            # Database search
            db_search_input = HybridSearchInput(
                query=request.query,
                search_type="all",  # Valid values: 'tasks', 'events', 'inputs', 'all'
                search_mode="hybrid",  # This specifies the search strategy
                limit=request.limit // 2 if request.search_type == "hybrid" else request.limit,
                include_metadata=request.include_embeddings
            )
            
            db_result = await db_tool.process_search_request(db_search_input)
            if db_result.get('success'):
                db_results = db_result.get('results', [])
                for result in db_results:
                    result['source'] = 'database'
                results.extend(db_results)
                total_found += len(db_results)
        
        if request.search_type in ["web", "hybrid"]:
            # Web search
            web_search_input = WebSearchInput(
                query=request.query,
                count=request.limit // 2 if request.search_type == "hybrid" else request.limit,
                summary=True
            )
            
            web_result = await web_tool.search_web(web_search_input)
            if web_result.get('success'):
                web_results = web_result.get('results', [])
                for result in web_results:
                    result['source'] = 'web'
                results.extend(web_results)
                total_found += len(web_results)
        
        # Sort results by relevance if available
        if request.search_type == "hybrid":
            results.sort(key=lambda x: x.get('relevance_score', x.get('similarity', 0)), reverse=True)
        
        # Limit final results
        results = results[:request.limit]
        
        execution_time = (datetime.now() - start_time).total_seconds()
        
        return SearchResponse(
            success=True,
            query=request.query,
            search_type=request.search_type,
            results=results,
            total_found=total_found,
            execution_time=execution_time,
            timestamp=datetime.now()
        )
        
    except Exception as e:
        logger.error(f"Search failed: {e}")
        execution_time = (datetime.now() - start_time).total_seconds()
        raise HTTPException(status_code=500, detail=str(e))


# =============================================================================
# SETTINGS ENDPOINT
# =============================================================================

@router.get("/settings", response_model=SettingsResponse)
async def get_settings():
    """Get application settings and configuration."""
    try:
        app_settings = {
            'api': {
                'host': settings.api_host,
                'port': settings.api_port,
                'debug': settings.debug,
                'environment': settings.environment
            },
            'features': {
                'embeddings_enabled': bool(settings.ollama_base_url),
                'web_search_enabled': bool(settings.langsearch_api_key),
                'database_enabled': True,
                'calendar_enabled': True,
                'task_management_enabled': True
            },
            'limits': {
                'max_input_length': 5000,
                'max_search_results': 100,
                'max_tasks_per_request': 50,
                'max_events_per_request': 50
            },
            'models': {
                'default_ollama_model': settings.OLLAMA_MODEL,
                'primary_llm_model': settings.primary_llm_model,
                'embedding_models_available': [
                    'nomic-embed-text',
                    'mxbai-embed-large',
                    'bge-m3',
                    'all-minilm'
                ]
            },
            'performance': {
                'max_workers': settings.max_workers,
                'connection_pool_size': settings.connection_pool_size,
                'request_timeout': settings.request_timeout
            }
        }
        
        return SettingsResponse(
            success=True,
            settings=app_settings,
            timestamp=datetime.now()
        )
        
    except Exception as e:
        logger.error(f"Failed to get settings: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# =============================================================================
# HEALTH CHECK ENDPOINTS
# =============================================================================

@router.get("/health")
async def health_check():
    """Extended health check with service status."""
    try:
        # Check database connectivity
        db_status = "unknown"
        try:
            db_result = await database_tool.get_search_stats()
            db_status = "healthy" if db_result.get('success') else "unhealthy"
        except:
            db_status = "unhealthy"
        
        # Check embedding service
        embedding_status = "unknown"
        try:
            embedding_result = await embedding_service.get_stats()
            embedding_status = "healthy" if embedding_result.get('success') else "unhealthy"
        except:
            embedding_status = "unhealthy"
        
        # Check web search
        web_search_status = "healthy" if settings.langsearch_api_key else "disabled"
        
        return {
            "status": "healthy",
            "service": "ai-powered-dashboard-api",
            "version": "1.0.0",
            "timestamp": datetime.now().isoformat(),
            "services": {
                "database": db_status,
                "embeddings": embedding_status,
                "web_search": web_search_status,
                "task_management": "healthy",
                "calendar": "healthy"
            }
        }
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }


# =============================================================================
# STATISTICS ENDPOINTS
# =============================================================================

@router.get("/stats")
async def get_api_stats():
    """Get API usage statistics and service metrics."""
    try:
        # Get individual service stats
        db_stats = await database_tool.get_search_stats()
        embedding_stats = await embedding_service.get_stats()
        
        return {
            "success": True,
            "api_version": "1.0.0",
            "timestamp": datetime.now().isoformat(),
            "services": {
                "database_search": db_stats,
                "embeddings": embedding_stats,
                "web_search": {
                    "enabled": bool(settings.langsearch_api_key),
                    "api_configured": bool(settings.langsearch_api_key)
                }
            },
            "endpoints": {
                "total_endpoints": 16,
                "categories": {
                    "input_processing": 1,
                    "task_management": 4,
                    "event_management": 4,
                    "search": 1,
                    "settings": 1,
                    "system": 5
                }
            }
        }
        
    except Exception as e:
        logger.error(f"Failed to get stats: {e}")
        return {
            "success": False,
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }
