import { useState } from 'react'
import { motion } from 'framer-motion'
import { APISettings } from './APISettings'
import { EmbeddingsStatus } from './EmbeddingsStatus'

/**
 * Settings Component  
 * PATTERN: Settings page with form validation
 * Features:
 * - API key management (masked inputs)
 * - Model selection for different providers
 * - Embedding status indicators  
 * - Index management controls
 * - Tabbed interface for organization
 */
export function Settings() {
  const [activeTab, setActiveTab] = useState<'api' | 'embeddings' | 'general'>('api')

  const tabs = [
    { id: 'api' as const, label: 'API Configuration', icon: '🔑' },
    { id: 'embeddings' as const, label: 'Embeddings', icon: '🧠' },
    { id: 'general' as const, label: 'General', icon: '⚙️' }
  ]

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.3,
        staggerChildren: 0.1
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 20
      }
    }
  }

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="max-w-4xl mx-auto p-6"
    >
      {/* Header */}
      <motion.div variants={itemVariants} className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Settings</h1>
        <p className="text-gray-600">Configure your AI-powered dashboard</p>
      </motion.div>

      {/* Tab Navigation */}
      <motion.div 
        variants={itemVariants}
        className="border-b border-gray-200 mb-8"
      >
        <nav className="flex space-x-8">
          {tabs.map((tab) => (
            <motion.button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`
                flex items-center gap-2 py-2 px-1 border-b-2 font-medium text-sm transition-colors
                ${activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }
              `}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <span className="text-lg">{tab.icon}</span>
              {tab.label}
            </motion.button>
          ))}
        </nav>
      </motion.div>

      {/* Tab Content */}
      <motion.div
        key={activeTab}
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        exit={{ opacity: 0, x: -20 }}
        transition={{ duration: 0.3 }}
      >
        {activeTab === 'api' && <APISettings />}
        {activeTab === 'embeddings' && <EmbeddingsStatus />}
        {activeTab === 'general' && (
          <motion.div
            variants={itemVariants}
            className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
          >
            <h3 className="text-lg font-semibold text-gray-900 mb-4">General Settings</h3>
            
            {/* Theme Selection */}
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Theme
                </label>
                <div className="flex gap-3">
                  {['Light', 'Dark', 'Auto'].map((theme) => (
                    <motion.button
                      key={theme}
                      className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors"
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      {theme}
                    </motion.button>
                  ))}
                </div>
              </div>

              {/* Animation Settings */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Animation Speed
                </label>
                <input
                  type="range"
                  min="0.5"
                  max="2"
                  step="0.1"
                  defaultValue="1"
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                />
                <div className="flex justify-between text-xs text-gray-500 mt-1">
                  <span>Slow</span>
                  <span>Normal</span>
                  <span>Fast</span>
                </div>
              </div>

              {/* Auto-save */}
              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium text-gray-700">
                    Auto-save inputs
                  </label>
                  <p className="text-xs text-gray-500">
                    Automatically save your inputs as you type
                  </p>
                </div>
                <motion.button
                  className="relative inline-flex h-6 w-11 items-center rounded-full bg-blue-600 transition-colors"
                  whileTap={{ scale: 0.95 }}
                >
                  <motion.span
                    className="inline-block h-4 w-4 transform rounded-full bg-white transition-transform"
                    initial={{ x: 1 }}
                    animate={{ x: 6 }}
                  />
                </motion.button>
              </div>

              {/* Notifications */}
              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium text-gray-700">
                    Desktop notifications
                  </label>
                  <p className="text-xs text-gray-500">
                    Get notified when AI processing completes
                  </p>
                </div>
                <motion.button
                  className="relative inline-flex h-6 w-11 items-center rounded-full bg-gray-200 transition-colors"
                  whileTap={{ scale: 0.95 }}
                >
                  <motion.span
                    className="inline-block h-4 w-4 transform rounded-full bg-white transition-transform"
                    initial={{ x: 1 }}
                  />
                </motion.button>
              </div>
            </div>

            {/* Save Button */}
            <motion.div className="mt-8">
              <motion.button
                className="px-6 py-3 bg-blue-600 text-white rounded-md font-medium hover:bg-blue-700 transition-colors"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                Save General Settings
              </motion.button>
            </motion.div>
          </motion.div>
        )}
      </motion.div>
    </motion.div>
  )
}
