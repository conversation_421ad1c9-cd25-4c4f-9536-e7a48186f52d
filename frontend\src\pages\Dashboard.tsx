import React from 'react'
import { motion } from 'framer-motion'
import HeroInputBar from '@/components/dashboard/HeroInputBar'
import VisualFeedback from '@/components/dashboard/VisualFeedback'

/**
 * Dashboard page component - Main AI-powered interface
 */
const Dashboard: React.FC = () => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="min-h-screen bg-background-primary"
    >
      {/* Hero section with input bar */}
      <div className="relative min-h-[60vh] flex items-center justify-center px-6">
        <div className="w-full max-w-4xl mx-auto text-center">
          {/* Welcome header */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="mb-12"
          >
            <h1 className="text-4xl md:text-6xl font-bold text-text-primary mb-4">
              AI-Powered
              <span className="gradient-text ml-3">Dashboard</span>
            </h1>
            <p className="text-lg md:text-xl text-text-secondary max-w-2xl mx-auto">
              Tell me anything - I'll intelligently categorize it into tasks, events, or questions 
              and handle it with the right AI tools.
            </p>
          </motion.div>

          {/* Hero Input Bar - Main Feature */}
          <motion.div
            initial={{ opacity: 0, y: 40 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="mb-8"
          >
            <HeroInputBar />
          </motion.div>

          {/* Visual Feedback System */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3 }}
          >
            <VisualFeedback />
          </motion.div>

          {/* Quick examples */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="mt-12"
          >
            <p className="text-text-muted text-sm mb-4">Try examples like:</p>
            <div className="flex flex-wrap justify-center gap-3">
              {[
                "Schedule a meeting with the team tomorrow at 3pm",
                "Create a task to review the quarterly report",
                "What's the weather like in New York?",
                "Remind me to call mom on Friday",
                "Search for React hooks best practices"
              ].map((example, index) => (
                <motion.button
                  key={index}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.5 + index * 0.1 }}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="px-4 py-2 bg-background-secondary hover:bg-background-hover border border-border-primary rounded-xl text-text-secondary hover:text-text-primary text-sm transition-all duration-200"
                  onClick={() => {
                    // This would trigger the input bar with the example
                    const event = new CustomEvent('setHeroInput', { detail: example })
                    window.dispatchEvent(event)
                  }}
                >
                  "{example}"
                </motion.button>
              ))}
            </div>
          </motion.div>
        </div>

        {/* Background decoration */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-accent-blue/5 rounded-full blur-3xl" />
          <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-accent-purple/5 rounded-full blur-3xl" />
        </div>
      </div>

      {/* Recent activity section */}
      <div className="px-6 pb-12">
        <div className="max-w-7xl mx-auto">
          <motion.h2
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.5 }}
            className="text-2xl font-bold text-text-primary mb-6"
          >
            Recent Activity
          </motion.h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Recent Tasks */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6 }}
              className="card-primary p-6"
            >
              <div className="flex items-center gap-3 mb-4">
                <div className="w-8 h-8 bg-accent-blue/10 rounded-lg flex items-center justify-center">
                  <span className="text-accent-blue">✓</span>
                </div>
                <h3 className="font-semibold text-text-primary">Recent Tasks</h3>
              </div>
              <p className="text-text-secondary text-sm mb-4">
                AI-categorized tasks from your recent inputs
              </p>
              <div className="space-y-2">
                <div className="text-text-muted text-sm">No recent tasks</div>
                <div className="text-text-muted text-xs">
                  Start by typing something above!
                </div>
              </div>
            </motion.div>

            {/* Recent Events */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.7 }}
              className="card-primary p-6"
            >
              <div className="flex items-center gap-3 mb-4">
                <div className="w-8 h-8 bg-accent-green/10 rounded-lg flex items-center justify-center">
                  <span className="text-accent-green">📅</span>
                </div>
                <h3 className="font-semibold text-text-primary">Recent Events</h3>
              </div>
              <p className="text-text-secondary text-sm mb-4">
                Smart calendar events from your inputs
              </p>
              <div className="space-y-2">
                <div className="text-text-muted text-sm">No recent events</div>
                <div className="text-text-muted text-xs">
                  Try "Schedule a meeting tomorrow"
                </div>
              </div>
            </motion.div>

            {/* AI Insights */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.8 }}
              className="card-primary p-6"
            >
              <div className="flex items-center gap-3 mb-4">
                <div className="w-8 h-8 bg-accent-purple/10 rounded-lg flex items-center justify-center">
                  <span className="text-accent-purple">🧠</span>
                </div>
                <h3 className="font-semibold text-text-primary">AI Insights</h3>
              </div>
              <p className="text-text-secondary text-sm mb-4">
                Intelligent analysis and suggestions
              </p>
              <div className="space-y-2">
                <div className="text-text-muted text-sm">Ready for input</div>
                <div className="text-text-muted text-xs">
                  The AI is waiting for your first command
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </motion.div>
  )
}

export default Dashboard
