"""
WebSocket Manager - Real-time Performance Optimization
PATTERN: Async WebSocket management with performance optimization
Features:
- Connection pooling and management
- Message queuing with backpressure handling
- Real-time visual feedback following mermaid diagram EXACTLY
- Performance monitoring for WebSocket connections
- Memory-efficient message broadcasting
"""

import asyncio
import json
import logging
import time
from typing import Dict, List, Any, Optional
from collections import defaultdict, deque
from datetime import datetime
from fastapi import WebSocket, WebSocketDisconnect
from dataclasses import dataclass

from app.config.settings import get_settings, PERFORMANCE_CONSTANTS, VISUAL_FEEDBACK_STATES

logger = logging.getLogger(__name__)
settings = get_settings()

@dataclass
class ConnectionInfo:
    """Information about a WebSocket connection."""
    websocket: WebSocket
    connect_time: datetime
    session_id: str
    last_activity: datetime
    message_count: int = 0
    is_active: bool = True

class WebSocketManager:
    """High-performance WebSocket connection manager for real-time AI feedback."""
    
    def __init__(self):
        # Connection management
        self.active_connections: Dict[str, ConnectionInfo] = {}
        self.connection_groups: Dict[str, List[str]] = defaultdict(list)  # Group connections by session
        
        # Message queuing and performance optimization
        self.message_queues: Dict[str, deque] = defaultdict(lambda: deque(maxsize=settings.websocket_message_queue_size))
        self.performance_metrics = {
            "connections_total": 0,
            "connections_active": 0,
            "messages_sent": 0,
            "messages_failed": 0,
            "avg_message_latency": 0.0,
            "memory_usage_mb": 0.0
        }
        
        # Background tasks
        self.cleanup_task = None
        self.heartbeat_task = None
        
        # Rate limiting per connection
        self.message_rate_limits: Dict[str, deque] = defaultdict(lambda: deque(maxsize=100))
        
    async def connect(self, websocket: WebSocket, session_id: str = None) -> str:
        """Accept a new WebSocket connection with performance optimization."""
        await websocket.accept()
        
        # Generate connection ID
        connection_id = f"conn_{int(time.time() * 1000)}_{len(self.active_connections)}"
        if not session_id:
            session_id = f"session_{int(time.time())}"
        
        # Store connection info
        self.active_connections[connection_id] = ConnectionInfo(
            websocket=websocket,
            connect_time=datetime.utcnow(),
            session_id=session_id,
            last_activity=datetime.utcnow()
        )
        
        # Add to session group
        self.connection_groups[session_id].append(connection_id)
        
        # Update metrics
        self.performance_metrics["connections_total"] += 1
        self.performance_metrics["connections_active"] = len(self.active_connections)
        
        # Start background tasks if this is the first connection
        if len(self.active_connections) == 1:
            await self._start_background_tasks()
        
        logger.info(f"WebSocket connected: {connection_id} (session: {session_id})")
        return connection_id
    
    async def disconnect(self, connection_id_or_websocket, reason: str = "client_disconnect"):
        """Disconnect a WebSocket connection with cleanup."""
        connection_id = None
        
        # Find connection by WebSocket object if needed
        if isinstance(connection_id_or_websocket, WebSocket):
            for cid, conn_info in self.active_connections.items():
                if conn_info.websocket == connection_id_or_websocket:
                    connection_id = cid
                    break
        else:
            connection_id = connection_id_or_websocket
        
        if not connection_id or connection_id not in self.active_connections:
            return
        
        connection_info = self.active_connections[connection_id]
        
        # Remove from session group
        session_id = connection_info.session_id
        if connection_id in self.connection_groups[session_id]:
            self.connection_groups[session_id].remove(connection_id)
        
        # Clean up empty session groups
        if not self.connection_groups[session_id]:
            del self.connection_groups[session_id]
        
        # Clean up connection data
        del self.active_connections[connection_id]
        if connection_id in self.message_queues:
            del self.message_queues[connection_id]
        if connection_id in self.message_rate_limits:
            del self.message_rate_limits[connection_id]
        
        # Update metrics
        self.performance_metrics["connections_active"] = len(self.active_connections)
        
        # Stop background tasks if no connections
        if not self.active_connections:
            await self._stop_background_tasks()
        
        logger.info(f"WebSocket disconnected: {connection_id} (reason: {reason})")
    
    async def send_message(self, connection_id: str, message: Dict[str, Any]) -> bool:
        """Send message to specific connection with performance optimization."""
        if connection_id not in self.active_connections:
            return False
        
        connection_info = self.active_connections[connection_id]
        
        # Check rate limiting
        if not await self._check_message_rate_limit(connection_id):
            logger.warning(f"Message rate limit exceeded for connection {connection_id}")
            return False
        
        try:
            # Add timestamp and performance data
            message["timestamp"] = datetime.utcnow().isoformat()
            message["connection_id"] = connection_id
            
            # Send message
            start_time = time.time()
            await connection_info.websocket.send_text(json.dumps(message))
            
            # Update performance metrics
            latency = time.time() - start_time
            self._update_message_metrics(latency, True)
            
            # Update connection activity
            connection_info.last_activity = datetime.utcnow()
            connection_info.message_count += 1
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to send message to {connection_id}: {e}")
            self._update_message_metrics(0, False)
            
            # Mark connection as inactive and disconnect
            connection_info.is_active = False
            await self.disconnect(connection_id, "send_error")
            return False
    
    async def broadcast_to_session(self, session_id: str, message: Dict[str, Any]) -> int:
        """Broadcast message to all connections in a session."""
        if session_id not in self.connection_groups:
            return 0
        
        successful_sends = 0
        connection_ids = self.connection_groups[session_id].copy()  # Copy to avoid modification during iteration
        
        # Send to all connections in session concurrently
        tasks = [
            self.send_message(connection_id, message.copy())
            for connection_id in connection_ids
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        for success in results:
            if success is True:
                successful_sends += 1
        
        return successful_sends
    
    async def broadcast_to_all(self, message: Dict[str, Any]) -> int:
        """Broadcast message to all active connections."""
        if not self.active_connections:
            return 0
        
        # Send to all connections concurrently with controlled concurrency
        semaphore = asyncio.Semaphore(50)  # Limit concurrent sends
        
        async def send_with_semaphore(connection_id: str):
            async with semaphore:
                return await self.send_message(connection_id, message.copy())
        
        tasks = [
            send_with_semaphore(connection_id)
            for connection_id in list(self.active_connections.keys())
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        successful_sends = sum(1 for success in results if success is True)
        return successful_sends
    
    async def send_visual_feedback(self, session_id: str, state_key: str, data: Dict[str, Any] = None):
        """
        Send visual feedback message following ai_orchestrator_flow.mermaid EXACTLY.
        
        This is the core method for implementing the mermaid diagram flow with
        proper visual transparency and performance optimization.
        """
        if state_key not in VISUAL_FEEDBACK_STATES:
            logger.warning(f"Unknown visual feedback state: {state_key}")
            return False
        
        feedback_state = VISUAL_FEEDBACK_STATES[state_key]
        
        message = {
            "type": "visual_feedback",
            "state": state_key,
            "message": feedback_state["message"],
            "animation": feedback_state["animation"],
            "duration": feedback_state["duration"],
            "data": data or {},
            "timestamp": datetime.utcnow().isoformat()
        }
        
        # Broadcast to all connections in the session
        sent_count = await self.broadcast_to_session(session_id, message)
        
        # Wait for the animation duration to maintain proper flow timing
        await asyncio.sleep(feedback_state["duration"])
        
        return sent_count > 0
    
    async def _check_message_rate_limit(self, connection_id: str) -> bool:
        """Check if connection has exceeded message rate limit."""
        current_time = time.time()
        window_start = current_time - 60  # 1-minute window
        
        rate_queue = self.message_rate_limits[connection_id]
        
        # Remove old timestamps
        while rate_queue and rate_queue[0] < window_start:
            rate_queue.popleft()
        
        # Check if under limit (100 messages per minute per connection)
        if len(rate_queue) >= 100:
            return False
        
        # Record this message
        rate_queue.append(current_time)
        return True
    
    def _update_message_metrics(self, latency: float, success: bool):
        """Update message performance metrics."""
        if success:
            self.performance_metrics["messages_sent"] += 1
            
            # Update average latency (rolling average)
            current_avg = self.performance_metrics["avg_message_latency"]
            total_messages = self.performance_metrics["messages_sent"]
            self.performance_metrics["avg_message_latency"] = (
                (current_avg * (total_messages - 1) + latency) / total_messages
            )
        else:
            self.performance_metrics["messages_failed"] += 1
    
    async def _start_background_tasks(self):
        """Start background maintenance tasks."""
        if not self.cleanup_task:
            self.cleanup_task = asyncio.create_task(self._connection_cleanup_loop())
        
        if not self.heartbeat_task:
            self.heartbeat_task = asyncio.create_task(self._heartbeat_loop())
    
    async def _stop_background_tasks(self):
        """Stop background maintenance tasks."""
        if self.cleanup_task:
            self.cleanup_task.cancel()
            try:
                await self.cleanup_task
            except asyncio.CancelledError:
                pass
            self.cleanup_task = None
        
        if self.heartbeat_task:
            self.heartbeat_task.cancel()
            try:
                await self.heartbeat_task
            except asyncio.CancelledError:
                pass
            self.heartbeat_task = None
    
    async def _connection_cleanup_loop(self):
        """Background task to clean up inactive connections."""
        while True:
            try:
                await asyncio.sleep(300)  # Run every 5 minutes
                
                current_time = datetime.utcnow()
                inactive_connections = []
                
                for connection_id, conn_info in self.active_connections.items():
                    # Mark connections inactive after 1 hour of no activity
                    if (current_time - conn_info.last_activity).total_seconds() > 3600:
                        inactive_connections.append(connection_id)
                
                # Disconnect inactive connections
                for connection_id in inactive_connections:
                    await self.disconnect(connection_id, "cleanup_timeout")
                
                if inactive_connections:
                    logger.info(f"Cleaned up {len(inactive_connections)} inactive WebSocket connections")
                
            except Exception as e:
                logger.error(f"Connection cleanup error: {e}")
    
    async def _heartbeat_loop(self):
        """Background task to send heartbeat messages."""
        while True:
            try:
                await asyncio.sleep(settings.websocket_heartbeat_interval)
                
                if self.active_connections:
                    heartbeat_message = {
                        "type": "heartbeat",
                        "timestamp": datetime.utcnow().isoformat(),
                        "active_connections": len(self.active_connections)
                    }
                    
                    # Send heartbeat to all connections
                    await self.broadcast_to_all(heartbeat_message)
                
            except Exception as e:
                logger.error(f"Heartbeat error: {e}")
    
    async def disconnect_all(self):
        """Disconnect all WebSocket connections during shutdown."""
        connection_ids = list(self.active_connections.keys())
        
        for connection_id in connection_ids:
            await self.disconnect(connection_id, "server_shutdown")
        
        await self._stop_background_tasks()
    
    def get_connection_stats(self) -> Dict[str, Any]:
        """Get comprehensive WebSocket connection statistics."""
        current_time = datetime.utcnow()
        
        # Calculate connection durations and message rates
        connection_durations = []
        message_rates = []
        
        for conn_info in self.active_connections.values():
            duration = (current_time - conn_info.connect_time).total_seconds()
            connection_durations.append(duration)
            
            if duration > 0:
                rate = conn_info.message_count / (duration / 60)  # Messages per minute
                message_rates.append(rate)
        
        avg_duration = sum(connection_durations) / len(connection_durations) if connection_durations else 0
        avg_message_rate = sum(message_rates) / len(message_rates) if message_rates else 0
        
        return {
            **self.performance_metrics,
            "session_groups": len(self.connection_groups),
            "avg_connection_duration_seconds": round(avg_duration, 2),
            "avg_message_rate_per_minute": round(avg_message_rate, 2),
            "message_latency_ms": round(self.performance_metrics["avg_message_latency"] * 1000, 2)
        }
