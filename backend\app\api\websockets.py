"""
Task 14: WebSocket Implementation - Real-time Updates

WebSocket connection management for real-time processing updates
following the mermaid diagram patterns with:
- Real-time processing updates
- Connection lifecycle management  
- Error handling and reconnection logic
- Message broadcasting to multiple clients
"""

from fastapi import WebSocket, WebSocketDisconnect, WebSocketException
from typing import Dict, List, Any, Optional, Set
import asyncio
import json
import logging
from datetime import datetime
from enum import Enum
import uuid

# Configure logging
logger = logging.getLogger(__name__)

class MessageType(str, Enum):
    """WebSocket message types."""
    CONNECT = "connect"
    DISCONNECT = "disconnect"
    PING = "ping"
    PONG = "pong"
    PROCESSING_UPDATE = "processing_update"
    TASK_UPDATE = "task_update"
    EVENT_UPDATE = "event_update"
    SEARCH_RESULT = "search_result"
    ERROR = "error"
    NOTIFICATION = "notification"

class ProcessingStatus(str, Enum):
    """Processing status for real-time updates."""
    STARTED = "started"
    ANALYZING = "analyzing"
    SEARCHING = "searching"
    GENERATING = "generating"
    COMPLETED = "completed"
    FAILED = "failed"

class WebSocketMessage:
    """WebSocket message structure."""
    
    def __init__(
        self,
        message_type: MessageType,
        data: Any,
        client_id: Optional[str] = None,
        timestamp: Optional[datetime] = None
    ):
        self.id = str(uuid.uuid4())
        self.type = message_type
        self.data = data
        self.client_id = client_id
        self.timestamp = timestamp or datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert message to dictionary for JSON serialization."""
        return {
            "id": self.id,
            "type": self.type.value,
            "data": self.data,
            "client_id": self.client_id,
            "timestamp": self.timestamp.isoformat()
        }
    
    def to_json(self) -> str:
        """Convert message to JSON string."""
        return json.dumps(self.to_dict())

class ConnectionManager:
    """
    WebSocket connection manager with real-time update capabilities.
    
    Features:
    - Multiple client connection management
    - Message broadcasting and targeted messaging
    - Connection lifecycle tracking
    - Automatic reconnection support
    - Error handling and recovery
    """
    
    def __init__(self):
        # Store active connections with client metadata
        self.active_connections: Dict[str, Dict[str, Any]] = {}
        
        # Connection groups for targeted broadcasting
        self.connection_groups: Dict[str, Set[str]] = {}
        
        # Message queue for handling failed sends
        self.message_queue: Dict[str, List[WebSocketMessage]] = {}
        
        # Connection statistics
        self.stats = {
            "total_connections": 0,
            "active_connections": 0,
            "messages_sent": 0,
            "messages_failed": 0,
            "reconnections": 0
        }
        
        logger.info("WebSocket ConnectionManager initialized")

    def generate_client_id(self) -> str:
        """Generate unique client ID."""
        return f"client_{uuid.uuid4().hex[:8]}"

    async def connect(self, websocket: WebSocket, client_id: Optional[str] = None) -> str:
        """
        Accept a WebSocket connection and register the client.
        
        Args:
            websocket: WebSocket connection object
            client_id: Optional client ID (generates one if not provided)
            
        Returns:
            str: Client ID for the connection
        """
        try:
            # Accept the WebSocket connection
            await websocket.accept()
            
            # Generate client ID if not provided
            if not client_id:
                client_id = self.generate_client_id()
            
            # Check if client is reconnecting
            is_reconnection = client_id in self.active_connections
            if is_reconnection:
                logger.info(f"Client {client_id} reconnecting")
                self.stats["reconnections"] += 1
                # Send queued messages if any
                await self._send_queued_messages(client_id, websocket)
            else:
                self.stats["total_connections"] += 1
            
            # Register the connection
            self.active_connections[client_id] = {
                "websocket": websocket,
                "connected_at": datetime.now(),
                "last_ping": datetime.now(),
                "is_alive": True,
                "groups": set()
            }
            
            self.stats["active_connections"] = len(self.active_connections)
            
            # Send connection confirmation
            welcome_message = WebSocketMessage(
                message_type=MessageType.CONNECT,
                data={
                    "client_id": client_id,
                    "connected_at": datetime.now().isoformat(),
                    "is_reconnection": is_reconnection
                },
                client_id=client_id
            )
            
            await self.send_personal_message(welcome_message, client_id)
            
            logger.info(f"Client {client_id} connected successfully")
            return client_id
            
        except Exception as e:
            logger.error(f"Failed to connect client: {e}")
            raise WebSocketException(code=1000, reason=str(e))

    def disconnect(self, client_id: str):
        """
        Disconnect and remove a client.
        
        Args:
            client_id: Client ID to disconnect
        """
        try:
            if client_id in self.active_connections:
                # Remove from all groups
                client_groups = self.active_connections[client_id].get("groups", set())
                for group in client_groups:
                    self.leave_group(client_id, group)
                
                # Remove connection
                del self.active_connections[client_id]
                
                # Clear message queue
                if client_id in self.message_queue:
                    del self.message_queue[client_id]
                
                self.stats["active_connections"] = len(self.active_connections)
                logger.info(f"Client {client_id} disconnected")
            
        except Exception as e:
            logger.error(f"Error disconnecting client {client_id}: {e}")

    async def send_personal_message(
        self, 
        message: WebSocketMessage, 
        client_id: str
    ):
        """
        Send a message to a specific client.
        
        Args:
            message: WebSocketMessage to send
            client_id: Target client ID
        """
        try:
            if client_id in self.active_connections:
                websocket = self.active_connections[client_id]["websocket"]
                await websocket.send_text(message.to_json())
                self.stats["messages_sent"] += 1
                
                # Update last activity
                self.active_connections[client_id]["last_ping"] = datetime.now()
                
        except WebSocketDisconnect:
            logger.info(f"Client {client_id} disconnected during message send")
            self.disconnect(client_id)
            
        except Exception as e:
            logger.error(f"Failed to send message to {client_id}: {e}")
            self.stats["messages_failed"] += 1
            
            # Queue message for retry
            if client_id not in self.message_queue:
                self.message_queue[client_id] = []
            self.message_queue[client_id].append(message)
            
            # Limit queue size
            if len(self.message_queue[client_id]) > 100:
                self.message_queue[client_id] = self.message_queue[client_id][-50:]

    async def broadcast_message(
        self, 
        message: WebSocketMessage, 
        exclude_client: Optional[str] = None
    ):
        """
        Broadcast a message to all connected clients.
        
        Args:
            message: WebSocketMessage to broadcast
            exclude_client: Optional client ID to exclude from broadcast
        """
        disconnected_clients = []
        
        for client_id in list(self.active_connections.keys()):
            if exclude_client and client_id == exclude_client:
                continue
                
            try:
                websocket = self.active_connections[client_id]["websocket"]
                await websocket.send_text(message.to_json())
                self.stats["messages_sent"] += 1
                
            except WebSocketDisconnect:
                disconnected_clients.append(client_id)
                
            except Exception as e:
                logger.error(f"Failed to broadcast to {client_id}: {e}")
                self.stats["messages_failed"] += 1
        
        # Clean up disconnected clients
        for client_id in disconnected_clients:
            self.disconnect(client_id)

    async def broadcast_to_group(
        self, 
        message: WebSocketMessage, 
        group: str, 
        exclude_client: Optional[str] = None
    ):
        """
        Broadcast a message to all clients in a specific group.
        
        Args:
            message: WebSocketMessage to broadcast
            group: Group name
            exclude_client: Optional client ID to exclude
        """
        if group not in self.connection_groups:
            return
            
        disconnected_clients = []
        
        for client_id in list(self.connection_groups[group]):
            if exclude_client and client_id == exclude_client:
                continue
                
            if client_id not in self.active_connections:
                disconnected_clients.append(client_id)
                continue
                
            try:
                websocket = self.active_connections[client_id]["websocket"]
                await websocket.send_text(message.to_json())
                self.stats["messages_sent"] += 1
                
            except WebSocketDisconnect:
                disconnected_clients.append(client_id)
                
            except Exception as e:
                logger.error(f"Failed to broadcast to group {group}, client {client_id}: {e}")
                self.stats["messages_failed"] += 1
        
        # Clean up disconnected clients
        for client_id in disconnected_clients:
            self.leave_group(client_id, group)

    def join_group(self, client_id: str, group: str):
        """
        Add a client to a group for targeted messaging.
        
        Args:
            client_id: Client ID to add
            group: Group name
        """
        if client_id in self.active_connections:
            if group not in self.connection_groups:
                self.connection_groups[group] = set()
            
            self.connection_groups[group].add(client_id)
            self.active_connections[client_id]["groups"].add(group)
            
            logger.debug(f"Client {client_id} joined group {group}")

    def leave_group(self, client_id: str, group: str):
        """
        Remove a client from a group.
        
        Args:
            client_id: Client ID to remove
            group: Group name
        """
        if group in self.connection_groups:
            self.connection_groups[group].discard(client_id)
            
            # Remove empty groups
            if not self.connection_groups[group]:
                del self.connection_groups[group]
        
        if client_id in self.active_connections:
            self.active_connections[client_id]["groups"].discard(group)
        
        logger.debug(f"Client {client_id} left group {group}")

    async def _send_queued_messages(self, client_id: str, websocket: WebSocket):
        """Send queued messages to a reconnected client."""
        if client_id in self.message_queue:
            messages = self.message_queue[client_id].copy()
            del self.message_queue[client_id]
            
            logger.info(f"Sending {len(messages)} queued messages to {client_id}")
            
            for message in messages:
                try:
                    await websocket.send_text(message.to_json())
                    self.stats["messages_sent"] += 1
                except Exception as e:
                    logger.error(f"Failed to send queued message: {e}")
                    break

    async def ping_clients(self):
        """Send ping messages to all clients to keep connections alive."""
        ping_message = WebSocketMessage(
            message_type=MessageType.PING,
            data={"timestamp": datetime.now().isoformat()}
        )
        
        await self.broadcast_message(ping_message)

    async def send_processing_update(
        self, 
        client_id: str, 
        status: ProcessingStatus, 
        details: Dict[str, Any],
        progress: Optional[float] = None
    ):
        """
        Send a processing status update to a client.
        
        Args:
            client_id: Target client ID
            status: Processing status
            details: Additional details about the processing
            progress: Optional progress percentage (0-100)
        """
        message_data = {
            "status": status.value,
            "details": details,
            "timestamp": datetime.now().isoformat()
        }
        
        if progress is not None:
            message_data["progress"] = max(0, min(100, progress))
        
        message = WebSocketMessage(
            message_type=MessageType.PROCESSING_UPDATE,
            data=message_data,
            client_id=client_id
        )
        
        await self.send_personal_message(message, client_id)

    async def send_task_update(
        self, 
        task_data: Dict[str, Any], 
        operation: str = "updated",
        target_group: Optional[str] = None
    ):
        """
        Send a task update notification.
        
        Args:
            task_data: Task data
            operation: Operation type (created, updated, deleted)
            target_group: Optional group to target (otherwise broadcast)
        """
        message = WebSocketMessage(
            message_type=MessageType.TASK_UPDATE,
            data={
                "operation": operation,
                "task": task_data,
                "timestamp": datetime.now().isoformat()
            }
        )
        
        if target_group:
            await self.broadcast_to_group(message, target_group)
        else:
            await self.broadcast_message(message)

    async def send_event_update(
        self, 
        event_data: Dict[str, Any], 
        operation: str = "updated",
        target_group: Optional[str] = None
    ):
        """
        Send an event update notification.
        
        Args:
            event_data: Event data
            operation: Operation type (created, updated, deleted)
            target_group: Optional group to target
        """
        message = WebSocketMessage(
            message_type=MessageType.EVENT_UPDATE,
            data={
                "operation": operation,
                "event": event_data,
                "timestamp": datetime.now().isoformat()
            }
        )
        
        if target_group:
            await self.broadcast_to_group(message, target_group)
        else:
            await self.broadcast_message(message)

    async def send_search_result(
        self, 
        client_id: str, 
        query: str, 
        results: List[Dict[str, Any]], 
        search_type: str = "hybrid"
    ):
        """
        Send search results to a client.
        
        Args:
            client_id: Target client ID
            query: Search query
            results: Search results
            search_type: Type of search performed
        """
        message = WebSocketMessage(
            message_type=MessageType.SEARCH_RESULT,
            data={
                "query": query,
                "search_type": search_type,
                "results": results,
                "count": len(results),
                "timestamp": datetime.now().isoformat()
            },
            client_id=client_id
        )
        
        await self.send_personal_message(message, client_id)

    async def send_error(
        self, 
        client_id: str, 
        error_message: str, 
        error_code: Optional[str] = None,
        error_details: Optional[Dict[str, Any]] = None
    ):
        """
        Send an error message to a client.
        
        Args:
            client_id: Target client ID
            error_message: Error message
            error_code: Optional error code
            error_details: Optional additional error details
        """
        message_data = {
            "message": error_message,
            "timestamp": datetime.now().isoformat()
        }
        
        if error_code:
            message_data["code"] = error_code
            
        if error_details:
            message_data["details"] = error_details
        
        message = WebSocketMessage(
            message_type=MessageType.ERROR,
            data=message_data,
            client_id=client_id
        )
        
        await self.send_personal_message(message, client_id)

    async def send_notification(
        self, 
        title: str, 
        message: str, 
        notification_type: str = "info",
        target_client: Optional[str] = None,
        target_group: Optional[str] = None
    ):
        """
        Send a notification message.
        
        Args:
            title: Notification title
            message: Notification message
            notification_type: Type of notification (info, success, warning, error)
            target_client: Optional specific client to notify
            target_group: Optional group to notify
        """
        notification_message = WebSocketMessage(
            message_type=MessageType.NOTIFICATION,
            data={
                "title": title,
                "message": message,
                "type": notification_type,
                "timestamp": datetime.now().isoformat()
            }
        )
        
        if target_client:
            await self.send_personal_message(notification_message, target_client)
        elif target_group:
            await self.broadcast_to_group(notification_message, target_group)
        else:
            await self.broadcast_message(notification_message)

    def get_connection_stats(self) -> Dict[str, Any]:
        """Get connection statistics."""
        return {
            **self.stats,
            "groups": {
                group: len(clients) for group, clients in self.connection_groups.items()
            },
            "queued_messages": {
                client_id: len(messages) 
                for client_id, messages in self.message_queue.items()
            },
            "active_clients": list(self.active_connections.keys())
        }

    async def cleanup_inactive_connections(self, timeout_minutes: int = 30):
        """
        Clean up inactive connections.
        
        Args:
            timeout_minutes: Minutes of inactivity before cleanup
        """
        cutoff_time = datetime.now().timestamp() - (timeout_minutes * 60)
        inactive_clients = []
        
        for client_id, conn_data in self.active_connections.items():
            last_ping = conn_data.get("last_ping", conn_data["connected_at"])
            if last_ping.timestamp() < cutoff_time:
                inactive_clients.append(client_id)
        
        for client_id in inactive_clients:
            logger.info(f"Cleaning up inactive client: {client_id}")
            self.disconnect(client_id)

# Global connection manager instance
connection_manager = ConnectionManager()


# =============================================================================
# WEBSOCKET ENDPOINT HANDLERS
# =============================================================================

async def handle_websocket_message(
    websocket: WebSocket, 
    client_id: str, 
    message_data: Dict[str, Any]
):
    """
    Handle incoming WebSocket messages from clients.
    
    Args:
        websocket: WebSocket connection
        client_id: Client ID
        message_data: Parsed message data
    """
    try:
        message_type = message_data.get("type")
        data = message_data.get("data", {})
        
        if message_type == MessageType.PING.value:
            # Respond to ping with pong
            pong_message = WebSocketMessage(
                message_type=MessageType.PONG,
                data={"timestamp": datetime.now().isoformat()},
                client_id=client_id
            )
            await connection_manager.send_personal_message(pong_message, client_id)
            
        elif message_type == "join_group":
            group_name = data.get("group")
            if group_name:
                connection_manager.join_group(client_id, group_name)
                
        elif message_type == "leave_group":
            group_name = data.get("group")
            if group_name:
                connection_manager.leave_group(client_id, group_name)
                
        else:
            logger.warning(f"Unknown message type from {client_id}: {message_type}")
            
    except Exception as e:
        logger.error(f"Error handling message from {client_id}: {e}")
        await connection_manager.send_error(
            client_id, 
            f"Error processing message: {str(e)}",
            "MESSAGE_PROCESSING_ERROR"
        )


# =============================================================================
# BACKGROUND TASKS
# =============================================================================

async def websocket_heartbeat_task():
    """Background task to send periodic heartbeat pings."""
    while True:
        try:
            await connection_manager.ping_clients()
            await asyncio.sleep(30)  # Ping every 30 seconds
        except Exception as e:
            logger.error(f"Heartbeat task error: {e}")
            await asyncio.sleep(60)  # Wait longer on error

async def websocket_cleanup_task():
    """Background task to clean up inactive connections."""
    while True:
        try:
            await connection_manager.cleanup_inactive_connections()
            await asyncio.sleep(300)  # Clean up every 5 minutes
        except Exception as e:
            logger.error(f"Cleanup task error: {e}")
            await asyncio.sleep(600)  # Wait longer on error
