import React from 'react'
import { NavLink, useLocation } from 'react-router-dom'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  LayoutDashboard,
  CheckSquare,
  Calendar,
  Search,
  Settings,
  ChevronLeft,
  ChevronRight,
  Sparkles,
  Activity,
  Bell,
  User
} from 'lucide-react'
import { useAppStore } from '@/stores/appStore'
import clsx from 'clsx'

/**
 * Navigation menu items
 */
const navigationItems = [
  {
    name: 'Dashboard',
    href: '/dashboard',
    icon: LayoutDashboard,
    description: 'AI-powered overview'
  },
  {
    name: 'Tasks',
    href: '/tasks',
    icon: CheckSquare,
    description: 'Intelligent task management'
  },
  {
    name: 'Events',
    href: '/events',
    icon: Calendar,
    description: 'Smart calendar integration'
  },
  {
    name: 'Search',
    href: '/search',
    icon: Search,
    description: 'Semantic & web search'
  },
  {
    name: 'Settings',
    href: '/settings',
    icon: Settings,
    description: 'Preferences & configuration'
  },
]

/**
 * Fixed sidebar with navigation following research/react patterns
 * 
 * Features:
 * - Framer Motion animations for menu items
 * - Active state management
 * - Responsive design with collapse functionality
 * - Following research/framer-motion patterns for smooth animations
 */
const Sidebar: React.FC = () => {
  const { sidebarCollapsed, toggleSidebar } = useAppStore()
  const location = useLocation()

  return (
    <motion.div
      initial={false}
      animate={{
        width: sidebarCollapsed ? '4rem' : '16rem',
      }}
      transition={{
        type: 'spring',
        stiffness: 300,
        damping: 30,
      }}
      className="fixed left-0 top-0 h-full bg-background-secondary border-r border-border-primary z-40 flex flex-col"
    >
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-border-primary">
        <AnimatePresence>
          {!sidebarCollapsed && (
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.2 }}
              className="flex items-center gap-3"
            >
              <div className="w-8 h-8 bg-gradient-to-br from-accent-blue to-accent-purple rounded-xl flex items-center justify-center">
                <Sparkles className="w-5 h-5 text-white" />
              </div>
              <div>
                <h1 className="font-bold text-text-primary text-lg">AI Dashboard</h1>
                <p className="text-text-muted text-xs">Intelligent Assistant</p>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Collapse button */}
        <motion.button
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
          onClick={toggleSidebar}
          className="w-8 h-8 bg-background-hover hover:bg-background-active rounded-lg flex items-center justify-center text-text-secondary hover:text-text-primary transition-colors duration-200"
        >
          {sidebarCollapsed ? (
            <ChevronRight className="w-4 h-4" />
          ) : (
            <ChevronLeft className="w-4 h-4" />
          )}
        </motion.button>
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-4 space-y-2">
        <AnimatePresence>
          {!sidebarCollapsed && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              transition={{ delay: 0.1 }}
              className="text-text-muted text-xs font-medium uppercase tracking-wider mb-4"
            >
              Navigation
            </motion.div>
          )}
        </AnimatePresence>

        {navigationItems.map((item, index) => {
          const isActive = location.pathname === item.href
          
          return (
            <NavLink
              key={item.name}
              to={item.href}
              className={clsx(
                'block w-full rounded-xl transition-all duration-200 group relative',
                'hover:bg-background-hover active:bg-background-active',
                isActive && 'bg-accent-blue/10 border border-accent-blue/20'
              )}
            >
              <motion.div
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.05 }}
                whileHover={{ x: 2 }}
                className={clsx(
                  'flex items-center gap-3 p-3 rounded-xl',
                  sidebarCollapsed && 'justify-center'
                )}
              >
                {/* Icon */}
                <div className={clsx(
                  'flex items-center justify-center w-6 h-6 transition-colors duration-200',
                  isActive ? 'text-accent-blue' : 'text-text-secondary group-hover:text-text-primary'
                )}>
                  <item.icon className="w-5 h-5" />
                </div>

                {/* Label and description */}
                <AnimatePresence>
                  {!sidebarCollapsed && (
                    <motion.div
                      initial={{ opacity: 0, width: 0 }}
                      animate={{ opacity: 1, width: 'auto' }}
                      exit={{ opacity: 0, width: 0 }}
                      transition={{ duration: 0.2 }}
                      className="flex-1 min-w-0"
                    >
                      <div className={clsx(
                        'font-medium text-sm transition-colors duration-200',
                        isActive ? 'text-text-primary' : 'text-text-secondary group-hover:text-text-primary'
                      )}>
                        {item.name}
                      </div>
                      <div className="text-text-muted text-xs truncate">
                        {item.description}
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>

                {/* Active indicator */}
                {isActive && (
                  <motion.div
                    layoutId="activeIndicator"
                    className="absolute right-2 w-2 h-2 bg-accent-blue rounded-full"
                    transition={{ type: 'spring', stiffness: 300, damping: 30 }}
                  />
                )}
              </motion.div>

              {/* Tooltip for collapsed state */}
              {sidebarCollapsed && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.8, x: -10 }}
                  whileHover={{ opacity: 1, scale: 1, x: 0 }}
                  transition={{ duration: 0.2 }}
                  className="absolute left-full ml-2 top-1/2 -translate-y-1/2 bg-background-tertiary border border-border-primary rounded-lg px-3 py-2 shadow-medium z-50 pointer-events-none group-hover:pointer-events-auto"
                >
                  <div className="text-text-primary text-sm font-medium">
                    {item.name}
                  </div>
                  <div className="text-text-muted text-xs">
                    {item.description}
                  </div>
                </motion.div>
              )}
            </NavLink>
          )
        })}
      </nav>

      {/* Status section */}
      <div className="p-4 border-t border-border-primary">
        {/* Connection status */}
        <div className={clsx(
          'flex items-center gap-3 p-3 rounded-xl bg-background-hover/50',
          sidebarCollapsed && 'justify-center'
        )}>
          <div className="w-2 h-2 bg-accent-green rounded-full animate-pulse" />
          
          <AnimatePresence>
            {!sidebarCollapsed && (
              <motion.div
                initial={{ opacity: 0, width: 0 }}
                animate={{ opacity: 1, width: 'auto' }}
                exit={{ opacity: 0, width: 0 }}
                transition={{ duration: 0.2 }}
                className="flex-1"
              >
                <div className="text-text-secondary text-xs font-medium">
                  AI Connected
                </div>
                <div className="text-text-muted text-xs">
                  Real-time processing active
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        {/* Quick actions (when expanded) */}
        <AnimatePresence>
          {!sidebarCollapsed && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.2, delay: 0.1 }}
              className="mt-3 flex gap-2"
            >
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="flex-1 p-2 bg-background-hover hover:bg-background-active rounded-lg text-text-secondary hover:text-text-primary transition-colors duration-200"
                title="Activity"
              >
                <Activity className="w-4 h-4 mx-auto" />
              </motion.button>
              
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="flex-1 p-2 bg-background-hover hover:bg-background-active rounded-lg text-text-secondary hover:text-text-primary transition-colors duration-200"
                title="Notifications"
              >
                <Bell className="w-4 h-4 mx-auto" />
              </motion.button>
              
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="flex-1 p-2 bg-background-hover hover:bg-background-active rounded-lg text-text-secondary hover:text-text-primary transition-colors duration-200"
                title="Profile"
              >
                <User className="w-4 h-4 mx-auto" />
              </motion.button>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </motion.div>
  )
}

export default Sidebar
