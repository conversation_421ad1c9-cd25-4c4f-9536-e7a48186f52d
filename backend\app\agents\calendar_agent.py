"""
Specialized agent for calendar event extraction and processing.

This agent handles inputs categorized as events, extracts date/time information,
and processes them using Mirascope patterns with calendar management tools.
Follows research/mirascope/page7-agents.md for state management.
"""

import asyncio
import logging
from typing import List, Dict, Any, Optional, AsyncGenerator
from datetime import datetime, timedelta
import re
from dateutil import parser as date_parser
from dateutil.relativedelta import relativedelta

from mirascope import llm
from pydantic import ValidationError

from app.models.ai_responses import AIEventExtractionResponse, AIProcessingError
from app.models.pydantic_models import (
    UserInput, EventData, EventStatus, ProcessingState, AnimationStep
)
from app.config.settings import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()


class CalendarAgent:
    """
    Specialized agent for processing event and calendar-related inputs.
    
    This agent extracts event details, parses dates and times,
    and integrates with calendar management tools.
    """
    
    def __init__(self):
        self.model = settings.primary_llm_model
        self.fallback_model = settings.ai_fallback_model
        self.max_retries = settings.ai_max_retries
        self.event_history: List[Dict[str, Any]] = []
        
        # Common time patterns for parsing
        self.time_patterns = [
            r'\\b([01]?[0-9]|2[0-3]):([0-5][0-9])\\s*(am|pm|AM|PM)?\\b',
            r'\\b([01]?[0-9])\\s*(am|pm|AM|PM)\\b',
            r'\\b(noon|midnight|morning|afternoon|evening|night)\\b',
            r'\\bat\\s+([01]?[0-9]|2[0-3])\\b',
        ]
        
        # Date reference patterns
        self.date_references = {
            'today': 0,
            'tomorrow': 1,
            'yesterday': -1,
            'next week': 7,
            'next month': 30,
            'this week': 0,
            'this month': 0,
        }
    
    async def process_event(
        self,
        user_input: UserInput,
        context: Optional[Dict[str, Any]] = None
    ) -> EventData:
        """
        Process a user input identified as an event.
        
        Args:
            user_input: The user's event input
            context: Optional context from categorization
            
        Returns:
            EventData: Extracted and processed event information
        """
        try:
            logger.info(f"Processing event: {user_input.text[:100]}...")
            
            # Extract event information with AI
            extraction_result = await self._extract_event_with_retry(user_input, context)
            
            # Create EventData from extraction results
            event_data = EventData(
                title=extraction_result.title,
                description=extraction_result.description,
                location=extraction_result.location,
                start_datetime=extraction_result.parsed_start_time or self._parse_fallback_time(user_input.text),
                end_datetime=extraction_result.parsed_end_time,
                all_day=extraction_result.all_day,
                event_type=extraction_result.event_type,
                reminder_minutes=extraction_result.suggested_reminder_minutes,
                status=EventStatus.SCHEDULED,
                ai_confidence=extraction_result.date_confidence,
                ai_extracted_datetime={
                    "raw_start": extraction_result.extracted_start_time,
                    "raw_end": extraction_result.extracted_end_time,
                    "all_day": extraction_result.all_day,
                    "parsing_confidence": extraction_result.date_confidence
                },
                ai_conflicts=extraction_result.potential_conflicts
            )
            
            # Check for scheduling conflicts
            await self._check_conflicts(event_data)
            
            # Store in processing history
            self.event_history.append({
                "input": user_input.text,
                "event_title": event_data.title,
                "event_type": event_data.event_type,
                "start_time": event_data.start_datetime,
                "timestamp": datetime.now(),
                "confidence": event_data.ai_confidence
            })
            
            # Keep history manageable
            if len(self.event_history) > 20:
                self.event_history.pop(0)
            
            logger.info(f"Event processed: {event_data.title} at {event_data.start_datetime}")
            return event_data
            
        except Exception as e:
            logger.error(f"Error processing event: {e}")
            
            # Return minimal event with error info
            return EventData(
                title=self._extract_simple_title(user_input.text),
                description=f"Error during processing: {str(e)}",
                start_datetime=datetime.now() + timedelta(hours=1),  # Default to 1 hour from now
                event_type="general",
                ai_confidence=0.1,
                ai_extracted_datetime={"error": str(e)}
            )
    
    async def _extract_event_with_retry(
        self,
        user_input: UserInput,
        context: Optional[Dict[str, Any]] = None
    ) -> AIEventExtractionResponse:
        """
        Extract event information with retry logic.
        
        Args:
            user_input: User input to process
            context: Optional context
            
        Returns:
            AIEventExtractionResponse: Structured event extraction result
        """
        last_error = None
        
        # Try primary model
        for attempt in range(self.max_retries):
            try:
                logger.debug(f"Event extraction attempt {attempt + 1}/{self.max_retries}")
                return await self._perform_event_extraction(user_input, context, self.model)
                
            except Exception as e:
                last_error = e
                logger.warning(f"Event extraction attempt {attempt + 1} failed: {e}")
                
                if attempt < self.max_retries - 1:
                    await asyncio.sleep(0.5 * (attempt + 1))
        
        # Try fallback model
        try:
            logger.info(f"Trying fallback model for event extraction")
            return await self._perform_event_extraction(user_input, context, self.fallback_model)
            
        except Exception as e:
            logger.error(f"Fallback event extraction failed: {e}")
            raise AIProcessingError(
                error_type="event_extraction_failed",
                error_message=f"Event extraction failed: {str(last_error)}",
                error_code="EVENT_EXTRACTION_FAILED",
                operation="event_processing",
                input_data=user_input.text,
                recoverable=True,
                suggested_action="Please provide specific date and time details",
                model_used=self.model
            )
    
    @llm.call(provider="openrouter", response_model=AIEventExtractionResponse, json_mode=True)
    async def _perform_event_extraction(
        self,
        user_input: UserInput,
        context: Optional[Dict[str, Any]] = None,
        model: str = None
    ) -> str:
        """
        Mirascope call for event extraction using structured response model.
        """
        
        # Build context from event history
        context_str = ""
        if self.event_history:
            recent_events = self.event_history[-5:]  # Last 5 events
            context_str = "\\n".join([
                f"Recent event: '{event['event_title']}' [{event['event_type']}] at {event['start_time'].strftime('%Y-%m-%d %H:%M')}"
                for event in recent_events
            ])
            context_str = f"\\n\\nRecent event patterns:\\n{context_str}"
        
        # Add current date/time context
        now = datetime.now()
        context_str += f"\\n\\nCurrent date/time: {now.strftime('%A, %B %d, %Y at %I:%M %p')}"
        
        # Add provided context
        if context:
            context_str += f"\\n\\nCategorization context: {context}"
        
        return f"""You are an expert calendar event extraction agent. Analyze the user's input and extract all relevant event information with precise date/time parsing.

INPUT TO ANALYZE: "{user_input.text}"

CONTEXT: {context_str}

EXTRACTION RULES:
1. **Title**: Create a clear, descriptive event title
2. **Date/Time Parsing**: Be extremely careful with date and time extraction
3. **Event Type**: Categorize based on content (meeting, appointment, reminder, etc.)
4. **Location**: Extract any location mentions
5. **Duration**: Determine if end time is specified or can be inferred

DATE/TIME PARSING GUIDELINES:
- "tomorrow at 2pm" = next day at 14:00
- "next Tuesday" = the Tuesday of next week
- "in 2 hours" = 2 hours from current time
- "this Friday at noon" = this week's Friday at 12:00
- Be conservative with confidence if times are ambiguous
- Default to business hours (9am-5pm) if no time specified
- Use 24-hour format for parsing

EVENT TYPE EXAMPLES:
- meeting, appointment, call, presentation, interview, deadline
- social, personal, work, medical, travel, entertainment
- reminder, follow-up, check-in, review, planning

CONFLICT CHECKING:
- Consider overlapping times
- Account for travel time between locations
- Flag back-to-back meetings

REMINDER SUGGESTIONS:
- meetings: 15 minutes
- appointments: 30 minutes  
- deadlines: 1 day
- social events: 2 hours

CLARIFICATION NEEDS:
- Ambiguous dates ("next week" without specific day)
- Missing times ("meeting tomorrow" without time)
- Unclear duration (no end time specified)

Respond with comprehensive JSON containing extracted information, parsed dates, confidence scores, and detailed reasoning for all decisions."""
    
    async def stream_event_processing(
        self,
        user_input: UserInput,
        context: Optional[Dict[str, Any]] = None
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Stream event processing with real-time visual feedback.
        
        Args:
            user_input: User input to process
            context: Optional context
            
        Yields:
            Dict[str, Any]: Processing updates for frontend animations
        """
        try:
            processing_steps = [
                AnimationStep(
                    id="parsing",
                    message="Parsing event details...",
                    animation_type="typewriter",
                    duration=1.0
                ),
                AnimationStep(
                    id="datetime_extraction",
                    message="Extracting date and time...",
                    animation_type="pulse",
                    duration=2.0,
                    delay=1.0
                ),
                AnimationStep(
                    id="validation",
                    message="Validating event information...",
                    animation_type="spin",
                    duration=1.5,
                    delay=3.0
                ),
                AnimationStep(
                    id="conflict_check",
                    message="Checking for conflicts...",
                    animation_type="fade",
                    duration=1.2,
                    delay=4.5
                ),
                AnimationStep(
                    id="finalizing",
                    message="Creating calendar event...",
                    animation_type="bounce",
                    duration=0.8,
                    delay=5.7
                )
            ]
            
            # Stream processing steps
            for i, step in enumerate(processing_steps):
                yield {
                    "type": "event_processing_update",
                    "data": {
                        "step": step.dict(),
                        "progress": i / len(processing_steps),
                        "message": step.message
                    }
                }
                
                if i < len(processing_steps) - 1:
                    await asyncio.sleep(step.delay + step.duration)
            
            # Perform actual event processing
            result = await self.process_event(user_input, context)
            
            # Yield final result
            yield {
                "type": "event_processing_complete",
                "data": {
                    "event": result.dict(),
                    "conflicts": result.ai_conflicts,
                    "confidence": result.ai_confidence,
                    "needs_clarification": len(result.ai_conflicts or []) > 0
                }
            }
            
        except Exception as e:
            logger.error(f"Error in stream_event_processing: {e}")
            yield {
                "type": "error",
                "data": {
                    "error_message": str(e),
                    "error_type": "event_processing_error"
                }
            }
    
    async def _check_conflicts(self, event_data: EventData):
        """
        Check for potential scheduling conflicts.
        
        Args:
            event_data: Event data to check for conflicts
        """
        try:
            conflicts = []
            
            # Check against recent events for overlaps
            for history_event in self.event_history[-10:]:  # Check last 10 events
                hist_start = history_event["start_time"]
                
                # Simple overlap check (assumes 1-hour default duration)
                event_end = event_data.end_datetime or (event_data.start_datetime + timedelta(hours=1))
                hist_end = hist_start + timedelta(hours=1)
                
                # Check for overlap
                if (event_data.start_datetime < hist_end and event_end > hist_start):
                    conflicts.append(f"Overlaps with '{history_event['event_title']}' at {hist_start.strftime('%I:%M %p')}")
            
            # Check for back-to-back meetings
            for history_event in self.event_history[-5:]:
                hist_start = history_event["start_time"]
                time_diff = abs((event_data.start_datetime - hist_start).total_seconds() / 60)
                
                if 0 < time_diff <= 15:  # Within 15 minutes
                    conflicts.append(f"Very close to '{history_event['event_title']}' - consider travel time")
            
            # Update event data with conflicts
            if conflicts:
                if not event_data.ai_conflicts:
                    event_data.ai_conflicts = []
                event_data.ai_conflicts.extend(conflicts)
                
        except Exception as e:
            logger.error(f"Error checking conflicts: {e}")
    
    def _parse_fallback_time(self, text: str) -> datetime:
        """
        Fallback time parsing when AI fails.
        
        Args:
            text: Input text
            
        Returns:
            datetime: Parsed or default datetime
        """
        try:
            # Try simple date parsing
            return date_parser.parse(text, fuzzy=True, default=datetime.now() + timedelta(hours=1))
        except:
            # Default to 1 hour from now
            return datetime.now() + timedelta(hours=1)
    
    def _extract_simple_title(self, text: str) -> str:
        """
        Extract a simple title from text as fallback.
        
        Args:
            text: Input text
            
        Returns:
            str: Simple extracted title
        """
        # Clean and truncate text for title
        title = re.sub(r'[^\\w\\s-]', '', text).strip()
        title = ' '.join(title.split()[:6])  # Max 6 words for events
        
        if not title:
            return "Event from input"
            
        return title[0].upper() + title[1:] if len(title) > 1 else title.upper()
    
    async def suggest_event_optimizations(
        self,
        event_data: EventData
    ) -> Dict[str, Any]:
        """
        Suggest optimizations for an event.
        
        Args:
            event_data: Event data to optimize
            
        Returns:
            Dict[str, Any]: Optimization suggestions
        """
        try:
            suggestions = {
                "scheduling": [],
                "details": [],
                "preparation": []
            }
            
            # Scheduling suggestions
            if not event_data.end_datetime:
                suggestions["scheduling"].append("Consider specifying an end time for better scheduling")
            
            if event_data.start_datetime.minute not in [0, 15, 30, 45]:
                suggestions["scheduling"].append("Consider rounding to quarter-hour for easier scheduling")
            
            # Detail suggestions
            if not event_data.location:
                suggestions["details"].append("Adding a location can help with time management")
            
            if not event_data.description:
                suggestions["details"].append("A brief description can help prepare for the event")
            
            # Preparation suggestions
            if event_data.event_type in ["meeting", "interview", "presentation"]:
                time_until = event_data.start_datetime - datetime.now()
                if time_until.total_seconds() > 0:
                    if time_until.days >= 1:
                        suggestions["preparation"].append("Consider preparing agenda or materials in advance")
                    elif time_until.seconds < 3600:  # Less than 1 hour
                        suggestions["preparation"].append("Event starting soon - time to prepare!")
            
            return suggestions
            
        except Exception as e:
            logger.error(f"Error suggesting event optimizations: {e}")
            return {"error": str(e)}
    
    def get_event_stats(self) -> Dict[str, Any]:
        """Get event processing statistics."""
        if not self.event_history:
            return {"total_processed": 0, "event_types": {}, "times": {}}
        
        event_types = {}
        hours = {}
        
        for event in self.event_history:
            event_type = event["event_type"]
            hour = event["start_time"].hour
            
            event_types[event_type] = event_types.get(event_type, 0) + 1
            hours[hour] = hours.get(hour, 0) + 1
        
        # Find most common meeting time
        most_common_hour = max(hours.keys(), key=lambda k: hours[k]) if hours else 9
        
        return {
            "total_processed": len(self.event_history),
            "event_types": event_types,
            "most_common_hour": most_common_hour,
            "avg_confidence": sum(e["confidence"] for e in self.event_history) / len(self.event_history),
            "last_processed": self.event_history[-1]["timestamp"].isoformat(),
            "upcoming_count": sum(1 for e in self.event_history if e["start_time"] > datetime.now())
        }


# Global calendar agent instance
_calendar_agent = None


def get_calendar_agent() -> CalendarAgent:
    """Get the global calendar agent instance (singleton pattern)."""
    global _calendar_agent
    if _calendar_agent is None:
        _calendar_agent = CalendarAgent()
    return _calendar_agent


# Export main components
__all__ = ["CalendarAgent", "get_calendar_agent"]
