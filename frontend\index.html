<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>AI-Powered Dashboard</title>
    <meta name="description" content="AI-Powered Dashboard with Visual Transparency & Smooth Animations" />
    
    <!-- Preload fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet">
    
    <!-- Theme color for browsers -->
    <meta name="theme-color" content="#131416" />
    
    <!-- Prevent flash of unstyled content -->
    <style>
      #root {
        display: contents;
      }
      
      /* Loading screen */
      .app-loading {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: #131416;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
      }
      
      .app-loading .spinner {
        width: 40px;
        height: 40px;
        border: 3px solid #35353a;
        border-top: 3px solid #4285f4;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    </style>
  </head>
  <body>
    <div id="root">
      <!-- Loading screen shown while React loads -->
      <div class="app-loading">
        <div class="spinner"></div>
      </div>
    </div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
