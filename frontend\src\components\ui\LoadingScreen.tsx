import React from 'react'
import { motion } from 'framer-motion'

/**
 * Props for the LoadingScreen component
 */
interface LoadingScreenProps {
  message?: string
  showProgress?: boolean
  progress?: number
}

/**
 * Loading screen component with smooth animations
 */
const LoadingScreen: React.FC<LoadingScreenProps> = ({ 
  message = 'Loading...', 
  showProgress = false, 
  progress = 0 
}) => {
  return (
    <div className="fixed inset-0 z-50 bg-background-primary flex items-center justify-center">
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.3 }}
        className="text-center max-w-sm mx-4"
      >
        {/* Loading spinner */}
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
          className="w-16 h-16 border-4 border-border-primary border-t-accent-blue rounded-full mx-auto mb-6"
        />
        
        {/* Loading message */}
        <motion.h2
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="text-xl font-semibold text-text-primary mb-2"
        >
          {message}
        </motion.h2>
        
        {/* Progress bar */}
        {showProgress && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="w-full max-w-xs mx-auto"
          >
            <div className="bg-background-secondary rounded-full h-2 overflow-hidden">
              <motion.div
                initial={{ width: 0 }}
                animate={{ width: `${progress}%` }}
                transition={{ duration: 0.3 }}
                className="h-full bg-accent-blue rounded-full"
              />
            </div>
            <div className="text-text-secondary text-sm mt-2">
              {Math.round(progress)}%
            </div>
          </motion.div>
        )}
        
        {/* AI thinking animation */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.3 }}
          className="flex justify-center items-center gap-1 mt-4"
        >
          {[0, 1, 2].map((i) => (
            <motion.div
              key={i}
              animate={{ scale: [1, 1.2, 1] }}
              transition={{
                duration: 1,
                repeat: Infinity,
                delay: i * 0.2,
              }}
              className="w-2 h-2 bg-accent-blue rounded-full"
            />
          ))}
        </motion.div>
      </motion.div>
    </div>
  )
}

export default LoadingScreen
