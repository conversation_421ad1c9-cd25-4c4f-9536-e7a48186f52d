import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { SearchResults } from './SearchResults.tsx'
import { SourceLinks } from './SourceLinks.tsx'
import { AIResponse, SourceLink } from '../../types/index.ts'

// Flexible interface for testing and real usage
interface AIResponsePanelProps {
  response?: AIResponse | {
    type: 'text' | 'search' | 'knowledge' | 'analysis' | 'code' | 'summary' | 'creative' | 'reasoning'
    content: string
    sources?: string[] | SourceLink[]
    searchResults?: Array<{
      title: string
      snippet: string
      url: string
      relevance: number
    }>
    confidence?: number
  }
  isStreaming?: boolean
  onCopy?: (content: string) => void
  onRefine?: (query: string) => void
  onSourceClick?: (url: string) => void
  className?: string
}

/**
 * AIResponsePanel Component
 * PATTERN: Dynamic response display based on question type
 * Features:
 * - Typewriter effect for text responses
 * - Search results with source attribution  
 * - Expandable/collapsible sections
 * - Copy to clipboard functionality
 * - Confidence indicators
 */
export function AIResponsePanel({ 
  response, 
  isStreaming = false, 
  onCopy, 
  onRefine, 
  onSourceClick,
  className = ''
}: AIResponsePanelProps) {
  const [displayedText, setDisplayedText] = useState('')
  const [isExpanded, setIsExpanded] = useState(true)
  const [showSources, setShowSources] = useState(false)

  // Type guard to check if response has searchResults
  const hasSearchResults = (response: any): response is { searchResults: Array<any> } => {
    return response && 'searchResults' in response && Array.isArray(response.searchResults)
  }

  // Type guard to check if sources are SourceLink[] or string[]
  const isSourceLinkArray = (sources: any): sources is SourceLink[] => {
    return Array.isArray(sources) && sources.length > 0 && typeof sources[0] === 'object' && 'url' in sources[0]
  }

  // Use callback functions if provided
  const handleRefine = () => onRefine && onRefine('');
  const handleSourceClick = (source: any) => onSourceClick && onSourceClick(source);
  const componentClassName = `ai-response-panel ${className}`.trim();

  // Typewriter effect for text responses
  useEffect(() => {
    if (response?.type === 'text' && response.content) {
      setDisplayedText('')
      let index = 0
      const content = response.content

      const typeWriter = setInterval(() => {
        if (index < content.length) {
          setDisplayedText(content.slice(0, index + 1))
          index++
        } else {
          clearInterval(typeWriter)
        }
      }, 20) // Adjust speed as needed

      return () => clearInterval(typeWriter)
    } else if (response?.content) {
      setDisplayedText(response.content)
    }
  }, [response])

  const handleCopy = () => {
    if (response?.content && onCopy) {
      onCopy(response.content)
    }
  }

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'text-green-600 bg-green-100'
    if (confidence >= 0.6) return 'text-yellow-600 bg-yellow-100'
    return 'text-red-600 bg-red-100'
  }

  const getConfidenceLabel = (confidence: number) => {
    if (confidence >= 0.8) return 'High Confidence'
    if (confidence >= 0.6) return 'Medium Confidence'
    return 'Low Confidence'
  }

  if (isStreaming) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
      >
        <div className="flex items-center gap-3">
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
            className="w-6 h-6 border-2 border-blue-600 border-t-transparent rounded-full"
          />
          <span className="text-gray-600">AI is thinking...</span>
        </div>
      </motion.div>
    )
  }

  if (!response) {
    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        className="bg-gray-50 rounded-lg border border-gray-200 p-8 text-center"
      >
        <div className="text-gray-400 mb-2">
          <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M8.625 12a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H8.25m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H12m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0h-.375M21 12c0 4.556-4.03 8.25-9 8.25a9.764 9.764 0 01-2.555-.337A5.972 5.972 0 015.41 20.97a5.969 5.969 0 01-.474-.065 4.48 4.48 0 00.978-2.025c.09-.457-.133-.901-.467-1.226C3.93 16.178 3 14.189 3 12c0-4.556 4.03-8.25 9-8.25s9 3.694 9 8.25z" />
          </svg>
        </div>
        <p className="text-gray-500">Ask me anything and I'll help you find the answer!</p>
      </motion.div>
    )
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={`bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden ${componentClassName}`}
    >
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              {response.type === 'search' ? (
                <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              ) : response.type === 'knowledge' ? (
                <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                </svg>
              ) : (
                <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                </svg>
              )}
            </div>
            
            <div>
              <h3 className="font-semibold text-gray-900">AI Response</h3>
              <p className="text-sm text-gray-600 capitalize">
                {response.type === 'search' ? 'Web Search Results' : 
                 response.type === 'knowledge' ? 'Knowledge Base' : 'Direct Answer'}
              </p>
            </div>
          </div>

          <div className="flex items-center gap-2">
            {/* Confidence Indicator */}
            {response.confidence && (
              <motion.span
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.2 }}
                className={`px-2 py-1 rounded-full text-xs font-medium ${getConfidenceColor(response.confidence)}`}
              >
                {getConfidenceLabel(response.confidence)}
              </motion.span>
            )}

            {/* Expand/Collapse Button */}
            <motion.button
              onClick={() => setIsExpanded(!isExpanded)}
              className="p-2 rounded-md hover:bg-gray-200 transition-colors"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <motion.svg
                animate={{ rotate: isExpanded ? 180 : 0 }}
                transition={{ duration: 0.2 }}
                className="w-4 h-4 text-gray-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </motion.svg>
            </motion.button>
          </div>
        </div>
      </div>

      {/* Content */}
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
            className="p-6"
          >
            {/* Main Response Content */}
            <div className="prose prose-sm max-w-none">
              {response.type === 'text' ? (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="text-gray-800 leading-relaxed"
                >
                  {displayedText}
                  {displayedText.length < response.content.length && (
                    <motion.span
                      animate={{ opacity: [1, 0] }}
                      transition={{ duration: 0.5, repeat: Infinity }}
                      className="inline-block w-0.5 h-4 bg-blue-600 ml-1"
                    />
                  )}
                </motion.div>
              ) : (
                <div className="text-gray-800 leading-relaxed">
                  {response.content}
                </div>
              )}
            </div>

            {/* Search Results */}
            {hasSearchResults(response) && response.searchResults.length > 0 && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
                className="mt-6"
              >
                <SearchResults results={response.searchResults} />
              </motion.div>
            )}

            {/* Sources */}
            {response.sources && response.sources.length > 0 && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
                className="mt-6"
              >
                <button
                  onClick={() => setShowSources(!showSources)}
                  className="flex items-center gap-2 text-sm font-medium text-gray-600 hover:text-gray-900 transition-colors"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                  </svg>
                  Sources ({response.sources.length})
                  <motion.svg
                    animate={{ rotate: showSources ? 180 : 0 }}
                    transition={{ duration: 0.2 }}
                    className="w-4 h-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </motion.svg>
                </button>

                <AnimatePresence>
                  {showSources && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: 'auto' }}
                      exit={{ opacity: 0, height: 0 }}
                      transition={{ duration: 0.3 }}
                      className="mt-3"
                    >
                      <SourceLinks 
                        sources={
                          isSourceLinkArray(response.sources) 
                            ? response.sources.map(s => s.url) 
                            : (response.sources as string[] || [])
                        } 
                      />
                    </motion.div>
                  )}
                </AnimatePresence>
              </motion.div>
            )}

            {/* Action Buttons */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.5 }}
              className="mt-6 pt-4 border-t border-gray-200"
            >
              <div className="flex gap-2">
                <motion.button
                  onClick={handleCopy}
                  className="flex items-center gap-2 px-4 py-2 bg-gray-100 hover:bg-gray-200 rounded-md text-sm font-medium text-gray-700 transition-colors"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                  </svg>
                  Copy Response
                </motion.button>
                {onRefine && (
                  <motion.button
                    onClick={handleRefine}
                    className="flex items-center gap-2 px-4 py-2 bg-blue-100 hover:bg-blue-200 rounded-md text-sm font-medium text-blue-700 transition-colors"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                    </svg>
                    Refine
                  </motion.button>
                )}
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  )
}
