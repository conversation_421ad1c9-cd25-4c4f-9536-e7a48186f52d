// Integration Tests - Complete AI Workflow Testing
import React from 'react';
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { Dashboard } from '../../../frontend/src/components/Dashboard';
import { mockAPIResponse, mockAIResponse, mockCalendarEvent } from '../setup';

// Mock the entire service layer
jest.mock('../../../frontend/src/services', () => ({
  aiService: {
    processQuery: jest.fn(),
    streamResponse: jest.fn(),
    getProviders: jest.fn(),
  },
  storageService: {
    save: jest.fn(),
    load: jest.fn(),
    remove: jest.fn(),
  },
  embeddingsService: {
    getStatus: jest.fn(),
    search: jest.fn(),
  },
}));

// Mock WebSocket
const mockWebSocket = {
  send: jest.fn(),
  close: jest.fn(),
  addEventListener: jest.fn(),
  removeEventListener: jest.fn(),
  readyState: 1, // OPEN
};

Object.defineProperty(global, 'WebSocket', {
  writable: true,
  value: jest.fn(() => mockWebSocket),
});

describe('AI Workflow Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Setup default successful responses
    const aiService = require('../../../frontend/src/services').aiService;
    aiService.processQuery.mockResolvedValue(mockAPIResponse(mockAIResponse()));
    aiService.getProviders.mockResolvedValue(mockAPIResponse([
      { id: 'openai', name: 'OpenAI', models: ['gpt-4'] },
    ]));
  });

  it('completes full AI query workflow from input to response', async () => {
    const user = userEvent.setup();
    render(<Dashboard />);

    // 1. User enters a query
    const searchInput = screen.getByTestId('search-input');
    await user.type(searchInput, 'What are the key trends in AI for 2024?');

    // 2. User clicks search button
    const searchButton = screen.getByTestId('search-button');
    await user.click(searchButton);

    // 3. Verify loading state is shown
    expect(screen.getByTestId('ai-loading')).toBeInTheDocument();

    // 4. Wait for AI response to appear
    await waitFor(() => {
      expect(screen.getByTestId('ai-response-panel')).toBeInTheDocument();
    });

    // 5. Verify response content is displayed
    expect(screen.getByText('This is a test AI response')).toBeInTheDocument();

    // 6. Verify search is saved to history
    expect(screen.getByTestId('search-history')).toBeInTheDocument();
  });

  it('handles streaming AI responses with typewriter effect', async () => {
    const user = userEvent.setup();
    
    // Mock streaming response
    const aiService = require('../../../frontend/src/services').aiService;
    aiService.streamResponse.mockImplementation(async function* () {
      yield { chunk: 'AI is ' };
      yield { chunk: 'thinking about ' };
      yield { chunk: 'your question...' };
    });

    render(<Dashboard />);

    const searchInput = screen.getByTestId('search-input');
    await user.type(searchInput, 'Stream test query');

    // Enable streaming mode
    const streamToggle = screen.getByTestId('stream-toggle');
    await user.click(streamToggle);

    const searchButton = screen.getByTestId('search-button');
    await user.click(searchButton);

    // Verify streaming indicator
    expect(screen.getByTestId('streaming-indicator')).toBeInTheDocument();

    // Verify typewriter effect shows progressive text
    await waitFor(() => {
      expect(screen.getByText(/AI is thinking/)).toBeInTheDocument();
    });
  });

  it('integrates calendar with AI-generated events', async () => {
    const user = userEvent.setup();
    
    // Mock AI response with calendar event suggestion
    const aiService = require('../../../frontend/src/services').aiService;
    const responseWithEvent = mockAIResponse({
      content: 'I can help you schedule a meeting for next Monday at 2 PM.',
      metadata: {
        ...mockAIResponse().metadata,
        suggestedEvents: [
          mockCalendarEvent({
            title: 'AI Suggested Meeting',
            startDate: new Date('2024-01-22T14:00:00Z'),
            aiGenerated: true,
          }),
        ],
      },
    });
    
    aiService.processQuery.mockResolvedValue(mockAPIResponse(responseWithEvent));

    render(<Dashboard />);

    // Query for calendar assistance
    const searchInput = screen.getByTestId('search-input');
    await user.type(searchInput, 'Schedule a meeting for Monday at 2 PM');

    const searchButton = screen.getByTestId('search-button');
    await user.click(searchButton);

    // Wait for AI response
    await waitFor(() => {
      expect(screen.getByTestId('ai-response-panel')).toBeInTheDocument();
    });

    // Verify suggested event appears
    expect(screen.getByTestId('suggested-event')).toBeInTheDocument();
    expect(screen.getByText('AI Suggested Meeting')).toBeInTheDocument();

    // User accepts the suggestion
    const acceptButton = screen.getByTestId('accept-suggestion');
    await user.click(acceptButton);

    // Verify event is added to calendar
    await waitFor(() => {
      expect(screen.getByTestId('calendar-event-ai-suggested-meeting')).toBeInTheDocument();
    });
  });

  it('handles real-time updates via WebSocket', async () => {
    const user = userEvent.setup();
    render(<Dashboard />);

    // Simulate incoming WebSocket message
    const messageHandler = mockWebSocket.addEventListener.mock.calls
      .find(call => call[0] === 'message')?.[1];

    if (messageHandler) {
      const mockEvent = {
        data: JSON.stringify({
          type: 'event-created',
          data: mockCalendarEvent({
            title: 'Real-time Event',
            startDate: new Date('2024-01-20T10:00:00Z'),
          }),
        }),
      };

      messageHandler(mockEvent);

      // Verify real-time event appears in calendar
      await waitFor(() => {
        expect(screen.getByText('Real-time Event')).toBeInTheDocument();
      });
    }
  });

  it('manages settings and API configuration workflow', async () => {
    const user = userEvent.setup();
    render(<Dashboard />);

    // Open settings panel
    const settingsButton = screen.getByTestId('settings-button');
    await user.click(settingsButton);

    // Navigate to API settings tab
    const apiTab = screen.getByText('API Settings');
    await user.click(apiTab);

    // Configure API provider
    const providerSelect = screen.getByTestId('provider-select');
    await user.selectOptions(providerSelect, 'openai');

    // Enter API key
    const apiKeyInput = screen.getByTestId('api-key-input');
    await user.type(apiKeyInput, 'test-api-key-123');

    // Test connection
    const testButton = screen.getByTestId('test-connection');
    await user.click(testButton);

    // Verify connection success
    await waitFor(() => {
      expect(screen.getByTestId('connection-success')).toBeInTheDocument();
    });

    // Save settings
    const saveButton = screen.getByTestId('save-settings');
    await user.click(saveButton);

    // Verify settings are persisted
    expect(screen.getByText('Settings saved successfully')).toBeInTheDocument();
  });

  it('handles error states gracefully throughout the workflow', async () => {
    const user = userEvent.setup();
    
    // Mock API error
    const aiService = require('../../../frontend/src/services').aiService;
    aiService.processQuery.mockRejectedValue(new Error('API Error: Rate limit exceeded'));

    render(<Dashboard />);

    const searchInput = screen.getByTestId('search-input');
    await user.type(searchInput, 'Error test query');

    const searchButton = screen.getByTestId('search-button');
    await user.click(searchButton);

    // Verify error is displayed
    await waitFor(() => {
      expect(screen.getByTestId('error-message')).toBeInTheDocument();
      expect(screen.getByText(/Rate limit exceeded/)).toBeInTheDocument();
    });

    // Verify retry button is available
    expect(screen.getByTestId('retry-button')).toBeInTheDocument();

    // Test retry functionality
    aiService.processQuery.mockResolvedValue(mockAPIResponse(mockAIResponse()));
    
    const retryButton = screen.getByTestId('retry-button');
    await user.click(retryButton);

    // Verify successful retry
    await waitFor(() => {
      expect(screen.getByTestId('ai-response-panel')).toBeInTheDocument();
    });
  });

  it('maintains state persistence across page reloads', async () => {
    const user = userEvent.setup();
    
    // Mock localStorage with existing data
    const mockData = {
      searchHistory: ['Previous search query'],
      userSettings: { theme: 'dark', provider: 'openai' },
      formDrafts: { searchInput: 'Saved draft query' },
    };

    const storageService = require('../../../frontend/src/services').storageService;
    storageService.load.mockImplementation((key: string) => {
      return Promise.resolve(mockData[key as keyof typeof mockData]);
    });

    render(<Dashboard />);

    // Verify persisted data is loaded
    await waitFor(() => {
      expect(screen.getByDisplayValue('Saved draft query')).toBeInTheDocument();
      expect(screen.getByText('Previous search query')).toBeInTheDocument();
    });

    // Verify theme is applied
    expect(document.body).toHaveClass('dark-theme');
  });

  it('handles offline mode and sync when connection is restored', async () => {
    const user = userEvent.setup();
    
    // Simulate offline state
    Object.defineProperty(navigator, 'onLine', {
      writable: true,
      value: false,
    });

    render(<Dashboard />);

    // Verify offline indicator
    expect(screen.getByTestId('offline-indicator')).toBeInTheDocument();

    // User tries to make query while offline
    const searchInput = screen.getByTestId('search-input');
    await user.type(searchInput, 'Offline query');

    const searchButton = screen.getByTestId('search-button');
    await user.click(searchButton);

    // Verify offline message
    expect(screen.getByText(/You are currently offline/)).toBeInTheDocument();

    // Simulate coming back online
    Object.defineProperty(navigator, 'onLine', {
      value: true,
    });

    fireEvent(window, new Event('online'));

    // Verify sync attempt
    await waitFor(() => {
      expect(screen.getByTestId('sync-indicator')).toBeInTheDocument();
    });
  });

  it('supports keyboard shortcuts throughout the interface', async () => {
    const user = userEvent.setup();
    render(<Dashboard />);

    // Test global search shortcut (Ctrl+K)
    await user.keyboard('{Control>}k{/Control}');
    
    expect(screen.getByTestId('search-input')).toHaveFocus();

    // Test escape to close modals
    const settingsButton = screen.getByTestId('settings-button');
    await user.click(settingsButton);

    await user.keyboard('{Escape}');
    
    expect(screen.queryByTestId('settings-modal')).not.toBeInTheDocument();

    // Test quick actions with keyboard
    await user.keyboard('{Control>}{Shift>}n{/Shift}{/Control}'); // New event
    
    expect(screen.getByTestId('event-creation-modal')).toBeInTheDocument();
  });

  it('maintains accessibility standards across all interactions', async () => {
    const user = userEvent.setup();
    render(<Dashboard />);

    // Verify main landmarks
    expect(screen.getByRole('main')).toBeInTheDocument();
    expect(screen.getByRole('banner')).toBeInTheDocument();
    expect(screen.getByRole('navigation')).toBeInTheDocument();

    // Test screen reader announcements
    const searchInput = screen.getByTestId('search-input');
    expect(searchInput).toHaveAttribute('aria-label');

    // Test focus management
    const searchButton = screen.getByTestId('search-button');
    await user.click(searchButton);

    // Focus should move to results when they appear
    await waitFor(() => {
      const responsePanel = screen.getByTestId('ai-response-panel');
      expect(responsePanel).toHaveAttribute('tabindex', '0');
    });

    // Test high contrast mode support
    document.documentElement.classList.add('high-contrast');
    
    expect(screen.getByTestId('search-input')).toHaveClass('high-contrast-input');
  });

  it('optimizes performance with proper component memoization', async () => {
    const user = userEvent.setup();
    
    // Track render counts
    let calendarRenderCount = 0;
    let settingsRenderCount = 0;

    const CalendarWrapper = () => {
      calendarRenderCount++;
      return <div data-testid="calendar-component">Calendar</div>;
    };

    const SettingsWrapper = () => {
      settingsRenderCount++;
      return <div data-testid="settings-component">Settings</div>;
    };

    // Mock components with render tracking
    jest.doMock('../../../frontend/src/components/calendar/Calendar', () => ({
      Calendar: CalendarWrapper,
    }));

    render(<Dashboard />);

    // Initial render
    const initialCalendarRenders = calendarRenderCount;

    // Update search query (should not re-render calendar)
    const searchInput = screen.getByTestId('search-input');
    await user.type(searchInput, 'Performance test');

    // Calendar should not have re-rendered
    expect(calendarRenderCount).toBe(initialCalendarRenders);

    // Only relevant components should re-render when needed
    const searchButton = screen.getByTestId('search-button');
    await user.click(searchButton);

    // Settings component should not have rendered at all
    expect(settingsRenderCount).toBe(0);
  });
});
