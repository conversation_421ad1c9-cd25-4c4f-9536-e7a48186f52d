// Test setup file for React Testing Library and animation testing
import '@testing-library/jest-dom';

// Mock Framer Motion for testing
jest.mock('framer-motion', () => ({
  motion: {
    div: (props: any) => props.children,
    span: (props: any) => props.children,
    button: (props: any) => props.children,
    form: (props: any) => props.children,
    section: (props: any) => props.children,
    article: (props: any) => props.children,
  },
  AnimatePresence: ({ children }: any) => children,
  useAnimation: () => ({
    start: jest.fn(),
    stop: jest.fn(),
    set: jest.fn(),
  }),
  useMotionValue: (initial: any) => ({ get: () => initial, set: jest.fn() }),
  useTransform: () => jest.fn(),
  useSpring: () => jest.fn(),
}));

// Mock IntersectionObserver
Object.defineProperty(global, 'IntersectionObserver', {
  writable: true,
  value: class IntersectionObserver {
    constructor() {}
    disconnect() {}
    observe() {}
    unobserve() {}
  },
});

// Mock ResizeObserver
Object.defineProperty(global, 'ResizeObserver', {
  writable: true,
  value: class ResizeObserver {
    constructor() {}
    disconnect() {}
    observe() {}
    unobserve() {}
  },
});

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(),
    removeListener: jest.fn(),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

// Mock WebSocket
Object.defineProperty(global, 'WebSocket', {
  writable: true,
  value: class WebSocket {
    static CONNECTING = 0;
    static OPEN = 1;
    static CLOSING = 2;
    static CLOSED = 3;
    
    readyState = 0;
    url: string;
    
    constructor(url: string) {
      this.url = url;
      setTimeout(() => {
        this.readyState = 1;
        if (this.onopen) this.onopen({} as any);
      }, 0);
    }
    
    onopen: ((event: Event) => void) | null = null;
    onclose: ((event: CloseEvent) => void) | null = null;
    onmessage: ((event: MessageEvent) => void) | null = null;
    onerror: ((event: Event) => void) | null = null;
    
    send(data: any) {}
    close() {
      this.readyState = 3;
      if (this.onclose) this.onclose({} as any);
    }
    
    addEventListener(type: string, listener: any) {}
    removeEventListener(type: string, listener: any) {}
  },
});

// Mock fetch
Object.defineProperty(global, 'fetch', {
  writable: true,
  value: jest.fn(),
});

// Silence console warnings in tests
const originalWarn = console.warn;
beforeAll(() => {
  console.warn = (...args: any[]) => {
    if (
      typeof args[0] === 'string' &&
      args[0].includes('React.createFactory') 
    ) {
      return;
    }
    originalWarn.call(console, ...args);
  };
});

afterAll(() => {
  console.warn = originalWarn;
});

// Global test helpers
export const mockAPIResponse = <T>(data: T, success = true) => ({
  success,
  data: success ? data : undefined,
  error: success ? undefined : 'Mock error',
  message: success ? 'Success' : 'Error',
  timestamp: new Date().toISOString(),
});

export const mockCalendarEvent = (overrides = {}) => ({
  id: 'test-event-1',
  title: 'Test Event',
  description: 'Test event description',
  startDate: new Date('2024-01-15T10:00:00Z'),
  endDate: new Date('2024-01-15T11:00:00Z'),
  allDay: false,
  category: 'work' as const,
  priority: 'medium' as const,
  aiGenerated: false,
  metadata: {
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
    createdBy: 'test-user',
    source: 'manual' as const,
    tags: [],
    importance: 5,
  },
  ...overrides,
});

export const mockAIResponse = (overrides = {}) => ({
  id: 'test-response-1',
  content: 'This is a test AI response',
  type: 'text' as const,
  confidence: 0.95,
  sources: [],
  metadata: {
    model: 'gpt-4',
    provider: 'openai',
    processingTime: 1500,
    contentFiltered: false,
  },
  usage: {
    promptTokens: 50,
    completionTokens: 25,
    totalTokens: 75,
  },
  generatedAt: new Date().toISOString(),
  ...overrides,
});

export const mockUserSettings = (overrides = {}) => ({
  profile: {
    id: 'test-user',
    email: '<EMAIL>',
    name: 'Test User',
    timezone: 'UTC',
    locale: 'en-US',
    createdAt: '2024-01-01T00:00:00Z',
    lastLoginAt: '2024-01-15T10:00:00Z',
  },
  preferences: {
    theme: 'light' as const,
    language: 'en',
    dateFormat: 'YYYY-MM-DD',
    timeFormat: '24h',
    workingHours: {
      start: '09:00',
      end: '17:00',
    },
    defaultView: 'month' as const,
    autoSave: true,
    keyboardShortcuts: true,
  },
  api: {
    providers: [],
    defaultProvider: 'openai',
    timeout: 30000,
    retryAttempts: 3,
    rateLimiting: {
      requestsPerMinute: 60,
      tokensPerMinute: 10000,
      concurrentRequests: 5,
    },
  },
  calendar: {
    defaultDuration: 30,
    workingDays: [1, 2, 3, 4, 5],
    workingHours: {
      start: '09:00',
      end: '17:00',
    },
    timezone: 'UTC',
    reminderDefaults: [],
    syncSettings: {
      enabled: false,
      providers: [],
      conflictResolution: 'manual' as const,
      syncInterval: 300,
    },
  },
  notifications: {
    browser: true,
    email: false,
    sound: true,
    types: ['ai-response', 'calendar-event'],
    quietHours: {
      start: '22:00',
      end: '08:00',
    },
  },
  privacy: {
    dataRetention: 90,
    anonymizeData: false,
    shareUsageStats: true,
    exportData: true,
    deleteAccount: false,
  },
  ...overrides,
});

// Animation testing utilities
export const waitForAnimation = (duration = 300) => 
  new Promise(resolve => setTimeout(resolve, duration));

export const mockAnimationFrame = () => {
  let id = 0;
  const callbacks = new Map();
  
  global.requestAnimationFrame = jest.fn((callback) => {
    id++;
    callbacks.set(id, callback);
    return id;
  });
  
  global.cancelAnimationFrame = jest.fn((id) => {
    callbacks.delete(id);
  });
  
  const runFrame = () => {
    callbacks.forEach(callback => callback(performance.now()));
    callbacks.clear();
  };
  
  return { runFrame };
};
