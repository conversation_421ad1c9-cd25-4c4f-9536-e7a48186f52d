"""
Main orchestration agent for AI-Powered Dashboard.

This agent handles the initial categorization of user inputs into tasks, events,
or AI questions using Mirascope patterns with OpenRouter free models.
Follows research/mirascope/page7-agents.md patterns for agent implementation.
"""

import asyncio
import logging
from typing import List, Dict, Any, Optional, AsyncGenerator
from datetime import datetime

from mirascope.core import openai
from pydantic import ValidationError

from app.models.ai_responses import AICategorizationResponse, AIProcessingError
from app.models.pydantic_models import (
    UserInput, CategoryDecision, InputCategory, ProcessingState, AnimationStep
)
from app.config.settings import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()


class AIOrchestrator:
    """
    Main orchestration agent that categorizes user inputs and routes to specialized agents.
    
    This agent uses Mirascope with OpenRouter free models and response_model
    for structured output since free models don't support native tool calling.
    """
    
    def __init__(self):
        self.model = settings.primary_llm_model
        self.fallback_model = settings.ai_fallback_model
        self.max_retries = settings.ai_max_retries
        self.processing_history: List[Dict[str, Any]] = []
        
    async def categorize_input(
        self, 
        user_input: UserInput,
        context: Optional[Dict[str, Any]] = None
    ) -> CategoryDecision:
        """
        Categorize user input into task, event, or AI question.
        
        Args:
            user_input: The user's input to categorize
            context: Optional context from previous interactions
            
        Returns:
            CategoryDecision: The categorization result with confidence and reasoning
        """
        try:
            logger.info(f"Categorizing input: {user_input.text[:100]}...")
            
            # Create processing state for visual feedback
            processing_state = ProcessingState(
                is_processing=True,
                started_at=datetime.now(),
                steps=[
                    AnimationStep(id="analyze", message="Analyzing input...", animation_type="pulse"),
                    AnimationStep(id="categorize", message="Determining category...", animation_type="spin"),
                    AnimationStep(id="validate", message="Validating decision...", animation_type="fade")
                ]
            )
            
            # Step 1: Analyze input (with animation feedback)
            processing_state.current_step = 0
            processing_state.steps[0].completed = False
            
            # Perform categorization with retry logic
            ai_response = await self._categorize_with_retry(user_input, context)
            
            # Step 2: Create category decision
            processing_state.current_step = 1
            processing_state.steps[0].completed = True
            
            category_decision = CategoryDecision(
                category=ai_response.category,
                confidence=ai_response.confidence,
                reasoning=ai_response.reasoning,
                processing_steps=[step.message for step in processing_state.steps],
                suggested_actions=ai_response.suggested_clarifications
            )
            
            # Step 3: Validation complete
            processing_state.current_step = 2
            processing_state.steps[1].completed = True
            processing_state.steps[2].completed = True
            processing_state.is_processing = False
            processing_state.completed_at = datetime.now()
            
            # Store in processing history for context
            self.processing_history.append({
                "input": user_input.text,
                "category": ai_response.category,
                "confidence": ai_response.confidence,
                "timestamp": datetime.now(),
                "reasoning": ai_response.reasoning
            })
            
            # Keep only last 10 items for memory efficiency
            if len(self.processing_history) > 10:
                self.processing_history.pop(0)
            
            logger.info(f"Categorized as {ai_response.category} with confidence {ai_response.confidence}")
            return category_decision
            
        except Exception as e:
            logger.error(f"Error in categorize_input: {e}")
            # Return error state for frontend to handle
            return CategoryDecision(
                category=InputCategory.AI_QUESTION,  # Default fallback
                confidence=0.1,
                reasoning=f"Error during categorization: {str(e)}",
                processing_steps=["Error occurred during processing"],
                suggested_actions=["Please try rephrasing your input"]
            )
    
    async def _categorize_with_retry(
        self, 
        user_input: UserInput, 
        context: Optional[Dict[str, Any]] = None
    ) -> AICategorizationResponse:
        """
        Perform categorization with retry logic and fallback models.
        
        Args:
            user_input: User input to categorize
            context: Optional context
            
        Returns:
            AICategorizationResponse: Structured AI response
        """
        last_error = None
        
        # Try primary model first
        for attempt in range(self.max_retries):
            try:
                logger.debug(f"Categorization attempt {attempt + 1}/{self.max_retries} with model {self.model}")
                return await self._perform_categorization(user_input, context, self.model)
                
            except Exception as e:
                last_error = e
                logger.warning(f"Attempt {attempt + 1} failed with {self.model}: {e}")
                
                if attempt < self.max_retries - 1:
                    await asyncio.sleep(0.5 * (attempt + 1))  # Exponential backoff
        
        # Try fallback model
        try:
            logger.info(f"Trying fallback model: {self.fallback_model}")
            return await self._perform_categorization(user_input, context, self.fallback_model)
            
        except Exception as e:
            logger.error(f"Fallback model also failed: {e}")
            
            # Return structured error response
            raise AIProcessingError(
                error_type="categorization_failed",
                error_message=f"All categorization attempts failed. Last error: {str(last_error)}",
                error_code="CATEGORIZATION_ALL_FAILED",
                operation="input_categorization",
                input_data=user_input.text,
                recoverable=True,
                suggested_action="Please try rephrasing your input or try again later",
                model_used=self.model
            )
    
    @openai.call(model=settings.primary_llm_model, response_model=AICategorizationResponse, json_mode=True)
    async def _perform_categorization(
        self, 
        user_input: UserInput, 
        context: Optional[Dict[str, Any]] = None,
        model: str = None
    ) -> str:
        """
        Mirascope call for input categorization.
        
        This uses OpenRouter with response_model for structured output
        since free models don't support native tool calling.
        """
        
        # Build context from processing history
        context_str = ""
        if self.processing_history:
            recent_history = self.processing_history[-3:]  # Last 3 interactions
            context_str = "\\n".join([
                f"Previous: '{item['input']}' → {item['category']} (confidence: {item['confidence']:.2f})"
                for item in recent_history
            ])
            context_str = f"\\n\\nRecent context:\\n{context_str}"
        
        # Add user-provided context
        if context:
            context_str += f"\\n\\nAdditional context: {context}"
        
        return f"""You are an intelligent input categorization agent. Analyze the user's input and determine whether it represents:

1. **TASK** - Something to be done, completed, or accomplished
2. **EVENT** - A time-based occurrence, meeting, appointment, or reminder  
3. **AI_QUESTION** - A question seeking information, explanation, or assistance

CRITICAL RULES:
- Base decisions on MEANING and INTENT, not just keywords
- Use AI reasoning, not hardcoded patterns
- Consider the full context of the input
- Provide high confidence only when certain
- Include specific reasoning and key indicators

INPUT TO ANALYZE: "{user_input.text}"

CONTEXT: {context_str}

Respond with a JSON object containing:
- category: one of "task", "event", or "ai_question"
- confidence: float between 0.0 and 1.0
- reasoning: detailed explanation of your decision
- key_indicators: specific words/phrases that influenced the decision
- requires_additional_info: whether more clarification is needed
- suggested_clarifications: array of questions if more info needed

Examples of good categorization:
- "I need to finish the report by Friday" → TASK (action-oriented, deadline)
- "Meeting with client tomorrow at 2pm" → EVENT (time-specific occurrence)
- "What's the weather like today?" → AI_QUESTION (seeking information)
- "Schedule dentist appointment next week" → TASK (action to schedule, not the appointment itself)
- "Remind me about the presentation" → EVENT (time-based reminder)

Analyze the input carefully and provide structured categorization."""
    
    async def stream_categorization(
        self,
        user_input: UserInput,
        context: Optional[Dict[str, Any]] = None
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Stream the categorization process with real-time updates.
        
        This provides visual feedback for the frontend animations
        following the mermaid diagram workflow.
        
        Args:
            user_input: User input to categorize
            context: Optional context
            
        Yields:
            Dict[str, Any]: Processing state updates for frontend
        """
        try:
            # Initialize processing state
            processing_state = ProcessingState(
                is_processing=True,
                started_at=datetime.now(),
                steps=[
                    AnimationStep(
                        id="received", 
                        message="Input received", 
                        animation_type="fade",
                        duration=0.3
                    ),
                    AnimationStep(
                        id="analyzing", 
                        message="Analyzing content...", 
                        animation_type="pulse",
                        duration=1.5,
                        delay=0.3
                    ),
                    AnimationStep(
                        id="categorizing", 
                        message="Determining category...", 
                        animation_type="spin",
                        duration=2.0,
                        delay=1.8
                    ),
                    AnimationStep(
                        id="validating", 
                        message="Validating decision...", 
                        animation_type="typewriter",
                        duration=1.0,
                        delay=3.8
                    ),
                    AnimationStep(
                        id="complete", 
                        message="Categorization complete", 
                        animation_type="bounce",
                        duration=0.5,
                        delay=4.8
                    )
                ]
            )
            
            # Stream each processing step
            for i, step in enumerate(processing_state.steps):
                processing_state.current_step = i
                
                # Yield current state
                yield {
                    "type": "processing_update",
                    "data": {
                        "step": step.dict(),
                        "progress": i / len(processing_state.steps),
                        "current_step": i,
                        "total_steps": len(processing_state.steps)
                    }
                }
                
                # Simulate processing time for smooth animations
                if i < len(processing_state.steps) - 1:
                    await asyncio.sleep(step.delay + step.duration)
                
                # Mark step as completed
                step.completed = True
            
            # Perform actual categorization during the processing steps
            result = await self.categorize_input(user_input, context)
            
            # Yield final result
            processing_state.is_processing = False
            processing_state.completed_at = datetime.now()
            
            yield {
                "type": "categorization_complete",
                "data": {
                    "result": result.dict(),
                    "processing_state": processing_state.dict()
                }
            }
            
        except Exception as e:
            logger.error(f"Error in stream_categorization: {e}")
            yield {
                "type": "error",
                "data": {
                    "error_message": str(e),
                    "error_type": "categorization_error"
                }
            }
    
    def get_processing_stats(self) -> Dict[str, Any]:
        """Get processing statistics for monitoring and debugging."""
        if not self.processing_history:
            return {"total_processed": 0, "categories": {}, "avg_confidence": 0.0}
        
        categories = {}
        total_confidence = 0.0
        
        for item in self.processing_history:
            category = item["category"]
            categories[category] = categories.get(category, 0) + 1
            total_confidence += item["confidence"]
        
        return {
            "total_processed": len(self.processing_history),
            "categories": categories,
            "avg_confidence": total_confidence / len(self.processing_history),
            "last_processed": self.processing_history[-1]["timestamp"].isoformat(),
            "models_used": [self.model, self.fallback_model]
        }


# Global orchestrator instance
_orchestrator = None


def get_orchestrator() -> AIOrchestrator:
    """Get the global orchestrator instance (singleton pattern)."""
    global _orchestrator
    if _orchestrator is None:
        _orchestrator = AIOrchestrator()
    return _orchestrator


# Export main components
__all__ = ["AIOrchestrator", "get_orchestrator"]
