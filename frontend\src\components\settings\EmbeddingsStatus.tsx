import { useState } from 'react'
import { motion } from 'framer-motion'

interface EmbeddingModel {
  name: string
  size: string
  status: 'downloading' | 'ready' | 'error' | 'not-installed'
  progress?: number
}

interface IndexStats {
  totalDocuments: number
  lastUpdated: string
  indexSize: string
  averageQueryTime: string
}

/**
 * EmbeddingsStatus Component
 * PATTERN: Embedding model management and index status
 * Features:
 * - Model download/installation status
 * - Index statistics and management
 * - Real-time progress indicators
 * - Index rebuild functionality
 */
export function EmbeddingsStatus() {
  const [models, setModels] = useState<EmbeddingModel[]>([
    {
      name: 'nomic-embed-text',
      size: '274 MB',
      status: 'ready'
    },
    {
      name: 'all-minilm',
      size: '45 MB',
      status: 'not-installed'
    },
    {
      name: 'sentence-transformers',
      size: '120 MB',
      status: 'not-installed'
    }
  ])

  const [indexStats, setIndexStats] = useState<IndexStats>({
    totalDocuments: 1247,
    lastUpdated: '2025-07-20T15:30:00Z',
    indexSize: '45.2 MB',
    averageQueryTime: '120ms'
  })

  const [isRebuilding, setIsRebuilding] = useState(false)
  const [rebuildProgress, setRebuildProgress] = useState(0)

  // Simulate model download
  const downloadModel = (modelName: string) => {
    setModels(prev => prev.map(model =>
      model.name === modelName
        ? { ...model, status: 'downloading', progress: 0 }
        : model
    ))

    // Simulate download progress
    const interval = setInterval(() => {
      setModels(prev => prev.map(model => {
        if (model.name === modelName && model.status === 'downloading') {
          const newProgress = (model.progress || 0) + Math.random() * 20
          if (newProgress >= 100) {
            clearInterval(interval)
            return { ...model, status: 'ready', progress: 100 }
          }
          return { ...model, progress: newProgress }
        }
        return model
      }))
    }, 500)
  }

  // Simulate index rebuild
  const rebuildIndex = () => {
    setIsRebuilding(true)
    setRebuildProgress(0)

    const interval = setInterval(() => {
      setRebuildProgress(prev => {
        const newProgress = prev + Math.random() * 15
        if (newProgress >= 100) {
          clearInterval(interval)
          setIsRebuilding(false)
          setIndexStats(prevStats => ({
            ...prevStats,
            lastUpdated: new Date().toISOString()
          }))
          return 100
        }
        return newProgress
      })
    }, 300)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getStatusColor = (status: EmbeddingModel['status']) => {
    switch (status) {
      case 'ready': return 'bg-green-100 text-green-800'
      case 'downloading': return 'bg-blue-100 text-blue-800'
      case 'error': return 'bg-red-100 text-red-800'
      case 'not-installed': return 'bg-gray-100 text-gray-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusIcon = (status: EmbeddingModel['status']) => {
    switch (status) {
      case 'ready':
        return (
          <svg className="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
        )
      case 'downloading':
        return (
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
            className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full"
          />
        )
      case 'error':
        return (
          <svg className="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        )
      case 'not-installed':
        return (
          <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        )
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="space-y-6"
    >
      {/* Embedding Models */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Embedding Models</h3>
        
        <div className="space-y-4">
          {models.map((model) => (
            <motion.div
              key={model.name}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              className="flex items-center justify-between p-4 border border-gray-200 rounded-lg"
            >
              <div className="flex items-center gap-3">
                {getStatusIcon(model.status)}
                <div>
                  <h4 className="font-medium text-gray-900">{model.name}</h4>
                  <p className="text-sm text-gray-600">Size: {model.size}</p>
                  {model.status === 'downloading' && model.progress && (
                    <div className="mt-2">
                      <div className="w-48 bg-gray-200 rounded-full h-2">
                        <motion.div
                          className="bg-blue-600 h-2 rounded-full"
                          initial={{ width: 0 }}
                          animate={{ width: `${model.progress}%` }}
                          transition={{ duration: 0.3 }}
                        />
                      </div>
                      <p className="text-xs text-gray-500 mt-1">
                        {Math.round(model.progress)}% downloaded
                      </p>
                    </div>
                  )}
                </div>
              </div>

              <div className="flex items-center gap-3">
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(model.status)}`}>
                  {model.status.replace('-', ' ').toUpperCase()}
                </span>
                
                {model.status === 'not-installed' && (
                  <motion.button
                    onClick={() => downloadModel(model.name)}
                    className="px-3 py-1 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700 transition-colors"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    Install
                  </motion.button>
                )}
              </div>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Index Statistics */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Search Index Statistics</h3>
        
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.1 }}
            className="text-center p-4 bg-gray-50 rounded-lg"
          >
            <div className="text-2xl font-bold text-gray-900">{indexStats.totalDocuments.toLocaleString()}</div>
            <div className="text-sm text-gray-600">Documents</div>
          </motion.div>
          
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.2 }}
            className="text-center p-4 bg-gray-50 rounded-lg"
          >
            <div className="text-2xl font-bold text-gray-900">{indexStats.indexSize}</div>
            <div className="text-sm text-gray-600">Index Size</div>
          </motion.div>
          
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.3 }}
            className="text-center p-4 bg-gray-50 rounded-lg"
          >
            <div className="text-2xl font-bold text-gray-900">{indexStats.averageQueryTime}</div>
            <div className="text-sm text-gray-600">Avg Query Time</div>
          </motion.div>
          
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.4 }}
            className="text-center p-4 bg-gray-50 rounded-lg"
          >
            <div className="text-2xl font-bold text-gray-900">
              {formatDate(indexStats.lastUpdated)}
            </div>
            <div className="text-sm text-gray-600">Last Updated</div>
          </motion.div>
        </div>

        {/* Rebuild Index */}
        <div className="border-t border-gray-200 pt-6">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium text-gray-900">Rebuild Search Index</h4>
              <p className="text-sm text-gray-600">
                Recreate the search index to include new documents and improve accuracy
              </p>
            </div>
            
            <motion.button
              onClick={rebuildIndex}
              disabled={isRebuilding}
              className="px-4 py-2 bg-orange-600 text-white rounded-md font-medium hover:bg-orange-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
              whileHover={{ scale: isRebuilding ? 1 : 1.05 }}
              whileTap={{ scale: isRebuilding ? 1 : 0.95 }}
            >
              {isRebuilding ? 'Rebuilding...' : 'Rebuild Index'}
            </motion.button>
          </div>

          {isRebuilding && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              className="mt-4"
            >
              <div className="w-full bg-gray-200 rounded-full h-3">
                <motion.div
                  className="bg-orange-600 h-3 rounded-full"
                  initial={{ width: 0 }}
                  animate={{ width: `${rebuildProgress}%` }}
                  transition={{ duration: 0.3 }}
                />
              </div>
              <p className="text-sm text-gray-600 mt-2">
                Rebuilding index... {Math.round(rebuildProgress)}% complete
              </p>
            </motion.div>
          )}
        </div>
      </div>

      {/* Ollama Status */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Ollama Service</h3>
        
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <motion.div
              animate={{
                scale: [1, 1.2, 1],
                backgroundColor: ['#10b981', '#34d399', '#10b981']
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                ease: "easeInOut"
              }}
              className="w-3 h-3 bg-green-500 rounded-full"
            />
            <div>
              <h4 className="font-medium text-gray-900">Service Status</h4>
              <p className="text-sm text-gray-600">Ollama is running and ready</p>
            </div>
          </div>
          
          <div className="text-right">
            <div className="text-sm font-medium text-gray-900">localhost:11434</div>
            <div className="text-xs text-gray-500">Endpoint</div>
          </div>
        </div>
      </div>
    </motion.div>
  )
}
