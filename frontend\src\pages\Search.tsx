import React from 'react'
import { motion } from 'framer-motion'
import { Search as SearchIcon, Filter, Layout, Grid } from 'lucide-react'

/**
 * Search page component - Search results display
 */
const Search: React.FC = () => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="p-6 min-h-screen"
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold text-text-primary mb-2">Search Results</h1>
          <p className="text-text-secondary">AI-powered intelligent search across all sources</p>
        </div>
        
        <div className="flex items-center gap-3">
          <motion.button
            whileHover={{ scale: 1.02 }}
            className="p-2 rounded-xl border border-border-primary hover:bg-background-secondary transition-colors"
          >
            <Layout className="w-5 h-5 text-text-secondary" />
          </motion.button>
          
          <motion.button
            whileHover={{ scale: 1.02 }}
            className="p-2 rounded-xl border border-border-primary hover:bg-background-secondary transition-colors"
          >
            <Grid className="w-5 h-5 text-text-secondary" />
          </motion.button>
        </div>
      </div>

      {/* Search Bar */}
      <div className="max-w-2xl mb-8">
        <div className="relative">
          <SearchIcon className="absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 text-text-muted" />
          <input
            type="text"
            placeholder="What would you like to search for?"
            className="input-primary pl-12 pr-20 h-14 text-lg w-full"
          />
          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            className="absolute right-2 top-1/2 -translate-y-1/2 button-primary h-10"
          >
            Search
          </motion.button>
        </div>
      </div>

      {/* Filters */}
      <div className="flex items-center gap-3 mb-8">
        <motion.button
          whileHover={{ scale: 1.02 }}
          className="flex items-center gap-2 px-4 py-2 bg-background-secondary border border-border-primary rounded-xl text-text-secondary hover:text-text-primary transition-colors"
        >
          <Filter className="w-4 h-4" />
          All Sources
        </motion.button>
        
        {['Web', 'Documents', 'Images', 'Videos'].map((filter) => (
          <motion.button
            key={filter}
            whileHover={{ scale: 1.02 }}
            className="px-4 py-2 rounded-xl text-text-secondary hover:text-text-primary hover:bg-background-secondary transition-colors"
          >
            {filter}
          </motion.button>
        ))}
      </div>

      {/* Empty State */}
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        className="text-center py-16"
      >
        <div className="w-20 h-20 bg-accent-purple/10 rounded-2xl flex items-center justify-center mx-auto mb-6">
          <SearchIcon className="w-10 h-10 text-accent-purple" />
        </div>
        
        <h2 className="text-2xl font-semibold text-text-primary mb-4">
          Start Your Search
        </h2>
        
        <p className="text-text-secondary mb-6 max-w-md mx-auto">
          Enter a query above or use the main dashboard to let our AI orchestrate your search across all sources.
        </p>
        
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={() => window.location.href = '/dashboard'}
          className="button-primary"
        >
          Go to Dashboard
        </motion.button>
      </motion.div>
    </motion.div>
  )
}

export default Search
