"""
Input Validation and Sanitization Utilities

Comprehensive input validation, sanitization, and security checking for:
- SQL injection prevention
- XSS attack prevention  
- Input length and format validation
- Data type validation
- Malicious content detection
"""

import re
import html
import json
import hashlib
from typing import Any, Dict, List, Optional, Union, Tuple
from datetime import datetime, date
from urllib.parse import urlparse
from pydantic import BaseModel, Field, validator, ValidationError

from ..config.security import SecurityConfig


class ValidationResult(BaseModel):
    """Result of input validation"""
    is_valid: bool
    sanitized_value: Any
    errors: List[str] = []
    warnings: List[str] = []
    security_flags: List[str] = []


class InputSanitizer:
    """Comprehensive input sanitization and validation"""
    
    def __init__(self):
        self.sql_patterns = [re.compile(p, re.IGNORECASE) for p in SecurityConfig.SQL_INJECTION_PATTERNS]
        self.xss_patterns = [re.compile(p, re.IGNORECASE) for p in SecurityConfig.XSS_PATTERNS]
    
    def sanitize_text(self, text: str, max_length: Optional[int] = None) -> ValidationResult:
        """Sanitize text input with XSS and injection protection"""
        if not isinstance(text, str):
            return ValidationResult(
                is_valid=False,
                sanitized_value="",
                errors=["Input must be a string"]
            )
        
        original_text = text
        errors = []
        warnings = []
        security_flags = []
        
        # Check length
        if max_length is None:
            max_length = SecurityConfig.INPUT_VALIDATION["max_text_length"]
        
        if len(text) > max_length:
            text = text[:max_length]
            warnings.append(f"Text truncated to {max_length} characters")
        
        # Check for SQL injection patterns
        for pattern in self.sql_patterns:
            if pattern.search(text):
                security_flags.append("Potential SQL injection detected")
                # In production, you might want to reject the input entirely
                break
        
        # Check for XSS patterns
        for pattern in self.xss_patterns:
            if pattern.search(text):
                security_flags.append("Potential XSS content detected")
                break
        
        # HTML escape for safety
        sanitized_text = html.escape(text.strip())
        
        # Remove null bytes and control characters
        sanitized_text = ''.join(char for char in sanitized_text if ord(char) >= 32 or char in '\n\r\t')
        
        is_valid = len(errors) == 0 and len(security_flags) == 0
        
        return ValidationResult(
            is_valid=is_valid,
            sanitized_value=sanitized_text,
            errors=errors,
            warnings=warnings,
            security_flags=security_flags
        )
    
    def sanitize_query(self, query: str) -> ValidationResult:
        """Sanitize search query with special handling for search terms"""
        if not query or not isinstance(query, str):
            return ValidationResult(
                is_valid=False,
                sanitized_value="",
                errors=["Query cannot be empty"]
            )
        
        # Basic sanitization
        result = self.sanitize_text(query, max_length=1000)
        
        if not result.is_valid:
            return result
        
        # Additional query-specific validation
        query = result.sanitized_value.strip()
        
        # Remove excessive whitespace
        query = re.sub(r'\s+', ' ', query)
        
        # Validate minimum length
        if len(query) < 1:
            return ValidationResult(
                is_valid=False,
                sanitized_value="",
                errors=["Query too short"]
            )
        
        return ValidationResult(
            is_valid=True,
            sanitized_value=query,
            warnings=result.warnings,
            security_flags=result.security_flags
        )
    
    def validate_email(self, email: str) -> ValidationResult:
        """Validate email format"""
        if not email or not isinstance(email, str):
            return ValidationResult(
                is_valid=False,
                sanitized_value="",
                errors=["Email is required"]
            )
        
        # Basic email regex
        email_pattern = re.compile(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')
        
        email = email.strip().lower()
        
        if not email_pattern.match(email):
            return ValidationResult(
                is_valid=False,
                sanitized_value="",
                errors=["Invalid email format"]
            )
        
        if len(email) > 254:  # RFC 5321 limit
            return ValidationResult(
                is_valid=False,
                sanitized_value="",
                errors=["Email too long"]
            )
        
        return ValidationResult(
            is_valid=True,
            sanitized_value=email
        )
    
    def validate_url(self, url: str) -> ValidationResult:
        """Validate URL format and safety"""
        if not url or not isinstance(url, str):
            return ValidationResult(
                is_valid=False,
                sanitized_value="",
                errors=["URL is required"]
            )
        
        try:
            parsed = urlparse(url.strip())
            
            # Must have scheme and netloc
            if not parsed.scheme or not parsed.netloc:
                return ValidationResult(
                    is_valid=False,
                    sanitized_value="",
                    errors=["Invalid URL format"]
                )
            
            # Only allow http/https
            if parsed.scheme not in ['http', 'https']:
                return ValidationResult(
                    is_valid=False,
                    sanitized_value="",
                    errors=["Only HTTP/HTTPS URLs allowed"]
                )
            
            # Check for suspicious patterns
            security_flags = []
            suspicious_patterns = ['javascript:', 'data:', 'file:', 'ftp:']
            for pattern in suspicious_patterns:
                if pattern in url.lower():
                    security_flags.append(f"Suspicious URL scheme detected: {pattern}")
            
            return ValidationResult(
                is_valid=len(security_flags) == 0,
                sanitized_value=url.strip(),
                security_flags=security_flags
            )
            
        except Exception as e:
            return ValidationResult(
                is_valid=False,
                sanitized_value="",
                errors=[f"URL parsing error: {str(e)}"]
            )
    
    def validate_json(self, json_str: str, max_size: int = 10240) -> ValidationResult:
        """Validate JSON input"""
        if not isinstance(json_str, str):
            return ValidationResult(
                is_valid=False,
                sanitized_value=None,
                errors=["Input must be a string"]
            )
        
        if len(json_str) > max_size:
            return ValidationResult(
                is_valid=False,
                sanitized_value=None,
                errors=[f"JSON too large (max {max_size} bytes)"]
            )
        
        try:
            parsed = json.loads(json_str)
            return ValidationResult(
                is_valid=True,
                sanitized_value=parsed
            )
        except json.JSONDecodeError as e:
            return ValidationResult(
                is_valid=False,
                sanitized_value=None,
                errors=[f"Invalid JSON: {str(e)}"]
            )
    
    def validate_integer(self, value: Any, min_val: Optional[int] = None, max_val: Optional[int] = None) -> ValidationResult:
        """Validate integer input with range checking"""
        try:
            if isinstance(value, str):
                int_value = int(value.strip())
            elif isinstance(value, (int, float)):
                int_value = int(value)
            else:
                return ValidationResult(
                    is_valid=False,
                    sanitized_value=None,
                    errors=["Value must be a number"]
                )
            
            errors = []
            if min_val is not None and int_value < min_val:
                errors.append(f"Value must be at least {min_val}")
            
            if max_val is not None and int_value > max_val:
                errors.append(f"Value must be at most {max_val}")
            
            return ValidationResult(
                is_valid=len(errors) == 0,
                sanitized_value=int_value,
                errors=errors
            )
            
        except (ValueError, OverflowError):
            return ValidationResult(
                is_valid=False,
                sanitized_value=None,
                errors=["Invalid number format"]
            )
    
    def validate_datetime(self, date_str: str) -> ValidationResult:
        """Validate datetime string"""
        if not isinstance(date_str, str):
            return ValidationResult(
                is_valid=False,
                sanitized_value=None,
                errors=["Date must be a string"]
            )
        
        # Try common datetime formats
        formats = [
            "%Y-%m-%d %H:%M:%S",
            "%Y-%m-%d %H:%M",
            "%Y-%m-%d",
            "%Y-%m-%dT%H:%M:%S",
            "%Y-%m-%dT%H:%M:%SZ",
            "%Y-%m-%dT%H:%M:%S.%fZ",
        ]
        
        for fmt in formats:
            try:
                dt = datetime.strptime(date_str.strip(), fmt)
                return ValidationResult(
                    is_valid=True,
                    sanitized_value=dt
                )
            except ValueError:
                continue
        
        return ValidationResult(
            is_valid=False,
            sanitized_value=None,
            errors=["Invalid date format"]
        )
    
    def sanitize_filename(self, filename: str) -> ValidationResult:
        """Sanitize filename for safe storage"""
        if not isinstance(filename, str):
            return ValidationResult(
                is_valid=False,
                sanitized_value="",
                errors=["Filename must be a string"]
            )
        
        # Remove path components
        filename = filename.split('/')[-1].split('\\')[-1]
        
        # Remove dangerous characters
        filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
        
        # Remove control characters
        filename = ''.join(char for char in filename if ord(char) >= 32)
        
        # Trim whitespace and dots (Windows restriction)
        filename = filename.strip(' .')
        
        # Ensure not empty
        if not filename:
            filename = f"file_{int(datetime.now().timestamp())}"
        
        # Limit length
        if len(filename) > 255:
            name, ext = filename.rsplit('.', 1) if '.' in filename else (filename, '')
            max_name_length = 250 - len(ext)
            filename = f"{name[:max_name_length]}.{ext}" if ext else name[:255]
        
        return ValidationResult(
            is_valid=True,
            sanitized_value=filename
        )


# Global sanitizer instance
input_sanitizer = InputSanitizer()


# Convenience functions
def sanitize_text(text: str, max_length: Optional[int] = None) -> ValidationResult:
    """Convenience function for text sanitization"""
    return input_sanitizer.sanitize_text(text, max_length)


def sanitize_query(query: str) -> ValidationResult:
    """Convenience function for query sanitization"""
    return input_sanitizer.sanitize_query(query)


def validate_email(email: str) -> ValidationResult:
    """Convenience function for email validation"""
    return input_sanitizer.validate_email(email)


def validate_url(url: str) -> ValidationResult:
    """Convenience function for URL validation"""
    return input_sanitizer.validate_url(url)


def validate_json(json_str: str, max_size: int = 10240) -> ValidationResult:
    """Convenience function for JSON validation"""
    return input_sanitizer.validate_json(json_str, max_size)


def validate_integer(value: Any, min_val: Optional[int] = None, max_val: Optional[int] = None) -> ValidationResult:
    """Convenience function for integer validation"""
    return input_sanitizer.validate_integer(value, min_val, max_val)


def sanitize_filename(filename: str) -> ValidationResult:
    """Convenience function for filename sanitization"""
    return input_sanitizer.sanitize_filename(filename)
