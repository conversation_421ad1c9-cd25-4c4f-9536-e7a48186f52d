"""
Comprehensive Error Handling System for AI-Powered Dashboard

This module provides centralized error handling, retry mechanisms, 
and graceful degradation strategies for all system components.

Features:
- Centralized error classification and handling
- Retry mechanisms with exponential backoff
- Circuit breaker pattern for external services
- Graceful degradation strategies
- User-friendly error messages
- Error logging and monitoring
- Recovery strategies
"""

import asyncio
import logging
import time
from enum import Enum
from typing import Any, Callable, Dict, List, Optional, Type, Union
from dataclasses import dataclass, field
from contextlib import asynccontextmanager
import traceback
import json
from datetime import datetime, timedelta


class ErrorSeverity(Enum):
    """Error severity levels"""
    LOW = "low"
    MEDIUM = "medium" 
    HIGH = "high"
    CRITICAL = "critical"


class ErrorCategory(Enum):
    """Error categories for classification"""
    NETWORK = "network"
    DATABASE = "database"
    LLM_API = "llm_api"
    AUTHENTICATION = "authentication"
    VALIDATION = "validation"
    CONFIGURATION = "configuration"
    SYSTEM = "system"
    BUSINESS_LOGIC = "business_logic"
    EXTERNAL_SERVICE = "external_service"
    USER_INPUT = "user_input"


@dataclass
class ErrorContext:
    """Context information for errors"""
    user_id: Optional[str] = None
    request_id: Optional[str] = None
    endpoint: Optional[str] = None
    input_data: Optional[Dict[str, Any]] = None
    timestamp: datetime = field(default_factory=datetime.utcnow)
    additional_info: Dict[str, Any] = field(default_factory=dict)


@dataclass
class RetryConfig:
    """Configuration for retry mechanisms"""
    max_attempts: int = 3
    base_delay: float = 1.0
    max_delay: float = 60.0
    exponential_base: float = 2.0
    jitter: bool = True


class DashboardError(Exception):
    """Base exception for all dashboard-related errors"""
    
    def __init__(
        self,
        message: str,
        category: ErrorCategory,
        severity: ErrorSeverity = ErrorSeverity.MEDIUM,
        context: Optional[ErrorContext] = None,
        user_message: Optional[str] = None,
        recoverable: bool = True,
        retry_after: Optional[int] = None,
        original_exception: Optional[Exception] = None
    ):
        super().__init__(message)
        self.message = message
        self.category = category
        self.severity = severity
        self.context = context or ErrorContext()
        self.user_message = user_message or self._generate_user_message()
        self.recoverable = recoverable
        self.retry_after = retry_after
        self.original_exception = original_exception
        self.timestamp = datetime.utcnow()
        
    def _generate_user_message(self) -> str:
        """Generate user-friendly error message"""
        user_messages = {
            ErrorCategory.NETWORK: "We're having trouble connecting to our services. Please try again in a moment.",
            ErrorCategory.DATABASE: "We're experiencing a temporary issue with data storage. Please try again.",
            ErrorCategory.LLM_API: "Our AI service is currently unavailable. Please try again in a few minutes.",
            ErrorCategory.AUTHENTICATION: "There was an authentication issue. Please check your credentials.",
            ErrorCategory.VALIDATION: "Please check your input and try again.",
            ErrorCategory.CONFIGURATION: "There's a configuration issue. Please contact support.",
            ErrorCategory.SYSTEM: "We're experiencing technical difficulties. Please try again later.",
            ErrorCategory.BUSINESS_LOGIC: "Unable to process your request. Please try again or contact support.",
            ErrorCategory.EXTERNAL_SERVICE: "An external service is currently unavailable. Please try again later.",
            ErrorCategory.USER_INPUT: "There was an issue with your input. Please check and try again."
        }
        return user_messages.get(self.category, "An unexpected error occurred. Please try again.")
        
    def to_dict(self) -> Dict[str, Any]:
        """Convert error to dictionary for logging/serialization"""
        return {
            'message': self.message,
            'category': self.category.value,
            'severity': self.severity.value,
            'user_message': self.user_message,
            'recoverable': self.recoverable,
            'retry_after': self.retry_after,
            'timestamp': self.timestamp.isoformat(),
            'context': {
                'user_id': self.context.user_id,
                'request_id': self.context.request_id,
                'endpoint': self.context.endpoint,
                'input_data': self.context.input_data,
                'additional_info': self.context.additional_info
            },
            'original_exception': str(self.original_exception) if self.original_exception else None,
            'traceback': traceback.format_exc() if self.original_exception else None
        }


# Specific error types
class NetworkError(DashboardError):
    """Network-related errors"""
    def __init__(self, message: str, **kwargs):
        super().__init__(message, ErrorCategory.NETWORK, **kwargs)


class DatabaseError(DashboardError):
    """Database-related errors"""
    def __init__(self, message: str, **kwargs):
        super().__init__(message, ErrorCategory.DATABASE, **kwargs)


class LLMAPIError(DashboardError):
    """LLM API-related errors"""
    def __init__(self, message: str, **kwargs):
        super().__init__(message, ErrorCategory.LLM_API, **kwargs)


class AuthenticationError(DashboardError):
    """Authentication-related errors"""
    def __init__(self, message: str, **kwargs):
        kwargs.setdefault('severity', ErrorSeverity.HIGH)
        kwargs.setdefault('recoverable', False)
        super().__init__(message, ErrorCategory.AUTHENTICATION, **kwargs)


class ValidationError(DashboardError):
    """Validation-related errors"""
    def __init__(self, message: str, **kwargs):
        kwargs.setdefault('severity', ErrorSeverity.LOW)
        super().__init__(message, ErrorCategory.VALIDATION, **kwargs)


class ConfigurationError(DashboardError):
    """Configuration-related errors"""
    def __init__(self, message: str, **kwargs):
        kwargs.setdefault('severity', ErrorSeverity.HIGH)
        kwargs.setdefault('recoverable', False)
        super().__init__(message, ErrorCategory.CONFIGURATION, **kwargs)


class CircuitBreakerState(Enum):
    """Circuit breaker states"""
    CLOSED = "closed"
    OPEN = "open"
    HALF_OPEN = "half_open"


class CircuitBreaker:
    """Circuit breaker for external service calls"""
    
    def __init__(
        self,
        failure_threshold: int = 5,
        recovery_timeout: int = 60,
        expected_exception: Type[Exception] = Exception
    ):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.expected_exception = expected_exception
        self.failure_count = 0
        self.last_failure_time: Optional[float] = None
        self.state = CircuitBreakerState.CLOSED
        
    def _can_attempt_call(self) -> bool:
        """Check if a call can be attempted"""
        if self.state == CircuitBreakerState.CLOSED:
            return True
        elif self.state == CircuitBreakerState.OPEN:
            if (
                self.last_failure_time and
                time.time() - self.last_failure_time >= self.recovery_timeout
            ):
                self.state = CircuitBreakerState.HALF_OPEN
                return True
            return False
        else:  # HALF_OPEN
            return True
            
    def _record_success(self):
        """Record a successful call"""
        self.failure_count = 0
        self.state = CircuitBreakerState.CLOSED
        
    def _record_failure(self):
        """Record a failed call"""
        self.failure_count += 1
        self.last_failure_time = time.time()
        
        if self.failure_count >= self.failure_threshold:
            self.state = CircuitBreakerState.OPEN
            
    async def call(self, func: Callable, *args, **kwargs) -> Any:
        """Execute function with circuit breaker protection"""
        if not self._can_attempt_call():
            raise NetworkError(
                f"Circuit breaker is OPEN. Service unavailable.",
                retry_after=self.recovery_timeout
            )
            
        try:
            result = await func(*args, **kwargs) if asyncio.iscoroutinefunction(func) else func(*args, **kwargs)
            self._record_success()
            return result
            
        except self.expected_exception as e:
            self._record_failure()
            
            if self.state == CircuitBreakerState.HALF_OPEN:
                self.state = CircuitBreakerState.OPEN
                
            raise NetworkError(
                f"Service call failed: {str(e)}",
                original_exception=e,
                retry_after=self.recovery_timeout if self.state == CircuitBreakerState.OPEN else None
            )


class RetryManager:
    """Manages retry logic with exponential backoff"""
    
    @staticmethod
    def calculate_delay(attempt: int, config: RetryConfig) -> float:
        """Calculate delay for next retry attempt"""
        delay = config.base_delay * (config.exponential_base ** (attempt - 1))
        delay = min(delay, config.max_delay)
        
        if config.jitter:
            import random
            delay *= random.uniform(0.5, 1.5)
            
        return delay
        
    @staticmethod
    async def retry_async(
        func: Callable,
        config: RetryConfig,
        retryable_exceptions: tuple = (Exception,),
        *args,
        **kwargs
    ) -> Any:
        """Retry an async function with exponential backoff"""
        last_exception = None
        
        for attempt in range(1, config.max_attempts + 1):
            try:
                return await func(*args, **kwargs)
                
            except retryable_exceptions as e:
                last_exception = e
                
                if attempt == config.max_attempts:
                    break
                    
                delay = RetryManager.calculate_delay(attempt, config)
                logging.warning(
                    f"Attempt {attempt} failed, retrying in {delay:.2f}s: {str(e)}"
                )
                await asyncio.sleep(delay)
                
        # All retries exhausted
        raise NetworkError(
            f"All retry attempts exhausted after {config.max_attempts} tries",
            original_exception=last_exception,
            retry_after=60  # Suggest retrying after 1 minute
        )


class ErrorHandler:
    """Centralized error handling and logging"""
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        self.logger = logger or logging.getLogger(__name__)
        self.error_counts: Dict[str, int] = {}
        self.circuit_breakers: Dict[str, CircuitBreaker] = {}
        
    def get_circuit_breaker(self, service_name: str) -> CircuitBreaker:
        """Get or create circuit breaker for a service"""
        if service_name not in self.circuit_breakers:
            self.circuit_breakers[service_name] = CircuitBreaker()
        return self.circuit_breakers[service_name]
        
    def log_error(self, error: DashboardError):
        """Log error with appropriate level"""
        error_data = error.to_dict()
        
        # Increment error count for monitoring
        error_key = f"{error.category.value}:{error.message[:50]}"
        self.error_counts[error_key] = self.error_counts.get(error_key, 0) + 1
        
        # Log based on severity
        if error.severity in [ErrorSeverity.CRITICAL, ErrorSeverity.HIGH]:
            self.logger.error(json.dumps(error_data, indent=2))
        elif error.severity == ErrorSeverity.MEDIUM:
            self.logger.warning(json.dumps(error_data, indent=2))
        else:
            self.logger.info(json.dumps(error_data, indent=2))
            
    def handle_error(self, error: Exception, context: Optional[ErrorContext] = None) -> DashboardError:
        """Convert generic exceptions to DashboardError and log"""
        if isinstance(error, DashboardError):
            dashboard_error = error
        else:
            # Map common exceptions to appropriate DashboardError types
            dashboard_error = self._map_exception_to_dashboard_error(error, context)
            
        self.log_error(dashboard_error)
        return dashboard_error
        
    def _map_exception_to_dashboard_error(
        self, 
        error: Exception, 
        context: Optional[ErrorContext] = None
    ) -> DashboardError:
        """Map common exceptions to DashboardError types"""
        import sqlite3
        import aiohttp
        from pydantic import ValidationError as PydanticValidationError
        
        # Database errors
        if isinstance(error, sqlite3.Error):
            return DatabaseError(
                f"Database operation failed: {str(error)}",
                context=context,
                original_exception=error
            )
            
        # Network errors
        if isinstance(error, (aiohttp.ClientError, ConnectionError, TimeoutError)):
            return NetworkError(
                f"Network operation failed: {str(error)}",
                context=context,
                original_exception=error
            )
            
        # Validation errors
        if isinstance(error, (PydanticValidationError, ValueError)):
            return ValidationError(
                f"Input validation failed: {str(error)}",
                context=context,
                original_exception=error,
                severity=ErrorSeverity.LOW
            )
            
        # Configuration errors
        if isinstance(error, (KeyError, AttributeError)) and "config" in str(error).lower():
            return ConfigurationError(
                f"Configuration error: {str(error)}",
                context=context,
                original_exception=error
            )
            
        # Generic system error
        return DashboardError(
            f"Unexpected error: {str(error)}",
            category=ErrorCategory.SYSTEM,
            context=context,
            original_exception=error,
            severity=ErrorSeverity.MEDIUM
        )


# Global error handler instance
error_handler = ErrorHandler()


# Decorator for automatic error handling
def handle_errors(
    context_func: Optional[Callable[..., ErrorContext]] = None,
    retryable: bool = False,
    retry_config: Optional[RetryConfig] = None
):
    """Decorator for automatic error handling"""
    def decorator(func: Callable) -> Callable:
        async def async_wrapper(*args, **kwargs):
            context = context_func(*args, **kwargs) if context_func else None
            
            if retryable and retry_config:
                try:
                    return await RetryManager.retry_async(
                        func, retry_config, (DashboardError,), *args, **kwargs
                    )
                except Exception as e:
                    dashboard_error = error_handler.handle_error(e, context)
                    raise dashboard_error
            else:
                try:
                    return await func(*args, **kwargs)
                except Exception as e:
                    dashboard_error = error_handler.handle_error(e, context)
                    raise dashboard_error
                    
        def sync_wrapper(*args, **kwargs):
            context = context_func(*args, **kwargs) if context_func else None
            
            try:
                return func(*args, **kwargs)
            except Exception as e:
                dashboard_error = error_handler.handle_error(e, context)
                raise dashboard_error
                
        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper
    return decorator


@asynccontextmanager
async def error_boundary(
    context: Optional[ErrorContext] = None,
    fallback_result: Any = None,
    suppress_errors: bool = False
):
    """Context manager for error boundaries with fallback"""
    try:
        yield fallback_result if suppress_errors else None
    except DashboardError as e:
        error_handler.log_error(e)
        if not suppress_errors:
            raise
        if fallback_result is not None:
            yield fallback_result
    except Exception as e:
        dashboard_error = error_handler.handle_error(e, context)
        if not suppress_errors:
            raise dashboard_error
        if fallback_result is not None:
            yield fallback_result


# Graceful degradation strategies
class DegradationLevel(Enum):
    """Levels of service degradation"""
    NORMAL = "normal"
    REDUCED = "reduced"
    MINIMAL = "minimal"
    EMERGENCY = "emergency"


class GracefulDegradation:
    """Manages graceful service degradation"""
    
    def __init__(self):
        self.current_level = DegradationLevel.NORMAL
        self.degradation_triggers = {
            DegradationLevel.REDUCED: 3,  # 3 consecutive failures
            DegradationLevel.MINIMAL: 5,  # 5 consecutive failures  
            DegradationLevel.EMERGENCY: 10  # 10 consecutive failures
        }
        self.consecutive_failures = 0
        
    def record_failure(self):
        """Record a service failure"""
        self.consecutive_failures += 1
        self._update_degradation_level()
        
    def record_success(self):
        """Record a service success"""
        if self.consecutive_failures > 0:
            self.consecutive_failures -= 1
            self._update_degradation_level()
            
    def _update_degradation_level(self):
        """Update degradation level based on failure count"""
        if self.consecutive_failures >= self.degradation_triggers[DegradationLevel.EMERGENCY]:
            self.current_level = DegradationLevel.EMERGENCY
        elif self.consecutive_failures >= self.degradation_triggers[DegradationLevel.MINIMAL]:
            self.current_level = DegradationLevel.MINIMAL
        elif self.consecutive_failures >= self.degradation_triggers[DegradationLevel.REDUCED]:
            self.current_level = DegradationLevel.REDUCED
        else:
            self.current_level = DegradationLevel.NORMAL
            
    def get_fallback_response(self, operation: str) -> Dict[str, Any]:
        """Get appropriate fallback response based on degradation level"""
        fallbacks = {
            DegradationLevel.NORMAL: None,
            DegradationLevel.REDUCED: {
                "message": "Service is experiencing high load. Response may be delayed.",
                "degraded": True
            },
            DegradationLevel.MINIMAL: {
                "message": "Service is in minimal mode. Limited functionality available.",
                "degraded": True,
                "features_disabled": ["real_time_updates", "advanced_search"]
            },
            DegradationLevel.EMERGENCY: {
                "message": "Service is in emergency mode. Please try again later.",
                "degraded": True,
                "features_disabled": ["ai_processing", "real_time_updates", "advanced_search"],
                "retry_after": 300  # 5 minutes
            }
        }
        
        return fallbacks.get(self.current_level)


# Global degradation manager
degradation_manager = GracefulDegradation()


def get_error_summary() -> Dict[str, Any]:
    """Get summary of system errors for monitoring"""
    return {
        "error_counts": error_handler.error_counts,
        "degradation_level": degradation_manager.current_level.value,
        "consecutive_failures": degradation_manager.consecutive_failures,
        "circuit_breaker_states": {
            name: breaker.state.value 
            for name, breaker in error_handler.circuit_breakers.items()
        },
        "timestamp": datetime.utcnow().isoformat()
    }
