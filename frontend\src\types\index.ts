// Task 28: TypeScript Definitions - Complete Type Safety
// API Response Interfaces

export interface APIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  timestamp: string;
}

export interface PaginatedResponse<T> extends APIResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// AI Service Types
export interface AIProvider {
  id: string;
  name: string;
  models: AIModel[];
  supportedFeatures: AIFeature[];
  pricing: PricingTier;
}

export interface AIModel {
  id: string;
  name: string;
  description: string;
  contextLength: number;
  tokensPerMinute: number;
  costPer1kTokens: number;
  capabilities: ModelCapability[];
}

export type AIFeature = 'chat' | 'embeddings' | 'functions' | 'vision' | 'code' | 'reasoning';
export type ModelCapability = 'text-generation' | 'code-completion' | 'summarization' | 'analysis' | 'creative-writing';
export type PricingTier = 'free' | 'hobby' | 'pro' | 'enterprise';

export interface AIRequest {
  query: string;
  context?: string;
  model?: string;
  provider?: string;
  options?: AIRequestOptions;
}

export interface AIRequestOptions {
  temperature?: number;
  maxTokens?: number;
  topP?: number;
  frequencyPenalty?: number;
  presencePenalty?: number;
  stream?: boolean;
  functions?: AIFunction[];
}

export interface AIFunction {
  name: string;
  description: string;
  parameters: Record<string, any>;
}

export interface AIResponse {
  id: string;
  content: string;
  type: AIResponseType;
  confidence: number;
  sources: SourceLink[];
  metadata: AIResponseMetadata;
  usage: TokenUsage;
  generatedAt: string;
}

export type AIResponseType = 'text' | 'code' | 'analysis' | 'summary' | 'creative' | 'reasoning';

export interface SourceLink {
  title: string;
  url: string;
  snippet: string;
  relevance: number;
  type: SourceType;
}

export type SourceType = 'web' | 'document' | 'database' | 'api' | 'knowledge-base';

export interface AIResponseMetadata {
  model: string;
  provider: string;
  processingTime: number;
  contentFiltered: boolean;
  warnings?: string[];
}

export interface TokenUsage {
  promptTokens: number;
  completionTokens: number;
  totalTokens: number;
  cost?: number;
}

// Search and Embeddings Types
export interface SearchQuery {
  query: string;
  filters?: SearchFilters;
  options?: SearchOptions;
}

export interface SearchFilters {
  dateRange?: DateRange;
  sources?: SourceType[];
  contentTypes?: string[];
  tags?: string[];
  authors?: string[];
}

export interface SearchOptions {
  limit?: number;
  offset?: number;
  sortBy?: SearchSortBy;
  sortOrder?: 'asc' | 'desc';
  includeSnippets?: boolean;
  highlightMatches?: boolean;
}

export type SearchSortBy = 'relevance' | 'date' | 'title' | 'author' | 'popularity';

export interface SearchResult {
  id: string;
  title: string;
  content: string;
  snippet: string;
  url?: string;
  score: number;
  highlights: string[];
  metadata: SearchResultMetadata;
}

export interface SearchResultMetadata {
  source: SourceType;
  author?: string;
  publishedAt?: string;
  updatedAt?: string;
  tags: string[];
  contentType: string;
  wordCount: number;
}

export interface EmbeddingModel {
  id: string;
  name: string;
  dimensions: number;
  maxTokens: number;
  provider: string;
  costPer1kTokens: number;
}

export interface EmbeddingStatus {
  isConnected: boolean;
  model: EmbeddingModel;
  indexStats: {
    totalDocuments: number;
    totalVectors: number;
    lastUpdated: string;
    indexSize: string;
  };
  performance: {
    avgQueryTime: number;
    throughput: number;
    errorRate: number;
  };
}

// Calendar and Event Types
export interface CalendarEvent {
  id: string;
  title: string;
  description?: string;
  startDate: Date;
  endDate: Date;
  allDay: boolean;
  category: EventCategory;
  priority: EventPriority;
  attendees?: Attendee[];
  location?: string;
  recurrence?: RecurrenceRule;
  aiGenerated: boolean;
  aiConfidence?: number;
  aiSuggestions?: AISuggestion[];
  metadata: EventMetadata;
}

export type EventCategory = 'work' | 'personal' | 'health' | 'social' | 'travel' | 'education' | 'other';
export type EventPriority = 'low' | 'medium' | 'high' | 'urgent';

export interface Attendee {
  email: string;
  name: string;
  status: AttendeeStatus;
  role: AttendeeRole;
}

export type AttendeeStatus = 'pending' | 'accepted' | 'declined' | 'tentative';
export type AttendeeRole = 'organizer' | 'required' | 'optional';

export interface RecurrenceRule {
  frequency: RecurrenceFrequency;
  interval: number;
  endDate?: Date;
  count?: number;
  daysOfWeek?: number[];
  dayOfMonth?: number;
}

export type RecurrenceFrequency = 'daily' | 'weekly' | 'monthly' | 'yearly';

export interface AISuggestion {
  type: SuggestionType;
  content: string;
  confidence: number;
  action?: string;
}

export type SuggestionType = 'time-optimization' | 'conflict-resolution' | 'preparation' | 'follow-up' | 'related-events';

export interface EventMetadata {
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  source: EventSource;
  tags: string[];
  importance: number;
}

export type EventSource = 'manual' | 'ai-generated' | 'imported' | 'synchronized';

export interface CalendarView {
  type: CalendarViewType;
  date: Date;
  range: DateRange;
  events: CalendarEvent[];
  settings: CalendarViewSettings;
}

export type CalendarViewType = 'month' | 'week' | 'day' | 'agenda';

export interface CalendarViewSettings {
  showWeekends: boolean;
  workingHours: TimeRange;
  timeZone: string;
  firstDayOfWeek: number;
  showAllDayEvents: boolean;
  eventHeight: number;
}

export interface TimeRange {
  start: string; // HH:mm format
  end: string;   // HH:mm format
}

export interface DateRange {
  start: Date;
  end: Date;
}

// Animation State Types
export interface AnimationState {
  isAnimating: boolean;
  phase: AnimationPhase;
  progress: number;
  duration: number;
  easing: EasingFunction;
}

export type AnimationPhase = 'idle' | 'entering' | 'present' | 'exiting';
export type EasingFunction = 'linear' | 'ease' | 'ease-in' | 'ease-out' | 'ease-in-out' | 'spring' | 'bounce';

export interface MotionConfig {
  initial?: MotionVariant;
  animate?: MotionVariant;
  exit?: MotionVariant;
  whileHover?: MotionVariant;
  whileTap?: MotionVariant;
  transition?: TransitionConfig;
}

export interface MotionVariant {
  opacity?: number;
  scale?: number;
  x?: number;
  y?: number;
  rotate?: number;
  rotateX?: number;
  rotateY?: number;
  skewX?: number;
  skewY?: number;
}

export interface TransitionConfig {
  duration?: number;
  delay?: number;
  ease?: EasingFunction;
  type?: 'tween' | 'spring' | 'inertia';
  stiffness?: number;
  damping?: number;
  mass?: number;
  velocity?: number;
}

export interface StaggerConfig {
  delayChildren?: number;
  staggerChildren?: number;
  staggerDirection?: 1 | -1;
}

// Form Validation Schemas
export interface ValidationSchema<T = any> {
  fields: Record<keyof T, FieldValidation>;
  mode: ValidationMode;
  revalidateMode: RevalidateMode;
}

export type ValidationMode = 'onSubmit' | 'onBlur' | 'onChange' | 'onTouched' | 'all';
export type RevalidateMode = 'onSubmit' | 'onBlur' | 'onChange';

export interface FieldValidation {
  required?: boolean | string;
  minLength?: number | { value: number; message: string };
  maxLength?: number | { value: number; message: string };
  min?: number | { value: number; message: string };
  max?: number | { value: number; message: string };
  pattern?: RegExp | { value: RegExp; message: string };
  validate?: ValidationFunction | Record<string, ValidationFunction>;
  deps?: string[];
}

export type ValidationFunction<T = any> = (value: T, formData: any) => boolean | string | Promise<boolean | string>;

export interface ValidationError {
  field: string;
  type: ValidationErrorType;
  message: string;
  value?: any;
}

export type ValidationErrorType = 'required' | 'minLength' | 'maxLength' | 'min' | 'max' | 'pattern' | 'custom';

export interface FormState<T = any> {
  values: T;
  errors: Record<keyof T, ValidationError[]>;
  touched: Record<keyof T, boolean>;
  isValid: boolean;
  isSubmitting: boolean;
  isValidating: boolean;
  isDirty: boolean;
  submitCount: number;
}

// WebSocket Message Types
export interface WebSocketMessage<T = any> {
  type: MessageType;
  id: string;
  timestamp: string;
  data: T;
  metadata?: MessageMetadata;
}

export type MessageType = 
  | 'event-created'
  | 'event-updated' 
  | 'event-deleted'
  | 'ai-response-stream'
  | 'ai-response-complete'
  | 'search-results'
  | 'embedding-status'
  | 'system-notification'
  | 'error'
  | 'heartbeat';

export interface MessageMetadata {
  sender: string;
  priority: MessagePriority;
  requiresAck: boolean;
  retryCount?: number;
  expiresAt?: string;
}

export type MessagePriority = 'low' | 'normal' | 'high' | 'critical';

export interface WebSocketConfig {
  url: string;
  protocols?: string[];
  reconnectAttempts: number;
  reconnectInterval: number;
  heartbeatInterval: number;
  messageTimeout: number;
}

export interface ConnectionState {
  status: ConnectionStatus;
  lastConnected?: string;
  lastDisconnected?: string;
  reconnectAttempts: number;
  latency?: number;
  error?: string;
}

export type ConnectionStatus = 'connecting' | 'connected' | 'disconnecting' | 'disconnected' | 'error';

// User and Settings Types
export interface UserSettings {
  profile: UserProfile;
  preferences: UserPreferences;
  api: APISettings;
  calendar: CalendarSettings;
  notifications: NotificationSettings;
  privacy: PrivacySettings;
}

export interface UserProfile {
  id: string;
  email: string;
  name: string;
  avatar?: string;
  timezone: string;
  locale: string;
  createdAt: string;
  lastLoginAt: string;
}

export interface UserPreferences {
  theme: ThemeMode;
  language: string;
  dateFormat: string;
  timeFormat: string;
  workingHours: TimeRange;
  defaultView: CalendarViewType;
  autoSave: boolean;
  keyboardShortcuts: boolean;
}

export type ThemeMode = 'light' | 'dark' | 'system';

export interface APISettings {
  providers: ProviderConfig[];
  defaultProvider: string;
  timeout: number;
  retryAttempts: number;
  rateLimiting: RateLimitConfig;
}

export interface ProviderConfig {
  id: string;
  name: string;
  apiKey: string;
  baseUrl?: string;
  models: string[];
  isEnabled: boolean;
  customHeaders?: Record<string, string>;
}

export interface RateLimitConfig {
  requestsPerMinute: number;
  tokensPerMinute: number;
  concurrentRequests: number;
}

export interface CalendarSettings {
  defaultDuration: number;
  workingDays: number[];
  workingHours: TimeRange;
  timezone: string;
  reminderDefaults: ReminderConfig[];
  syncSettings: SyncSettings;
}

export interface ReminderConfig {
  type: ReminderType;
  timing: number;
  method: NotificationMethod;
}

export type ReminderType = 'before-event' | 'at-event' | 'after-event';
export type NotificationMethod = 'browser' | 'email' | 'webhook';

export interface SyncSettings {
  enabled: boolean;
  providers: string[];
  conflictResolution: ConflictResolution;
  syncInterval: number;
}

export type ConflictResolution = 'local-wins' | 'remote-wins' | 'manual' | 'merge';

export interface NotificationSettings {
  browser: boolean;
  email: boolean;
  sound: boolean;
  types: NotificationType[];
  quietHours: TimeRange;
}

export type NotificationType = 'ai-response' | 'calendar-event' | 'system-update' | 'error' | 'reminder';

export interface PrivacySettings {
  dataRetention: number; // days
  anonymizeData: boolean;
  shareUsageStats: boolean;
  exportData: boolean;
  deleteAccount: boolean;
}

// Storage and Persistence Types
export interface StorageAdapter {
  get<T>(key: string): Promise<T | null>;
  set<T>(key: string, value: T): Promise<void>;
  remove(key: string): Promise<void>;
  clear(): Promise<void>;
  keys(): Promise<string[]>;
  size(): Promise<number>;
}

export interface PersistenceConfig {
  local: LocalStorageConfig;
  remote: RemoteStorageConfig;
  sync: SyncConfig;
}

export interface LocalStorageConfig {
  prefix: string;
  encryption: boolean;
  compression: boolean;
  maxSize: number; // bytes
  ttl: number; // seconds
}

export interface RemoteStorageConfig {
  endpoint: string;
  apiKey: string;
  timeout: number;
  retryAttempts: number;
  batchSize: number;
}

export interface SyncConfig {
  enabled: boolean;
  interval: number; // seconds
  conflictResolution: ConflictResolution;
  offlineMode: boolean;
}

export interface StorageEntry<T = any> {
  key: string;
  value: T;
  timestamp: string;
  ttl?: number;
  metadata?: StorageMetadata;
}

export interface StorageMetadata {
  size: number;
  checksum: string;
  compressed: boolean;
  encrypted: boolean;
  synced: boolean;
}

// Utility Types
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

export type AsyncResult<T> = Promise<{ success: true; data: T } | { success: false; error: string }>;

export type EventHandler<T = any> = (event: T) => void | Promise<void>;

export type Callback<T = void> = () => T | Promise<T>;

// Error Types
export interface AppError extends Error {
  code: ErrorCode;
  context?: Record<string, any>;
  timestamp: string;
  retryable: boolean;
}

export type ErrorCode = 
  | 'NETWORK_ERROR'
  | 'API_ERROR'
  | 'VALIDATION_ERROR'
  | 'AUTHENTICATION_ERROR'
  | 'AUTHORIZATION_ERROR'
  | 'NOT_FOUND_ERROR'
  | 'RATE_LIMIT_ERROR'
  | 'TIMEOUT_ERROR'
  | 'INTERNAL_ERROR';

// Component Props Types
export interface BaseComponentProps {
  className?: string;
  style?: React.CSSProperties;
  'data-testid'?: string;
}

export interface AnimatedComponentProps extends BaseComponentProps {
  animate?: boolean;
  animationDelay?: number;
  animationDuration?: number;
  motionConfig?: MotionConfig;
}

// Global State Types
export interface AppState {
  user: UserState;
  calendar: CalendarState;
  ai: AIState;
  settings: SettingsState;
  ui: UIState;
}

export interface UserState {
  profile: UserProfile | null;
  isAuthenticated: boolean;
  permissions: string[];
  lastActivity: string;
}

export interface CalendarState {
  currentView: CalendarView;
  selectedDate: Date;
  events: CalendarEvent[];
  loading: boolean;
  error: string | null;
}

export interface AIState {
  activeSession: AISession | null;
  history: AIResponse[];
  providers: AIProvider[];
  loading: boolean;
  error: string | null;
}

export interface AISession {
  id: string;
  startedAt: string;
  messages: AIMessage[];
  context: string;
  model: string;
  provider: string;
}

export interface AIMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: string;
  metadata?: AIResponseMetadata;
}

export interface SettingsState {
  current: UserSettings;
  loading: boolean;
  error: string | null;
  unsavedChanges: boolean;
}

export interface UIState {
  theme: ThemeMode;
  sidebarOpen: boolean;
  modals: ModalState[];
  notifications: Notification[];
  loading: Record<string, boolean>;
}

export interface ModalState {
  id: string;
  type: string;
  props: Record<string, any>;
  isOpen: boolean;
}

export interface Notification {
  id: string;
  type: NotificationType;
  title: string;
  message: string;
  timestamp: string;
  read: boolean;
  actions?: NotificationAction[];
}

export interface NotificationAction {
  label: string;
  action: () => void;
  style?: 'primary' | 'secondary' | 'danger';
}
