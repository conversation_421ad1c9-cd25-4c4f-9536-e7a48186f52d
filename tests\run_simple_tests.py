"""
Simple Test Runner for API-based Integration Testing
Task 40: Browser-free integration testing

PATTERN: API-focused testing without browser dependencies
Features:
- API endpoint testing
- Security validation
- Performance benchmarking
- No browser automation dependencies
"""

import asyncio
import json
import time
import logging
from datetime import datetime
from pathlib import Path
import sys
import os

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from httpx import AsyncClient
from tests.e2e.test_comprehensive import E2ETestSuite
from tests.e2e.test_security import SecurityTestSuite

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

async def run_simple_tests():
    """Run simplified API-based integration tests"""
    
    test_results = {
        "timestamp": datetime.utcnow().isoformat(),
        "tests": [],
        "overall_success": False,
        "summary": {}
    }
    
    logger.info("Starting API-based integration tests...")
    
    # Test 1: Comprehensive E2E API Tests
    logger.info("Running comprehensive API tests...")
    try:
        suite = E2ETestSuite()
        await suite.setup()
        
        comprehensive_results = await suite.run_comprehensive_test_suite()
        test_results["tests"].append({
            "suite": "comprehensive",
            "results": comprehensive_results,
            "success": comprehensive_results.get("overall_success", False)
        })
        
        await suite.teardown()
        logger.info("Comprehensive tests completed")
        
    except Exception as e:
        logger.error(f"Comprehensive tests failed: {e}")
        test_results["tests"].append({
            "suite": "comprehensive", 
            "error": str(e),
            "success": False
        })
    
    # Test 2: Security Tests
    logger.info("Running security tests...")
    try:
        security_suite = SecurityTestSuite()
        await security_suite.setup()
        
        security_results = await security_suite.run_security_test_suite()
        test_results["tests"].append({
            "suite": "security",
            "results": security_results,
            "success": security_results.get("overall_success", False)
        })
        
        await security_suite.teardown()
        logger.info("Security tests completed")
        
    except Exception as e:
        logger.error(f"Security tests failed: {e}")
        test_results["tests"].append({
            "suite": "security",
            "error": str(e), 
            "success": False
        })
    
    # Calculate overall results
    successful_tests = sum(1 for test in test_results["tests"] if test.get("success", False))
    total_tests = len(test_results["tests"])
    
    test_results["summary"] = {
        "total_suites": total_tests,
        "successful_suites": successful_tests,
        "success_rate": (successful_tests / total_tests * 100) if total_tests > 0 else 0
    }
    
    test_results["overall_success"] = successful_tests >= (total_tests * 0.8)  # 80% success rate
    
    # Save results
    timestamp = datetime.utcnow().strftime("%Y%m%d_%H%M%S")
    results_file = f"simple_test_results_{timestamp}.json"
    
    with open(results_file, 'w') as f:
        json.dump(test_results, f, indent=2, default=str)
    
    # Print summary
    print("\n" + "="*60)
    print("API INTEGRATION TEST RESULTS")
    print("="*60)
    print(f"Total Suites: {total_tests}")
    print(f"Successful: {successful_tests}")
    print(f"Success Rate: {test_results['summary']['success_rate']:.1f}%")
    print(f"Overall Success: {'PASS' if test_results['overall_success'] else 'FAIL'}")
    print(f"Results saved to: {results_file}")
    print("="*60)
    
    # Individual suite results
    for test in test_results["tests"]:
        suite_name = test["suite"]
        success = test.get("success", False)
        status = "PASS" if success else "FAIL"
        
        print(f"{suite_name.upper()} Suite: {status}")
        
        if "error" in test:
            print(f"  Error: {test['error']}")
        elif "results" in test:
            results = test["results"]
            if suite_name == "comprehensive":
                summary = results.get("summary", {})
                print(f"  Success Rate: {summary.get('success_rate', 0):.1f}%")
            elif suite_name == "security":
                score = results.get("security_score", 0)
                print(f"  Security Score: {score:.1f}/100")
    
    return test_results["overall_success"]

if __name__ == "__main__":
    try:
        success = asyncio.run(run_simple_tests())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("Tests interrupted by user")
        sys.exit(2)
    except Exception as e:
        logger.error(f"Test runner failed: {e}")
        sys.exit(3)
