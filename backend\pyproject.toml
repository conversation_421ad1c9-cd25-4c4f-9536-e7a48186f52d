[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "ai-powered-dashboard"
version = "1.0.0"
description = "AI-Powered Dashboard with Visual Transparency & Smooth Animations"
authors = [
    {name = "OneSearch", email = "<EMAIL>"},
]
dependencies = [
    "fastapi==0.104.1",
    "uvicorn[standard]==0.24.0",
    "python-multipart==0.0.6",
    "sqlalchemy==2.0.23",
    "alembic==1.12.1",
    "aiosqlite==0.19.0",
    "mirascope==1.0.0",
    "openai==1.6.1",
    "pydantic==2.5.0",
    "aiohttp==3.9.1",
    "httpx==0.25.2",
    "python-dotenv==1.0.0",
    "python-dateutil==2.8.2",
    "typing-extensions==4.8.0",
]

[project.optional-dependencies]
dev = [
    "pytest==7.4.3",
    "pytest-asyncio==0.21.1",
    "black==23.11.0",
    "ruff==0.1.6",
    "mypy==1.7.1",
]

[tool.black]
line-length = 88
target-version = ['py311']

[tool.ruff]
line-length = 88
target-version = "py311"

[tool.mypy]
python_version = "3.11"
strict = true
warn_return_any = true
warn_unused_configs = true
