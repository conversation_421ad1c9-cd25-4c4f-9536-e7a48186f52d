"""
Enhanced Input Validation and Sanitization System

PATTERN: Comprehensive input validation with security-first approach
Features:
- SQL injection prevention
- XSS attack mitigation  
- Data sanitization and validation
- File upload security
- JSON schema validation
- Email and URL validation
"""

import re
import json
import html
import base64
import hashlib
from typing import Any, Dict, List, Optional, Union, Tuple
from dataclasses import dataclass
from urllib.parse import urlparse
from pathlib import Path

from ..config.security import SecurityConfig, get_security_config

@dataclass
class ValidationResult:
    """Result of input validation"""
    is_valid: bool
    sanitized_value: Any
    original_value: Any
    errors: List[str]
    warnings: List[str]
    security_flags: List[str]
    metadata: Dict[str, Any]

class AdvancedInputSanitizer:
    """Advanced input sanitization with comprehensive security checks"""
    
    def __init__(self, security_config: SecurityConfig = None):
        self.config = security_config or get_security_config()
        self._compile_patterns()
    
    def _compile_patterns(self):
        """Compile regex patterns for performance"""
        self.sql_patterns = [
            re.compile(pattern, re.IGNORECASE) 
            for pattern in self.config.SQL_INJECTION_PATTERNS
        ]
        self.xss_patterns = [
            re.compile(pattern, re.IGNORECASE) 
            for pattern in self.config.XSS_PATTERNS
        ]
        self.sensitive_patterns = [
            re.compile(pattern) 
            for pattern in self.config.SENSITIVE_PATTERNS
        ]
    
    def sanitize_text(self, text: str, max_length: int = None) -> ValidationResult:
        """Comprehensive text sanitization"""
        if not isinstance(text, str):
            return ValidationResult(
                is_valid=False,
                sanitized_value="",
                original_value=text,
                errors=[f"Expected string, got {type(text)}"],
                warnings=[],
                security_flags=[],
                metadata={}
            )
        
        original_text = text
        errors = []
        warnings = []
        security_flags = []
        
        # Length validation
        max_len = max_length or self.config.INPUT_VALIDATION["max_text_length"]
        if len(text) > max_len:
            errors.append(f"Text exceeds maximum length of {max_len} characters")
            text = text[:max_len]
            warnings.append("Text was truncated")
        
        # SQL injection detection
        sql_matches = []
        for pattern in self.sql_patterns:
            matches = pattern.findall(text)
            if matches:
                sql_matches.extend(matches)
                security_flags.append("sql_injection_detected")
        
        if sql_matches:
            errors.append("Potential SQL injection detected")
        
        # XSS detection and mitigation
        xss_matches = []
        for pattern in self.xss_patterns:
            matches = pattern.findall(text)
            if matches:
                xss_matches.extend(matches)
                security_flags.append("xss_detected")
        
        # HTML escape for XSS prevention
        if xss_matches:
            text = html.escape(text)
            warnings.append("HTML content was escaped for security")
        
        # Sensitive data detection
        sensitive_matches = []
        for pattern in self.sensitive_patterns:
            matches = pattern.findall(text)
            if matches:
                sensitive_matches.extend(matches)
                security_flags.append("sensitive_data_detected")
        
        if sensitive_matches:
            warnings.append("Potential sensitive data detected")
        
        # Basic sanitization
        # Remove null bytes
        text = text.replace('\x00', '')
        
        # Normalize whitespace
        text = re.sub(r'\s+', ' ', text.strip())
        
        # Remove or escape control characters
        text = ''.join(char for char in text if ord(char) >= 32 or char in '\n\r\t')
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            sanitized_value=text,
            original_value=original_text,
            errors=errors,
            warnings=warnings,
            security_flags=security_flags,
            metadata={
                "length_original": len(original_text),
                "length_sanitized": len(text),
                "sql_matches": len(sql_matches),
                "xss_matches": len(xss_matches),
                "sensitive_matches": len(sensitive_matches)
            }
        )
    
    def sanitize_query(self, query: str) -> ValidationResult:
        """Sanitize search queries with specific rules"""
        return self.sanitize_text(
            query, 
            max_length=self.config.INPUT_VALIDATION["max_query_length"]
        )
    
    def validate_email(self, email: str) -> ValidationResult:
        """Validate email addresses"""
        errors = []
        warnings = []
        security_flags = []
        
        if not isinstance(email, str):
            return ValidationResult(
                is_valid=False,
                sanitized_value="",
                original_value=email,
                errors=[f"Expected string, got {type(email)}"],
                warnings=[], security_flags=[], metadata={}
            )
        
        # Length check
        if len(email) > self.config.INPUT_VALIDATION["max_email_length"]:
            errors.append("Email address too long")
        
        # Basic format validation
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_pattern, email):
            errors.append("Invalid email format")
            normalized_email = email
        else:
            normalized_email = email.lower().strip()
        
        # Check for suspicious patterns
        suspicious_domains = ['tempmail', 'guerrillamail', '10minutemail']
        domain = email.split('@')[-1] if '@' in email else ''
        if any(sus in domain.lower() for sus in suspicious_domains):
            security_flags.append("suspicious_email_domain")
            warnings.append("Email from potentially suspicious domain")
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            sanitized_value=normalized_email,
            original_value=email,
            errors=errors,
            warnings=warnings,
            security_flags=security_flags,
            metadata={"domain": domain}
        )
    
    def validate_url(self, url: str) -> ValidationResult:
        """Validate URLs with security checks"""
        errors = []
        warnings = []
        security_flags = []
        
        if not isinstance(url, str):
            return ValidationResult(
                is_valid=False, sanitized_value="", original_value=url,
                errors=[f"Expected string, got {type(url)}"],
                warnings=[], security_flags=[], metadata={}
            )
        
        # Length check
        if len(url) > self.config.INPUT_VALIDATION["max_url_length"]:
            errors.append("URL too long")
        
        # Parse URL
        try:
            parsed = urlparse(url)
        except Exception as e:
            errors.append(f"Invalid URL format: {str(e)}")
            return ValidationResult(
                is_valid=False, sanitized_value=url, original_value=url,
                errors=errors, warnings=warnings, 
                security_flags=security_flags, metadata={}
            )
        
        # Scheme validation
        allowed_schemes = {'http', 'https', 'ftp', 'ftps'}
        if parsed.scheme.lower() not in allowed_schemes:
            errors.append(f"URL scheme '{parsed.scheme}' not allowed")
        
        # Check for dangerous protocols
        dangerous_schemes = {'javascript', 'vbscript', 'data', 'file'}
        if parsed.scheme.lower() in dangerous_schemes:
            security_flags.append("dangerous_url_scheme")
            errors.append("Dangerous URL scheme detected")
        
        # Check for local/private IP addresses
        hostname = parsed.hostname
        if hostname:
            try:
                import ipaddress
                ip = ipaddress.ip_address(hostname)
                if ip.is_private or ip.is_loopback:
                    security_flags.append("private_ip_access")
                    warnings.append("URL points to private/local IP address")
            except ValueError:
                pass  # Not an IP address
        
        # Check for suspicious domains
        suspicious_tlds = ['.tk', '.ml', '.ga', '.cf']
        if any(hostname.endswith(tld) for tld in suspicious_tlds):
            security_flags.append("suspicious_domain")
            warnings.append("URL uses suspicious top-level domain")
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            sanitized_value=url,
            original_value=url,
            errors=errors,
            warnings=warnings,
            security_flags=security_flags,
            metadata={
                "scheme": parsed.scheme,
                "hostname": hostname,
                "path": parsed.path
            }
        )
    
    def sanitize_filename(self, filename: str) -> ValidationResult:
        """Sanitize filenames for security"""
        errors = []
        warnings = []
        security_flags = []
        original_filename = filename
        
        if not isinstance(filename, str):
            return ValidationResult(
                is_valid=False, sanitized_value="", original_value=filename,
                errors=[f"Expected string, got {type(filename)}"],
                warnings=[], security_flags=[], metadata={}
            )
        
        # Length check
        if len(filename) > self.config.INPUT_VALIDATION["max_filename_length"]:
            errors.append("Filename too long")
            filename = filename[:self.config.INPUT_VALIDATION["max_filename_length"]]
        
        # Remove dangerous characters
        dangerous_chars = r'[<>:"/\\|?*\x00-\x1f]'
        if re.search(dangerous_chars, filename):
            security_flags.append("dangerous_filename_chars")
            filename = re.sub(dangerous_chars, '_', filename)
            warnings.append("Dangerous characters removed from filename")
        
        # Check for path traversal
        if '..' in filename or filename.startswith('/') or filename.startswith('\\'):
            security_flags.append("path_traversal_attempt")
            errors.append("Path traversal attempt detected")
        
        # Check file extension
        if '.' in filename:
            extension = '.' + filename.split('.')[-1].lower()
            if extension not in self.config.ALLOWED_EXTENSIONS:
                errors.append(f"File extension '{extension}' not allowed")
        
        # Reserved names check (Windows)
        reserved_names = {
            'CON', 'PRN', 'AUX', 'NUL', 'COM1', 'COM2', 'COM3', 'COM4',
            'COM5', 'COM6', 'COM7', 'COM8', 'COM9', 'LPT1', 'LPT2',
            'LPT3', 'LPT4', 'LPT5', 'LPT6', 'LPT7', 'LPT8', 'LPT9'
        }
        if filename.upper().split('.')[0] in reserved_names:
            security_flags.append("reserved_filename")
            errors.append("Reserved filename not allowed")
        
        # Ensure filename is not empty after sanitization
        if not filename.strip():
            errors.append("Filename cannot be empty")
            filename = "untitled"
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            sanitized_value=filename,
            original_value=original_filename,
            errors=errors,
            warnings=warnings,
            security_flags=security_flags,
            metadata={"extension": extension if '.' in filename else None}
        )
    
    def validate_json(self, json_data: Union[str, dict], max_depth: int = None) -> ValidationResult:
        """Validate JSON data with depth and size limits"""
        errors = []
        warnings = []
        security_flags = []
        original_data = json_data
        
        # Parse if string
        if isinstance(json_data, str):
            try:
                json_data = json.loads(json_data)
            except json.JSONDecodeError as e:
                return ValidationResult(
                    is_valid=False, sanitized_value=None, original_value=original_data,
                    errors=[f"Invalid JSON: {str(e)}"],
                    warnings=[], security_flags=[], metadata={}
                )
        
        # Validate depth
        max_depth = max_depth or self.config.INPUT_VALIDATION["max_json_depth"]
        actual_depth = self._calculate_json_depth(json_data)
        if actual_depth > max_depth:
            errors.append(f"JSON depth {actual_depth} exceeds maximum {max_depth}")
            security_flags.append("excessive_json_depth")
        
        # Validate array lengths
        array_issues = self._validate_json_arrays(json_data)
        if array_issues:
            errors.extend(array_issues)
            security_flags.append("excessive_array_size")
        
        # Check for sensitive data in JSON
        json_str = json.dumps(json_data) if isinstance(json_data, (dict, list)) else str(json_data)
        sensitive_matches = []
        for pattern in self.sensitive_patterns:
            matches = pattern.findall(json_str)
            if matches:
                sensitive_matches.extend(matches)
                security_flags.append("sensitive_data_in_json")
        
        if sensitive_matches:
            warnings.append("Potential sensitive data detected in JSON")
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            sanitized_value=json_data,
            original_value=original_data,
            errors=errors,
            warnings=warnings,
            security_flags=security_flags,
            metadata={
                "depth": actual_depth,
                "sensitive_matches": len(sensitive_matches)
            }
        )
    
    def _calculate_json_depth(self, obj, current_depth=0):
        """Calculate the maximum depth of a JSON object"""
        if isinstance(obj, dict):
            return max([self._calculate_json_depth(v, current_depth + 1) for v in obj.values()], default=current_depth + 1)
        elif isinstance(obj, list):
            return max([self._calculate_json_depth(item, current_depth + 1) for item in obj], default=current_depth + 1)
        else:
            return current_depth
    
    def _validate_json_arrays(self, obj, path=""):
        """Validate array lengths in JSON object"""
        errors = []
        max_array_length = self.config.INPUT_VALIDATION["max_array_length"]
        
        if isinstance(obj, list):
            if len(obj) > max_array_length:
                errors.append(f"Array at {path} exceeds maximum length {max_array_length}")
            for i, item in enumerate(obj):
                errors.extend(self._validate_json_arrays(item, f"{path}[{i}]"))
        elif isinstance(obj, dict):
            for key, value in obj.items():
                errors.extend(self._validate_json_arrays(value, f"{path}.{key}" if path else key))
        
        return errors

# Create global instance
input_sanitizer = AdvancedInputSanitizer()

# Utility functions for common validation tasks
def validate_integer(value: Any, min_val: int = None, max_val: int = None) -> ValidationResult:
    """Validate integer values with range checks"""
    errors = []
    original_value = value
    
    try:
        int_value = int(value)
    except (ValueError, TypeError):
        return ValidationResult(
            is_valid=False, sanitized_value=0, original_value=original_value,
            errors=[f"Cannot convert to integer: {value}"],
            warnings=[], security_flags=[], metadata={}
        )
    
    if min_val is not None and int_value < min_val:
        errors.append(f"Value {int_value} is less than minimum {min_val}")
    
    if max_val is not None and int_value > max_val:
        errors.append(f"Value {int_value} is greater than maximum {max_val}")
    
    return ValidationResult(
        is_valid=len(errors) == 0,
        sanitized_value=int_value,
        original_value=original_value,
        errors=errors,
        warnings=[], security_flags=[],
        metadata={"min_val": min_val, "max_val": max_val}
    )

def sanitize_text(text: str, max_length: int = None) -> ValidationResult:
    """Convenience function for text sanitization"""
    return input_sanitizer.sanitize_text(text, max_length)

def sanitize_query(query: str) -> ValidationResult:
    """Convenience function for query sanitization"""
    return input_sanitizer.sanitize_query(query)
