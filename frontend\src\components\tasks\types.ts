/**
 * Shared types for task components
 */

/**
 * Task priority levels
 */
export type TaskPriority = 'low' | 'medium' | 'high' | 'urgent'

/**
 * Task status
 */
export type TaskStatus = 'todo' | 'in_progress' | 'completed' | 'cancelled'

/**
 * Task interface matching the PRP data models
 */
export interface Task {
  id: string
  title: string
  description?: string
  completed: boolean
  priority: TaskPriority
  status: TaskStatus
  category?: string
  ai_generated_category?: string
  due_date?: string
  created_at: string
  updated_at?: string
}

/**
 * Task filter interface
 */
export interface TaskFilters {
  search?: string
  priority?: TaskPriority | 'all'
  status?: TaskStatus | 'all'
  category?: string | 'all'
  showCompleted?: boolean
  completed?: boolean
}
