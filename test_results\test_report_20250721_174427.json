{"test_run_id": "run_20250721_174420", "start_time": "2025-07-21T17:44:27.034902", "end_time": "2025-07-21T17:44:27.807753", "duration": 0.7725791931152344, "environment": {"python_version": "3.13.5 (tags/v3.13.5:6cb20a2, Jun 11 2025, 16:15:46) [MSC v.1943 64 bit (AMD64)]", "platform": "win32", "working_directory": "C:\\Users\\<USER>\\Documents\\Coding\\OneSearch", "test_runner_version": "1.0.0", "timestamp": "2025-07-21T17:44:20.768085", "config": {}, "backend_status": "unavailable", "frontend_status": "unavailable"}, "suites": [{"suite_name": "comprehensive", "description": "Complete user workflow and performance testing", "weight": 40, "start_time": "2025-07-21T17:44:27.035846", "success": false, "score": 0, "duration": 0.169142484664917, "error": "No module named 'playwright'", "results": null, "end_time": "2025-07-21T17:44:27.205170"}, {"suite_name": "security", "description": "Security vulnerability and protection testing", "weight": 30, "start_time": "2025-07-21T17:44:27.206501", "success": false, "score": 0, "duration": 0.5998623371124268, "error": "No module named 'app'", "results": null, "end_time": "2025-07-21T17:44:27.806360"}], "summary": {"total_suites": 2, "successful_suites": 0, "failed_suites": 2, "success_rate": 0.0, "overall_score": 0.0, "grade": "F"}, "overall_score": 0.0, "success": false}