"""
Specialized agent for task extraction and processing.

This agent handles inputs categorized as tasks, extracts relevant information,
and processes them using Mirascope patterns with task management tools.
Follows research/mirascope/page7-agents.md for agent state management.
"""

import asyncio
import logging
from typing import List, Dict, Any, Optional, AsyncGenerator
from datetime import datetime, timedelta
import re

from mirascope import llm
from pydantic import ValidationError

from app.models.ai_responses import AITaskExtractionResponse, AIProcessingError
from app.models.pydantic_models import (
    UserInput, TaskData, TaskPriority, TaskStatus, ProcessingState, AnimationStep
)
from app.config.settings import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()


class TaskAgent:
    """
    Specialized agent for processing task-related inputs.
    
    This agent extracts task details, determines categories and priorities,
    and integrates with task management tools.
    """
    
    def __init__(self):
        self.model = settings.primary_llm_model
        self.fallback_model = settings.ai_fallback_model
        self.max_retries = settings.ai_max_retries
        self.task_history: List[Dict[str, Any]] = []
        
        # Common date patterns for parsing
        self.date_patterns = [
            r'(?:by|due|deadline)\s+(.+?)(?:\s|$)',
            r'(?:tomorrow|today|yesterday)',
            r'(?:next|this)\s+(?:week|month|monday|tuesday|wednesday|thursday|friday|saturday|sunday)',
            r'\d{1,2}/\d{1,2}(?:/\d{2,4})?',
            r'\d{1,2}-\d{1,2}(?:-\d{2,4})?',
            r'(?:january|february|march|april|may|june|july|august|september|october|november|december)\s+\d{1,2}',
        ]
    
    async def process_task(
        self,
        user_input: UserInput,
        context: Optional[Dict[str, Any]] = None
    ) -> TaskData:
        """
        Process a user input identified as a task.
        
        Args:
            user_input: The user's task input
            context: Optional context from categorization
            
        Returns:
            TaskData: Extracted and processed task information
        """
        try:
            logger.info(f"Processing task: {user_input.text[:100]}...")
            
            # Extract task information with AI
            extraction_result = await self._extract_task_with_retry(user_input, context)
            
            # Create TaskData from extraction results
            task_data = TaskData(
                title=extraction_result.title,
                description=extraction_result.description,
                category=extraction_result.category,
                priority=extraction_result.priority,
                status=TaskStatus.PENDING,
                due_date=extraction_result.parsed_due_date,
                ai_generated_category=extraction_result.category,
                ai_confidence=extraction_result.date_confidence if extraction_result.has_due_date else 0.8,
                ai_reasoning=extraction_result.extraction_reasoning,
                ai_suggestions={
                    "subtasks": extraction_result.suggested_subtasks,
                    "estimated_time": extraction_result.estimated_time,
                    "category_reasoning": extraction_result.category_reasoning,
                    "priority_reasoning": extraction_result.priority_reasoning
                }
            )
            
            # Store in processing history
            self.task_history.append({
                "input": user_input.text,
                "task_title": task_data.title,
                "category": task_data.category,
                "priority": task_data.priority,
                "timestamp": datetime.now(),
                "confidence": task_data.ai_confidence
            })
            
            # Keep history manageable
            if len(self.task_history) > 20:
                self.task_history.pop(0)
            
            logger.info(f"Task processed: {task_data.title} [{task_data.category}]")
            return task_data
            
        except Exception as e:
            logger.error(f"Error processing task: {e}")
            
            # Return minimal task with error info
            return TaskData(
                title=self._extract_simple_title(user_input.text),
                description=f"Error during processing: {str(e)}",
                category="general",
                priority=TaskPriority.MEDIUM,
                ai_reasoning=f"Processing failed: {str(e)}",
                ai_confidence=0.1
            )
    
    async def _extract_task_with_retry(
        self,
        user_input: UserInput,
        context: Optional[Dict[str, Any]] = None
    ) -> AITaskExtractionResponse:
        """
        Extract task information with retry logic.
        
        Args:
            user_input: User input to process
            context: Optional context
            
        Returns:
            AITaskExtractionResponse: Structured task extraction result
        """
        last_error = None
        
        # Try primary model
        for attempt in range(self.max_retries):
            try:
                logger.debug(f"Task extraction attempt {attempt + 1}/{self.max_retries}")
                return await self._perform_task_extraction(user_input, context, self.model)
                
            except Exception as e:
                last_error = e
                logger.warning(f"Task extraction attempt {attempt + 1} failed: {e}")
                
                if attempt < self.max_retries - 1:
                    await asyncio.sleep(0.5 * (attempt + 1))
        
        # Try fallback model
        try:
            logger.info(f"Trying fallback model for task extraction")
            return await self._perform_task_extraction(user_input, context, self.fallback_model)
            
        except Exception as e:
            logger.error(f"Fallback task extraction failed: {e}")
            raise AIProcessingError(
                error_type="task_extraction_failed",
                error_message=f"Task extraction failed: {str(last_error)}",
                error_code="TASK_EXTRACTION_FAILED",
                operation="task_processing",
                input_data=user_input.text,
                recoverable=True,
                suggested_action="Please provide more specific task details",
                model_used=self.model
            )
    
    @llm.call(provider="openrouter", response_model=AITaskExtractionResponse, json_mode=True)
    async def _perform_task_extraction(
        self,
        user_input: UserInput,
        context: Optional[Dict[str, Any]] = None,
        model: str = None
    ) -> str:
        """
        Mirascope call for task extraction using structured response model.
        """
        
        # Build context from task history
        context_str = ""
        if self.task_history:
            recent_tasks = self.task_history[-5:]  # Last 5 tasks
            context_str = "\\n".join([
                f"Recent task: '{task['task_title']}' [{task['category']}] - {task['priority']}"
                for task in recent_tasks
            ])
            context_str = f"\\n\\nRecent task patterns:\\n{context_str}"
        
        # Add provided context
        if context:
            context_str += f"\\n\\nCategorization context: {context}"
        
        return f"""You are an expert task extraction agent. Analyze the user's input and extract all relevant task information with AI-powered reasoning.

INPUT TO ANALYZE: "{user_input.text}"

CONTEXT: {context_str}

EXTRACTION RULES:
1. **Title**: Create a clear, actionable task title (verb + object format preferred)
2. **Category**: Generate meaningful categories based on content (NOT from predefined list)
3. **Priority**: Estimate based on urgency indicators, deadlines, and importance cues
4. **Dates**: Extract and parse any date/time references carefully
5. **Reasoning**: Explain all decisions with specific evidence from the input

CATEGORY EXAMPLES (but create your own based on content):
- development, writing, research, planning, communication, maintenance, 
- personal, work, learning, creative, administrative, health, etc.

PRIORITY INDICATORS:
- URGENT: "ASAP", "immediately", "urgent", "critical", overdue mentions
- HIGH: "important", "soon", "priority", deadlines within days
- MEDIUM: "need to", "should", general deadlines
- LOW: "when possible", "eventually", "might", no urgency cues

DATE PARSING GUIDELINES:
- "tomorrow" = next day
- "next week" = following Monday
- "end of week" = Friday
- "by Friday" = that Friday by end of day
- Be conservative with confidence if dates are ambiguous

SUBTASK IDENTIFICATION:
- Look for compound actions or multi-step processes
- Break down complex tasks into logical components
- Suggest related or prerequisite tasks

Respond with comprehensive JSON containing all extracted information and detailed reasoning for each decision."""
    
    async def stream_task_processing(
        self,
        user_input: UserInput,
        context: Optional[Dict[str, Any]] = None
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Stream task processing with real-time visual feedback.
        
        Args:
            user_input: User input to process
            context: Optional context
            
        Yields:
            Dict[str, Any]: Processing updates for frontend animations
        """
        try:
            processing_steps = [
                AnimationStep(
                    id="parsing",
                    message="Parsing task details...",
                    animation_type="typewriter",
                    duration=1.2
                ),
                AnimationStep(
                    id="extracting",
                    message="Extracting task information...",
                    animation_type="pulse",
                    duration=1.8,
                    delay=1.2
                ),
                AnimationStep(
                    id="categorizing", 
                    message="Determining category and priority...",
                    animation_type="spin",
                    duration=1.5,
                    delay=3.0
                ),
                AnimationStep(
                    id="dating",
                    message="Processing dates and deadlines...",
                    animation_type="fade",
                    duration=1.0,
                    delay=4.5
                ),
                AnimationStep(
                    id="finalizing",
                    message="Finalizing task details...",
                    animation_type="bounce",
                    duration=0.8,
                    delay=5.5
                )
            ]
            
            # Stream processing steps
            for i, step in enumerate(processing_steps):
                yield {
                    "type": "task_processing_update",
                    "data": {
                        "step": step.dict(),
                        "progress": i / len(processing_steps),
                        "message": step.message
                    }
                }
                
                if i < len(processing_steps) - 1:
                    await asyncio.sleep(step.delay + step.duration)
            
            # Perform actual task processing
            result = await self.process_task(user_input, context)
            
            # Yield final result
            yield {
                "type": "task_processing_complete",
                "data": {
                    "task": result.dict(),
                    "suggestions": result.ai_suggestions,
                    "confidence": result.ai_confidence
                }
            }
            
        except Exception as e:
            logger.error(f"Error in stream_task_processing: {e}")
            yield {
                "type": "error", 
                "data": {
                    "error_message": str(e),
                    "error_type": "task_processing_error"
                }
            }
    
    def _extract_simple_title(self, text: str) -> str:
        """
        Extract a simple title from text as fallback.
        
        Args:
            text: Input text
            
        Returns:
            str: Simple extracted title
        """
        # Clean and truncate text for title
        title = re.sub(r'[^\\w\\s-]', '', text).strip()
        title = ' '.join(title.split()[:8])  # Max 8 words
        
        if not title:
            return "Task from input"
            
        # Capitalize first word
        return title[0].upper() + title[1:] if len(title) > 1 else title.upper()
    
    def _detect_date_patterns(self, text: str) -> List[str]:
        """
        Detect potential date patterns in text.
        
        Args:
            text: Input text
            
        Returns:
            List[str]: Found date patterns
        """
        found_dates = []
        text_lower = text.lower()
        
        for pattern in self.date_patterns:
            matches = re.findall(pattern, text_lower, re.IGNORECASE)
            found_dates.extend(matches)
        
        return found_dates
    
    async def suggest_task_improvements(
        self,
        task_data: TaskData
    ) -> Dict[str, Any]:
        """
        Suggest improvements for an existing task.
        
        Args:
            task_data: Existing task data
            
        Returns:
            Dict[str, Any]: Improvement suggestions
        """
        try:
            # Analyze task for potential improvements
            suggestions = {
                "clarity": [],
                "scheduling": [],
                "breakdown": [],
                "resources": []
            }
            
            # Title clarity
            if len(task_data.title.split()) < 3:
                suggestions["clarity"].append("Consider adding more detail to the task title")
            
            # Scheduling
            if not task_data.due_date:
                suggestions["scheduling"].append("Consider adding a deadline to improve focus")
            
            # Task breakdown
            if len(task_data.title) > 50 and not task_data.ai_suggestions.get("subtasks"):
                suggestions["breakdown"].append("This looks like a complex task - consider breaking it down")
            
            # Priority validation
            if task_data.priority == TaskPriority.LOW and task_data.due_date:
                time_until_due = task_data.due_date - datetime.now()
                if time_until_due.days <= 3:
                    suggestions["scheduling"].append("Task due soon - consider raising priority")
            
            return suggestions
            
        except Exception as e:
            logger.error(f"Error suggesting task improvements: {e}")
            return {"error": str(e)}
    
    def get_task_stats(self) -> Dict[str, Any]:
        """Get task processing statistics."""
        if not self.task_history:
            return {"total_processed": 0, "categories": {}, "priorities": {}}
        
        categories = {}
        priorities = {}
        
        for task in self.task_history:
            cat = task["category"]
            pri = task["priority"]
            categories[cat] = categories.get(cat, 0) + 1
            priorities[pri] = priorities.get(pri, 0) + 1
        
        return {
            "total_processed": len(self.task_history),
            "categories": categories,
            "priorities": priorities,
            "avg_confidence": sum(t["confidence"] for t in self.task_history) / len(self.task_history),
            "last_processed": self.task_history[-1]["timestamp"].isoformat()
        }


# Global task agent instance
_task_agent = None


def get_task_agent() -> TaskAgent:
    """Get the global task agent instance (singleton pattern)."""
    global _task_agent
    if _task_agent is None:
        _task_agent = TaskAgent()
    return _task_agent


# Export main components
__all__ = ["TaskAgent", "get_task_agent"]
