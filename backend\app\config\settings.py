"""
Backend Performance Optimization Configuration
PATTERN: Async/await throughout Mirascope calls, connection pooling, caching
Features:
- Connection pooling for database
- Caching for embeddings and frequent queries
- Background task processing
- Rate limiting implementation
- Memory optimization
- Secure environment variable management
- Following ai_orchestrator_flow.mermaid EXACTLY
"""

from pydantic_settings import BaseSettings
from pydantic import Field, field_validator
from typing import List, Optional
from functools import lru_cache
from pathlib import Path
import os
import os
import json

class Settings(BaseSettings):
    """Application settings with performance optimizations."""
    
    # Environment
    environment: str = Field(default="development", description="Environment: development, staging, production")
    debug: bool = Field(default=True, description="Enable debug mode")
    log_level: str = Field(default="INFO", description="Log level: DEBUG, INFO, WARNING, ERROR")
    
    # API Configuration
    api_host: str = Field(default="0.0.0.0", description="API host")
    api_port: int = Field(default=8000, description="API port")
    cors_origins: str = Field(
        default="http://localhost:3000", 
        description="CORS allowed origins (comma-separated or JSON array)"
    )
    
    # Database Configuration with Performance Settings
    database_url: str = Field(
        default="sqlite:///./data/dashboard.db", 
        description="Database connection string"
    )
    enable_wal_mode: bool = Field(default=True, description="Enable SQLite WAL mode for concurrent access")
    db_pool_size: int = Field(default=5, description="Database connection pool size")
    db_max_overflow: int = Field(default=10, description="Max database connection overflow")
    db_pool_timeout: int = Field(default=30, description="Database connection timeout seconds")
    db_pool_recycle: int = Field(default=3600, description="Database connection recycle time")
    
    # LLM Configuration (OpenRouter)
    openrouter_api_key: str = Field(..., description="OpenRouter API key")
    primary_llm_model: str = Field(
        default="tngtech/deepseek-r1t2-chimera:free", 
        description="Primary LLM model for categorization"
    )
    fallback_llm_model: str = Field(
        default="microsoft/phi-3.5-mini-instruct:free", 
        description="Fallback model if primary fails"
    )
    llm_temperature: float = Field(default=0.7, description="Temperature for AI responses")
    max_response_tokens: int = Field(default=2048, description="Maximum tokens for responses")
    llm_timeout: int = Field(default=30, description="LLM request timeout in seconds")
    llm_max_retries: int = Field(default=3, description="Maximum LLM request retries")
    
    # Web Search Configuration
    langsearch_api_key: str = Field(..., description="LangSearch API key")
    web_search_timeout: int = Field(default=20, description="Web search timeout in seconds")
    max_search_results: int = Field(default=10, description="Maximum search results to return")
    
    # Ollama Configuration
    ollama_base_url: str = Field(default="http://localhost:11434", description="Ollama service URL")
    ollama_embedding_model: str = Field(default="nomic-embed-text", description="Embedding model")
    ollama_keep_alive: str = Field(default="24h", description="Keep models loaded in memory")
    embedding_timeout: int = Field(default=15, description="Embedding generation timeout")
    
    # Performance Settings
    workers: Optional[int] = Field(default=None, description="Number of worker processes (auto-detect if None)")
    async_enabled: bool = Field(default=True, description="Enable async processing")
    
    # Caching Configuration
    cache_ttl: int = Field(default=3600, description="Cache TTL in seconds")
    enable_embedding_cache: bool = Field(default=True, description="Cache embedding results")
    enable_llm_cache: bool = Field(default=True, description="Cache LLM responses")
    cache_size: int = Field(default=1000, description="Maximum cache entries")
    
    # Rate Limiting
    api_rate_limit_per_minute: int = Field(default=60, description="API requests per minute per IP")
    embedding_rate_limit_per_minute: int = Field(default=30, description="Embedding requests per minute")
    web_search_rate_limit_per_minute: int = Field(default=20, description="Web search requests per minute")
    
    # Background Task Processing
    enable_background_tasks: bool = Field(default=True, description="Enable background task processing")
    task_queue_size: int = Field(default=100, description="Background task queue size")
    task_worker_count: int = Field(default=2, description="Number of background task workers")
    
    # Memory Management
    max_memory_usage_mb: int = Field(default=1024, description="Maximum memory usage in MB")
    gc_threshold: int = Field(default=100, description="Garbage collection threshold")
    enable_memory_monitoring: bool = Field(default=True, description="Enable memory monitoring")
    
    # Security
    secret_key: str = Field(default="dev_secret_key_change_in_production", description="Secret key")
    force_https: bool = Field(default=False, description="Force HTTPS redirect")
    max_payload_size: int = Field(default=10, description="Max request payload size in MB")
    
    # Monitoring
    enable_monitoring: bool = Field(default=True, description="Enable performance monitoring")
    enable_analytics: bool = Field(default=False, description="Enable analytics tracking")
    error_reporting_url: Optional[str] = Field(default=None, description="Error reporting service URL")
    
    # WebSocket Configuration
    websocket_heartbeat_interval: int = Field(default=30, description="WebSocket heartbeat interval")
    websocket_max_connections: int = Field(default=100, description="Maximum WebSocket connections")
    websocket_message_queue_size: int = Field(default=1000, description="WebSocket message queue size")
    
    # Legacy fields for compatibility
    OLLAMA_MODEL: str = Field(
        default="nomic-embed-text",
        description="Ollama embedding model"
    )
    
    # AI Configuration
    ai_max_retries: int = Field(default=3, description="Maximum AI retry attempts")
    ai_fallback_model: str = Field(
        default="google/gemini-2.0-flash-exp:free",
        description="Fallback AI model"
    )
    
    # Performance Configuration
    max_workers: int = Field(default=4, description="Maximum worker threads")
    connection_pool_size: int = Field(default=20, description="Database connection pool size")
    request_timeout: int = Field(default=30, description="Request timeout in seconds")
    
    # Animation Configuration
    animation_duration_ms: int = Field(default=300, description="Animation duration in milliseconds")
    spring_tension: int = Field(default=400, description="Spring animation tension")
    spring_friction: int = Field(default=25, description="Spring animation friction")
    
    # Security Configuration
    bcrypt_salt_rounds: int = Field(default=12, description="BCrypt salt rounds")
    session_cookie_max_age: int = Field(default=604800000, description="Session cookie max age")
    
    @field_validator('cors_origins', mode='before')
    @classmethod
    def parse_cors_origins(cls, v):
        return v  # Keep as string, we'll parse it in a property
    
    @property
    def cors_origins_list(self) -> List[str]:
        """Parse CORS origins as a list of strings."""
        if isinstance(self.cors_origins, str):
            # Try JSON parsing first
            import json
            try:
                parsed = json.loads(self.cors_origins)
                if isinstance(parsed, list):
                    return parsed
                else:
                    return [parsed]
            except:
                # Fall back to comma-separated parsing
                return [origin.strip() for origin in self.cors_origins.split(',')]
        return self.cors_origins
    
    @field_validator('database_url')
    @classmethod
    def validate_database_url(cls, v):
        """Ensure database directory exists."""
        if v.startswith('sqlite:'):
            db_path = v.replace('sqlite:///', '')
            Path(db_path).parent.mkdir(parents=True, exist_ok=True)
        return v

    class Config:
        env_file = "../.env"  # Look for .env in parent directory (project root)
        case_sensitive = False


# Global settings instance
_settings = None


def get_settings() -> Settings:
    """Get the global settings instance (singleton pattern)."""
    global _settings
    if _settings is None:
        _settings = Settings()
    return _settings


# Performance constants optimized for AI orchestrator flow
PERFORMANCE_CONSTANTS = {
    # Animation timing to match mermaid diagram EXACTLY
    "ANIMATION_INTERVALS": {
        "typing_indicator": 0.1,      # 🎭 'Analyzing input...'
        "brain_animation": 0.2,       # 🤖 'Categorizing...'  
        "category_pulse": 0.3,        # Task/Event/Question identified pulses
        "processing_spinner": 0.15,   # ⚙️ Processing animations
        "success_confetti": 0.5,      # ✅ Success celebrations
        "error_shake": 0.2,           # ❌ Error animations
    },
    
    # Database performance
    "DB_BATCH_SIZE": 100,
    "DB_CONNECTION_TIMEOUT": 30,
    "DB_QUERY_TIMEOUT": 10,
    
    # LLM performance  
    "LLM_STREAM_CHUNK_SIZE": 1024,
    "LLM_CONCURRENT_REQUESTS": 5,
    "LLM_RETRY_BACKOFF": [1, 2, 4],  # Exponential backoff
    
    # Embedding performance
    "EMBEDDING_BATCH_SIZE": 50,
    "VECTOR_SIMILARITY_THRESHOLD": 0.7,
    "EMBEDDING_CACHE_SIZE": 10000,
    
    # Memory management
    "MAX_CONCURRENT_TASKS": 10,
    "MEMORY_CHECK_INTERVAL": 60,
    "GC_COLLECTION_INTERVAL": 300,
}

# Visual feedback states matching mermaid diagram exactly
VISUAL_FEEDBACK_STATES = {
    # Initial processing
    "analyzing": {
        "message": "🎭 'Analyzing input...'",
        "animation": "TYPING INDICATOR",
        "duration": 1.0
    },
    "categorizing": {
        "message": "🤖 'Categorizing...'", 
        "animation": "BRAIN ANIMATION",
        "duration": 1.5
    },
    
    # Category identification with color-coded pulses
    "task_identified": {
        "message": "📝 'Task identified!'",
        "animation": "GREEN PULSE", 
        "duration": 0.8
    },
    "event_identified": {
        "message": "📋 'Event identified!'",
        "animation": "BLUE PULSE",
        "duration": 0.8  
    },
    "question_identified": {
        "message": "❓ 'Question identified!'",
        "animation": "PURPLE PULSE",
        "duration": 0.8
    },
    
    # Task processing flow
    "extracting_details": {
        "message": "⚙️ 'Extracting details...'",
        "animation": "SPINNER ANIMATION",
        "duration": 1.2
    },
    "auto_categorizing": {
        "message": "🏷️ 'Auto-categorizing...'",
        "animation": "TAG ANIMATIONS", 
        "duration": 1.0
    },
    "checking_dates": {
        "message": "📅 'Checking for dates...'",
        "animation": "CALENDAR SCAN",
        "duration": 1.5
    },
    "adding_to_calendar": {
        "message": "🗓️ 'Scheduling...'",
        "animation": "SLIDE TO DATE",
        "duration": 1.0
    },
    "updating_task_list": {
        "message": "📋 'Updating task list...'", 
        "animation": "LIST GROW ANIMATION",
        "duration": 1.2
    },
    
    # Search processing flow
    "searching_database": {
        "message": "🔍 'Searching database...'",
        "animation": "RADAR SCAN", 
        "duration": 2.0
    },
    "generating_embeddings": {
        "message": "🧮 'Generating embeddings...'",
        "animation": "MATRIX ANIMATION",
        "duration": 1.5
    },
    "vector_searching": {
        "message": "📊 'Vector searching...'",
        "animation": "PROGRESS DOTS",
        "duration": 2.5
    },
    "web_searching": {
        "message": "🌐 'Starting web search...'",
        "animation": "GLOBE SPIN",
        "duration": 1.0
    },
    "calling_langsearch": {
        "message": "🔗 'Calling LangSearch API...'",
        "animation": "API PULSE", 
        "duration": 3.0
    },
    
    # Success states
    "task_added": {
        "message": "✅ 'Task added successfully!'",
        "animation": "SUCCESS CONFETTI",
        "duration": 2.0
    },
    "event_scheduled": {
        "message": "✅ 'Event scheduled!'", 
        "animation": "SUCCESS SPARKLE",
        "duration": 2.0
    },
    "answer_ready": {
        "message": "✅ 'Answer ready!'",
        "animation": "REVEAL ANIMATION", 
        "duration": 1.5
    }
}


# Export commonly used settings
__all__ = ["Settings", "get_settings", "PERFORMANCE_CONSTANTS", "VISUAL_FEEDBACK_STATES"]
