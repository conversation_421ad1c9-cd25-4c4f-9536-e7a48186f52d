import axios, { AxiosInstance, AxiosResponse, AxiosError } from 'axios'

// Response types for AI operations
export interface CategoryResponse {
  category: 'task' | 'event' | 'ai_question'
  confidence: number
  reasoning: string
  processing_steps: string[]
}

export interface TaskResponse {
  id: string
  title: string
  description?: string
  category?: string
  priority: 'low' | 'medium' | 'high' | 'urgent'
  due_date?: string
  ai_generated_category?: string
}

export interface EventResponse {
  id: string
  title: string
  description?: string
  start_time: string
  end_time?: string
  location?: string
  ai_generated?: boolean
}

export interface AIAnswerResponse {
  type: 'text' | 'search' | 'knowledge'
  content: string
  sources?: string[]
  searchResults?: Array<{
    title: string
    snippet: string
    url: string
    relevance: number
  }>
  confidence?: number
}

export interface ProcessInputRequest {
  text: string
  context?: Record<string, any>
}

export interface ProcessInputResponse {
  category: CategoryResponse
  result?: TaskResponse | EventResponse | AIAnswerResponse
  processing_time_ms: number
}

/**
 * AI Service
 * PATTERN: Axios-based API client with interceptors
 * Features:
 * - WebSocket service integration
 * - Request/response transformation
 * - Error handling and retry logic
 * - Token management for authenticated requests
 * - Real-time processing updates
 */
class AIService {
  private api: AxiosInstance
  private wsConnection: WebSocket | null = null
  private wsListeners: Map<string, Function[]> = new Map()

  constructor() {
    this.api = axios.create({
      baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api',
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    })

    this.setupInterceptors()
    this.initializeWebSocket()
  }

  private setupInterceptors() {
    // Request interceptor
    this.api.interceptors.request.use(
      (config: any) => {
        // Add auth token if available
        const token = localStorage.getItem('auth_token')
        if (token) {
          config.headers.Authorization = `Bearer ${token}`
        }

        // Add request timestamp for debugging
        config.metadata = { startTime: new Date() }
        
        console.log(`[AI Service] ${config.method?.toUpperCase()} ${config.url}`, config.data)
        return config
      },
      (error: any) => {
        console.error('[AI Service] Request error:', error)
        return Promise.reject(error)
      }
    )

    // Response interceptor
    this.api.interceptors.response.use(
      (response: AxiosResponse) => {
        const endTime = new Date()
        const startTime = (response.config as any).metadata?.startTime
        const duration = startTime ? endTime.getTime() - startTime.getTime() : 0
        
        console.log(`[AI Service] Response in ${duration}ms:`, response.data)
        return response
      },
      async (error: AxiosError) => {
        console.error('[AI Service] Response error:', error)
        
        // Retry logic for network errors
        if (error.code === 'NETWORK_ERROR' && error.config && !(error.config as any)._retry) {
          (error.config as any)._retry = true
          console.log('[AI Service] Retrying request...')
          return this.api.request(error.config)
        }

        // Handle specific error cases
        if (error.response?.status === 401) {
          // Clear invalid token
          localStorage.removeItem('auth_token')
          // Optionally redirect to login
        }

        return Promise.reject(error)
      }
    )
  }

  private initializeWebSocket() {
    const wsUrl = import.meta.env.VITE_WS_URL || 'ws://localhost:8000/ws'
    
    try {
      this.wsConnection = new WebSocket(wsUrl)
      
      this.wsConnection.onopen = () => {
        console.log('[AI Service] WebSocket connected')
        this.emit('connection', { status: 'connected' })
      }
      
      this.wsConnection.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data)
          console.log('[AI Service] WebSocket message:', data)
          
          // Emit to specific listeners
          if (data.type) {
            this.emit(data.type, data)
          }
          
          // Emit to general listeners
          this.emit('message', data)
        } catch (error) {
          console.error('[AI Service] Failed to parse WebSocket message:', error)
        }
      }
      
      this.wsConnection.onclose = (event) => {
        console.log('[AI Service] WebSocket disconnected:', event.code, event.reason)
        this.emit('connection', { status: 'disconnected', code: event.code })
        
        // Attempt reconnection after delay
        setTimeout(() => {
          console.log('[AI Service] Attempting WebSocket reconnection...')
          this.initializeWebSocket()
        }, 5000)
      }
      
      this.wsConnection.onerror = (error) => {
        console.error('[AI Service] WebSocket error:', error)
        this.emit('connection', { status: 'error', error })
      }
    } catch (error) {
      console.error('[AI Service] Failed to initialize WebSocket:', error)
    }
  }

  // WebSocket event handling
  on(event: string, callback: Function) {
    if (!this.wsListeners.has(event)) {
      this.wsListeners.set(event, [])
    }
    this.wsListeners.get(event)!.push(callback)
  }

  off(event: string, callback: Function) {
    const listeners = this.wsListeners.get(event)
    if (listeners) {
      const index = listeners.indexOf(callback)
      if (index > -1) {
        listeners.splice(index, 1)
      }
    }
  }

  private emit(event: string, data: any) {
    const listeners = this.wsListeners.get(event) || []
    listeners.forEach(callback => {
      try {
        callback(data)
      } catch (error) {
        console.error(`[AI Service] Error in ${event} listener:`, error)
      }
    })
  }

  // API Methods

  /**
   * Process user input through AI orchestrator
   */
  async processInput(request: ProcessInputRequest): Promise<ProcessInputResponse> {
    try {
      const response = await this.api.post<ProcessInputResponse>('/process-input', request)
      return response.data
    } catch (error) {
      console.error('[AI Service] Process input error:', error)
      throw this.handleError(error)
    }
  }

  /**
   * Get all tasks
   */
  async getTasks(): Promise<TaskResponse[]> {
    try {
      const response = await this.api.get<TaskResponse[]>('/tasks')
      return response.data
    } catch (error) {
      console.error('[AI Service] Get tasks error:', error)
      throw this.handleError(error)
    }
  }

  /**
   * Create a new task
   */
  async createTask(task: Omit<TaskResponse, 'id'>): Promise<TaskResponse> {
    try {
      const response = await this.api.post<TaskResponse>('/tasks', task)
      return response.data
    } catch (error) {
      console.error('[AI Service] Create task error:', error)
      throw this.handleError(error)
    }
  }

  /**
   * Update a task
   */
  async updateTask(id: string, updates: Partial<TaskResponse>): Promise<TaskResponse> {
    try {
      const response = await this.api.put<TaskResponse>(`/tasks/${id}`, updates)
      return response.data
    } catch (error) {
      console.error('[AI Service] Update task error:', error)
      throw this.handleError(error)
    }
  }

  /**
   * Delete a task
   */
  async deleteTask(id: string): Promise<void> {
    try {
      await this.api.delete(`/tasks/${id}`)
    } catch (error) {
      console.error('[AI Service] Delete task error:', error)
      throw this.handleError(error)
    }
  }

  /**
   * Get all events
   */
  async getEvents(): Promise<EventResponse[]> {
    try {
      const response = await this.api.get<EventResponse[]>('/events')
      return response.data
    } catch (error) {
      console.error('[AI Service] Get events error:', error)
      throw this.handleError(error)
    }
  }

  /**
   * Create a new event
   */
  async createEvent(event: Omit<EventResponse, 'id'>): Promise<EventResponse> {
    try {
      const response = await this.api.post<EventResponse>('/events', event)
      return response.data
    } catch (error) {
      console.error('[AI Service] Create event error:', error)
      throw this.handleError(error)
    }
  }

  /**
   * Update an event
   */
  async updateEvent(id: string, updates: Partial<EventResponse>): Promise<EventResponse> {
    try {
      const response = await this.api.put<EventResponse>(`/events/${id}`, updates)
      return response.data
    } catch (error) {
      console.error('[AI Service] Update event error:', error)
      throw this.handleError(error)
    }
  }

  /**
   * Delete an event
   */
  async deleteEvent(id: string): Promise<void> {
    try {
      await this.api.delete(`/events/${id}`)
    } catch (error) {
      console.error('[AI Service] Delete event error:', error)
      throw this.handleError(error)
    }
  }

  /**
   * Perform semantic search
   */
  async searchDocuments(query: string): Promise<AIAnswerResponse> {
    try {
      const response = await this.api.post<AIAnswerResponse>('/search/documents', { query })
      return response.data
    } catch (error) {
      console.error('[AI Service] Search documents error:', error)
      throw this.handleError(error)
    }
  }

  /**
   * Perform web search
   */
  async searchWeb(query: string): Promise<AIAnswerResponse> {
    try {
      const response = await this.api.post<AIAnswerResponse>('/search/web', { query })
      return response.data
    } catch (error) {
      console.error('[AI Service] Web search error:', error)
      throw this.handleError(error)
    }
  }

  /**
   * Get system health status
   */
  async getHealth(): Promise<{ status: string; services: Record<string, boolean> }> {
    try {
      const response = await this.api.get('/health')
      return response.data
    } catch (error) {
      console.error('[AI Service] Health check error:', error)
      throw this.handleError(error)
    }
  }

  /**
   * Test API connection with current settings
   */
  async testConnection(): Promise<{ success: boolean; latency: number }> {
    try {
      const startTime = Date.now()
      await this.api.get('/health')
      const latency = Date.now() - startTime
      
      return { success: true, latency }
    } catch (error) {
      console.error('[AI Service] Connection test error:', error)
      return { success: false, latency: -1 }
    }
  }

  /**
   * Send message through WebSocket
   */
  sendMessage(message: any) {
    if (this.wsConnection && this.wsConnection.readyState === WebSocket.OPEN) {
      this.wsConnection.send(JSON.stringify(message))
    } else {
      console.warn('[AI Service] WebSocket not connected, cannot send message')
    }
  }

  /**
   * Close WebSocket connection
   */
  disconnect() {
    if (this.wsConnection) {
      this.wsConnection.close()
      this.wsConnection = null
    }
    this.wsListeners.clear()
  }

  private handleError(error: any): Error {
    if (axios.isAxiosError(error)) {
      const message = error.response?.data?.detail || error.response?.data?.message || error.message
      return new Error(`API Error: ${message}`)
    }
    return error instanceof Error ? error : new Error('Unknown error occurred')
  }
}

// Export singleton instance
export const aiService = new AIService()
export default aiService
