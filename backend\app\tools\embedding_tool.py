"""
Task 12: Ollama Embeddings Tool

Tool for generating and managing embeddings using Ollama models with comprehensive 
model management, batch processing, vector storage, and performance optimization.

Features:
- Model management (download, load, unload)
- Batch embedding processing
- Vector storage and retrieval
- Performance optimization
- Context-aware embedding generation
- Similarity search capabilities
- Caching and memory optimization
"""

import asyncio
import logging
import time
import hashlib
import json
from typing import List, Dict, Any, Optional, Union, Tuple
from dataclasses import dataclass
from enum import Enum

import ollama
import numpy as np
from pydantic import BaseModel, Field, validator
import aiohttp

from ..config.settings import get_settings

# Configure logging
logger = logging.getLogger(__name__)

class EmbeddingModel(str, Enum):
    """Available embedding models in Ollama."""
    NOMIC_EMBED_TEXT = "nomic-embed-text"
    MXBAI_EMBED_LARGE = "mxbai-embed-large"
    BGE_M3 = "bge-m3"
    BGE_LARGE = "bge-large"
    ALL_MINILM = "all-minilm"
    SNOWFLAKE_ARCTIC_EMBED = "snowflake-arctic-embed"
    PARAPHRASE_MULTILINGUAL = "paraphrase-multilingual"
    GRANITE_EMBEDDING = "granite-embedding"

class ProcessingMode(str, Enum):
    """Processing modes for embeddings."""
    SINGLE = "single"
    BATCH = "batch"
    STREAMING = "streaming"

class VectorSearchMode(str, Enum):
    """Vector search modes."""
    COSINE = "cosine"
    EUCLIDEAN = "euclidean"
    DOT_PRODUCT = "dot_product"

@dataclass
class EmbeddingResult:
    """Result container for embedding operations."""
    text: str
    embedding: List[float]
    model: str
    processing_time: float
    metadata: Dict[str, Any]

@dataclass
class ModelInfo:
    """Information about an embedding model."""
    name: str
    size: str
    downloads: str
    tags: List[str]
    is_loaded: bool
    embedding_dimension: int
    max_tokens: int

class EmbeddingInput(BaseModel):
    """Input model for embedding requests."""
    text: Union[str, List[str]] = Field(..., description="Text or list of texts to embed")
    model: EmbeddingModel = Field(
        default=EmbeddingModel.NOMIC_EMBED_TEXT,
        description="Embedding model to use"
    )
    mode: ProcessingMode = Field(
        default=ProcessingMode.SINGLE,
        description="Processing mode"
    )
    include_metadata: bool = Field(
        default=True,
        description="Include metadata in response"
    )
    normalize: bool = Field(
        default=True,
        description="Normalize embeddings to unit length"
    )
    cache_key: Optional[str] = Field(
        default=None,
        description="Optional cache key for results"
    )
    
    @validator('text')
    def validate_text(cls, v):
        if isinstance(v, str):
            if len(v.strip()) == 0:
                raise ValueError("Text cannot be empty")
            if len(v) > 8000:  # Reasonable limit for embedding
                raise ValueError("Text too long (max 8000 characters)")
        elif isinstance(v, list):
            if len(v) == 0:
                raise ValueError("Text list cannot be empty")
            if len(v) > 100:  # Batch limit
                raise ValueError("Too many texts in batch (max 100)")
            for text in v:
                if not isinstance(text, str) or len(text.strip()) == 0:
                    raise ValueError("All texts must be non-empty strings")
        return v

class SimilaritySearchInput(BaseModel):
    """Input model for similarity search."""
    query_text: str = Field(..., description="Query text for similarity search")
    texts: List[str] = Field(..., description="Texts to search against")
    model: EmbeddingModel = Field(
        default=EmbeddingModel.NOMIC_EMBED_TEXT,
        description="Embedding model to use"
    )
    mode: VectorSearchMode = Field(
        default=VectorSearchMode.COSINE,
        description="Vector similarity calculation mode"
    )
    top_k: int = Field(
        default=5,
        ge=1,
        le=50,
        description="Number of top results to return"
    )
    threshold: float = Field(
        default=0.0,
        ge=0.0,
        le=1.0,
        description="Minimum similarity threshold"
    )

class ModelManagementInput(BaseModel):
    """Input model for model management operations."""
    model: EmbeddingModel = Field(..., description="Model to manage")
    operation: str = Field(..., description="Operation: pull, delete, show, list")
    force: bool = Field(default=False, description="Force operation")

class EmbeddingTool:
    """
    Advanced Ollama Embeddings Tool with model management, batch processing,
    and performance optimization capabilities.
    """
    
    def __init__(self):
        """Initialize the embedding tool."""
        self.settings = get_settings()
        self.name = "embedding_tool"
        self.description = "Tool for generating embeddings using Ollama models with advanced features"
        
        # Initialize Ollama client
        self.client = ollama.Client(
            host=self.settings.ollama_base_url or "http://localhost:11434"
        )
        self.async_client = ollama.AsyncClient(
            host=self.settings.ollama_base_url or "http://localhost:11434"
        )
        
        # Performance settings
        self.batch_size = 32
        self.max_concurrent = 4
        self.cache_enabled = True
        self.embedding_cache: Dict[str, EmbeddingResult] = {}
        self.model_cache: Dict[str, ModelInfo] = {}
        
        # Rate limiting
        self.requests_per_minute = 60
        self.request_timestamps: List[float] = []
        
        logger.info(f"EmbeddingTool initialized with Ollama URL: {self.settings.ollama_base_url}")

    def _generate_cache_key(self, text: str, model: str) -> str:
        """Generate a cache key for embedding results."""
        content = f"{text}:{model}"
        return hashlib.md5(content.encode()).hexdigest()

    def _check_rate_limit(self) -> bool:
        """Check if request is within rate limits."""
        now = time.time()
        # Remove timestamps older than 1 minute
        self.request_timestamps = [ts for ts in self.request_timestamps if now - ts < 60]
        
        if len(self.request_timestamps) >= self.requests_per_minute:
            return False
        
        self.request_timestamps.append(now)
        return True

    def _normalize_embedding(self, embedding: List[float]) -> List[float]:
        """Normalize embedding to unit length."""
        embedding_array = np.array(embedding)
        norm = np.linalg.norm(embedding_array)
        if norm == 0:
            return embedding
        return (embedding_array / norm).tolist()

    def _calculate_similarity(
        self, 
        embedding1: List[float], 
        embedding2: List[float], 
        mode: VectorSearchMode
    ) -> float:
        """Calculate similarity between two embeddings."""
        vec1 = np.array(embedding1)
        vec2 = np.array(embedding2)
        
        if mode == VectorSearchMode.COSINE:
            # Cosine similarity
            norm1 = np.linalg.norm(vec1)
            norm2 = np.linalg.norm(vec2)
            if norm1 == 0 or norm2 == 0:
                return 0.0
            return float(np.dot(vec1, vec2) / (norm1 * norm2))
        
        elif mode == VectorSearchMode.EUCLIDEAN:
            # Euclidean distance (converted to similarity)
            distance = np.linalg.norm(vec1 - vec2)
            return float(1.0 / (1.0 + distance))
        
        elif mode == VectorSearchMode.DOT_PRODUCT:
            # Dot product
            return float(np.dot(vec1, vec2))
        
        return 0.0

    async def is_model_available(self, model: str) -> bool:
        """Check if a model is available locally."""
        try:
            models = await self._list_models()
            return any(m['name'] == model for m in models.get('models', []))
        except Exception as e:
            logger.error(f"Error checking model availability: {e}")
            return False

    async def _ensure_model_available(self, model: str) -> bool:
        """Ensure model is available, download if necessary."""
        if await self.is_model_available(model):
            return True
        
        logger.info(f"Model {model} not found locally, pulling...")
        try:
            await self._pull_model(model)
            return True
        except Exception as e:
            logger.error(f"Failed to pull model {model}: {e}")
            return False

    async def _pull_model(self, model: str) -> Dict[str, Any]:
        """Pull a model from Ollama registry."""
        try:
            # Use synchronous client for pulling (async pull not available in current version)
            self.client.pull(model)
            logger.info(f"Successfully pulled model: {model}")
            return {"success": True, "model": model, "message": "Model pulled successfully"}
        except Exception as e:
            logger.error(f"Failed to pull model {model}: {e}")
            return {"success": False, "error": str(e)}

    async def _list_models(self) -> Dict[str, Any]:
        """List available models."""
        try:
            # Use synchronous client for listing
            models = self.client.list()
            return models
        except Exception as e:
            logger.error(f"Failed to list models: {e}")
            return {"models": []}

    async def _show_model(self, model: str) -> Dict[str, Any]:
        """Show model information."""
        try:
            info = self.client.show(model)
            return info
        except Exception as e:
            logger.error(f"Failed to show model {model}: {e}")
            return {"error": str(e)}

    async def _delete_model(self, model: str) -> Dict[str, Any]:
        """Delete a model."""
        try:
            self.client.delete(model)
            logger.info(f"Successfully deleted model: {model}")
            return {"success": True, "model": model, "message": "Model deleted successfully"}
        except Exception as e:
            logger.error(f"Failed to delete model {model}: {e}")
            return {"success": False, "error": str(e)}

    async def generate_single_embedding(
        self, 
        text: str, 
        model: str = "nomic-embed-text",
        normalize: bool = True,
        include_metadata: bool = True
    ) -> EmbeddingResult:
        """Generate embedding for a single text."""
        start_time = time.time()
        
        # Check cache
        if self.cache_enabled:
            cache_key = self._generate_cache_key(text, model)
            if cache_key in self.embedding_cache:
                logger.debug(f"Cache hit for embedding: {cache_key[:8]}")
                return self.embedding_cache[cache_key]
        
        # Check rate limits
        if not self._check_rate_limit():
            raise Exception("Rate limit exceeded. Please try again later.")
        
        # Ensure model is available
        if not await self._ensure_model_available(model):
            raise Exception(f"Model {model} is not available and could not be pulled")
        
        try:
            # Generate embedding
            response = self.client.embed(model=model, input=text)
            embedding = response.get('embeddings', [[]])[0]
            
            if normalize:
                embedding = self._normalize_embedding(embedding)
            
            processing_time = time.time() - start_time
            
            # Prepare metadata
            metadata = {}
            if include_metadata:
                metadata = {
                    "model": model,
                    "text_length": len(text),
                    "embedding_dimension": len(embedding),
                    "processing_time": processing_time,
                    "normalized": normalize,
                    "timestamp": time.time()
                }
            
            result = EmbeddingResult(
                text=text,
                embedding=embedding,
                model=model,
                processing_time=processing_time,
                metadata=metadata
            )
            
            # Cache result
            if self.cache_enabled:
                self.embedding_cache[cache_key] = result
            
            logger.debug(f"Generated embedding for text (length: {len(text)}) in {processing_time:.3f}s")
            return result
            
        except Exception as e:
            logger.error(f"Failed to generate embedding: {e}")
            raise Exception(f"Embedding generation failed: {str(e)}")

    async def generate_batch_embeddings(
        self,
        texts: List[str],
        model: str = "nomic-embed-text",
        normalize: bool = True,
        include_metadata: bool = True
    ) -> List[EmbeddingResult]:
        """Generate embeddings for multiple texts in batch."""
        start_time = time.time()
        
        if not texts:
            return []
        
        # Ensure model is available
        if not await self._ensure_model_available(model):
            raise Exception(f"Model {model} is not available and could not be pulled")
        
        try:
            # Check for cached results
            results = []
            texts_to_process = []
            
            if self.cache_enabled:
                for text in texts:
                    cache_key = self._generate_cache_key(text, model)
                    if cache_key in self.embedding_cache:
                        results.append(self.embedding_cache[cache_key])
                    else:
                        texts_to_process.append(text)
            else:
                texts_to_process = texts
            
            # Process uncached texts
            if texts_to_process:
                # Check rate limits
                if not self._check_rate_limit():
                    raise Exception("Rate limit exceeded. Please try again later.")
                
                # Generate embeddings in batch
                response = self.client.embed(model=model, input=texts_to_process)
                embeddings = response.get('embeddings', [])
                
                for i, (text, embedding) in enumerate(zip(texts_to_process, embeddings)):
                    if normalize:
                        embedding = self._normalize_embedding(embedding)
                    
                    processing_time = (time.time() - start_time) / len(texts_to_process)
                    
                    # Prepare metadata
                    metadata = {}
                    if include_metadata:
                        metadata = {
                            "model": model,
                            "text_length": len(text),
                            "embedding_dimension": len(embedding),
                            "processing_time": processing_time,
                            "normalized": normalize,
                            "timestamp": time.time(),
                            "batch_index": i
                        }
                    
                    result = EmbeddingResult(
                        text=text,
                        embedding=embedding,
                        model=model,
                        processing_time=processing_time,
                        metadata=metadata
                    )
                    
                    results.append(result)
                    
                    # Cache result
                    if self.cache_enabled:
                        cache_key = self._generate_cache_key(text, model)
                        self.embedding_cache[cache_key] = result
            
            total_time = time.time() - start_time
            logger.info(f"Generated {len(results)} embeddings in {total_time:.3f}s")
            return results
            
        except Exception as e:
            logger.error(f"Failed to generate batch embeddings: {e}")
            raise Exception(f"Batch embedding generation failed: {str(e)}")

    async def similarity_search(
        self,
        query_text: str,
        texts: List[str],
        model: str = "nomic-embed-text",
        mode: VectorSearchMode = VectorSearchMode.COSINE,
        top_k: int = 5,
        threshold: float = 0.0
    ) -> List[Dict[str, Any]]:
        """Perform similarity search against a list of texts."""
        try:
            # Generate embedding for query
            query_result = await self.generate_single_embedding(query_text, model)
            query_embedding = query_result.embedding
            
            # Generate embeddings for all texts
            text_results = await self.generate_batch_embeddings(texts, model)
            
            # Calculate similarities
            similarities = []
            for i, text_result in enumerate(text_results):
                similarity = self._calculate_similarity(
                    query_embedding,
                    text_result.embedding,
                    mode
                )
                
                if similarity >= threshold:
                    similarities.append({
                        "text": text_result.text,
                        "similarity": similarity,
                        "index": i,
                        "metadata": text_result.metadata
                    })
            
            # Sort by similarity (descending)
            similarities.sort(key=lambda x: x["similarity"], reverse=True)
            
            # Return top k results
            results = similarities[:top_k]
            
            logger.info(f"Similarity search returned {len(results)} results (threshold: {threshold})")
            return results
            
        except Exception as e:
            logger.error(f"Similarity search failed: {e}")
            raise Exception(f"Similarity search failed: {str(e)}")

    async def manage_model(
        self,
        model: str,
        operation: str,
        force: bool = False
    ) -> Dict[str, Any]:
        """Manage Ollama models (pull, delete, show, list)."""
        try:
            if operation == "pull":
                return await self._pull_model(model)
            
            elif operation == "delete":
                if not force:
                    # Check if model is being used
                    if model in self.embedding_cache:
                        return {
                            "success": False,
                            "error": "Model is cached. Use force=True to delete anyway."
                        }
                return await self._delete_model(model)
            
            elif operation == "show":
                return await self._show_model(model)
            
            elif operation == "list":
                return await self._list_models()
            
            else:
                return {"success": False, "error": f"Unknown operation: {operation}"}
                
        except Exception as e:
            logger.error(f"Model management operation failed: {e}")
            return {"success": False, "error": str(e)}

    async def get_embedding_stats(self) -> Dict[str, Any]:
        """Get embedding tool statistics."""
        try:
            models = await self._list_models()
            
            # Get available embedding models
            embedding_models = []
            for model_info in models.get('models', []):
                model_name = model_info.get('name', '')
                if any(embed_model.value in model_name for embed_model in EmbeddingModel):
                    embedding_models.append({
                        "name": model_name,
                        "size": model_info.get('size', 'Unknown'),
                        "modified": model_info.get('modified_at', 'Unknown')
                    })
            
            return {
                "success": True,
                "total_models": len(models.get('models', [])),
                "embedding_models": embedding_models,
                "cache_size": len(self.embedding_cache),
                "supported_models": [model.value for model in EmbeddingModel],
                "processing_modes": [mode.value for mode in ProcessingMode],
                "search_modes": [mode.value for mode in VectorSearchMode],
                "batch_size": self.batch_size,
                "rate_limit": self.requests_per_minute,
                "cache_enabled": self.cache_enabled,
                "ollama_url": self.settings.ollama_base_url
            }
            
        except Exception as e:
            logger.error(f"Failed to get embedding stats: {e}")
            return {"success": False, "error": str(e)}

    async def clear_cache(self) -> Dict[str, Any]:
        """Clear the embedding cache."""
        try:
            cache_size = len(self.embedding_cache)
            self.embedding_cache.clear()
            logger.info(f"Cleared embedding cache ({cache_size} entries)")
            return {
                "success": True,
                "message": f"Cleared {cache_size} cached embeddings"
            }
        except Exception as e:
            logger.error(f"Failed to clear cache: {e}")
            return {"success": False, "error": str(e)}

    # Main tool methods for different operations
    async def process_embedding_request(self, embedding_input: EmbeddingInput) -> Dict[str, Any]:
        """Process an embedding request based on input parameters."""
        try:
            if isinstance(embedding_input.text, str):
                # Single text embedding
                result = await self.generate_single_embedding(
                    text=embedding_input.text,
                    model=embedding_input.model.value,
                    normalize=embedding_input.normalize,
                    include_metadata=embedding_input.include_metadata
                )
                return {
                    "success": True,
                    "mode": "single",
                    "result": {
                        "text": result.text,
                        "embedding": result.embedding,
                        "model": result.model,
                        "processing_time": result.processing_time,
                        "metadata": result.metadata
                    }
                }
            
            else:
                # Batch text embeddings
                results = await self.generate_batch_embeddings(
                    texts=embedding_input.text,
                    model=embedding_input.model.value,
                    normalize=embedding_input.normalize,
                    include_metadata=embedding_input.include_metadata
                )
                return {
                    "success": True,
                    "mode": "batch",
                    "count": len(results),
                    "results": [
                        {
                            "text": result.text,
                            "embedding": result.embedding,
                            "model": result.model,
                            "processing_time": result.processing_time,
                            "metadata": result.metadata
                        }
                        for result in results
                    ]
                }
                
        except Exception as e:
            logger.error(f"Embedding request processing failed: {e}")
            return {"success": False, "error": str(e)}

    async def process_similarity_search(self, search_input: SimilaritySearchInput) -> Dict[str, Any]:
        """Process a similarity search request."""
        try:
            results = await self.similarity_search(
                query_text=search_input.query_text,
                texts=search_input.texts,
                model=search_input.model.value,
                mode=search_input.mode,
                top_k=search_input.top_k,
                threshold=search_input.threshold
            )
            
            return {
                "success": True,
                "query": search_input.query_text,
                "mode": search_input.mode.value,
                "model": search_input.model.value,
                "top_k": search_input.top_k,
                "threshold": search_input.threshold,
                "found": len(results),
                "results": results
            }
            
        except Exception as e:
            logger.error(f"Similarity search processing failed: {e}")
            return {"success": False, "error": str(e)}

    async def process_model_management(self, management_input: ModelManagementInput) -> Dict[str, Any]:
        """Process a model management request."""
        try:
            result = await self.manage_model(
                model=management_input.model.value,
                operation=management_input.operation,
                force=management_input.force
            )
            
            return {
                "success": result.get("success", True),
                "operation": management_input.operation,
                "model": management_input.model.value,
                "result": result
            }
            
        except Exception as e:
            logger.error(f"Model management processing failed: {e}")
            return {"success": False, "error": str(e)}
