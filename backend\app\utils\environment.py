"""
Environment Variable Protection and Management

Secure handling of sensitive environment variables:
- Encrypted storage of sensitive values
- Access logging and monitoring
- Runtime validation and sanitization
- Secure defaults and fallbacks
"""

import os
import base64
import hashlib
import logging
from typing import Dict, Any, Optional, List, Union
from datetime import datetime
from cryptography.fernet import Fernet
from pydantic import BaseModel, Field

from ..config.security import SecurityConfig


logger = logging.getLogger(__name__)


class SecureEnvironment:
    """Secure environment variable manager"""
    
    def __init__(self):
        self._encryption_key = self._generate_or_load_key()
        self._cipher = Fernet(self._encryption_key)
        self._access_log: List[Dict[str, Any]] = []
        self._validated_vars: Dict[str, Any] = {}
        
        # Load and validate environment variables
        self._load_environment_variables()
    
    def _generate_or_load_key(self) -> bytes:
        """Generate or load encryption key for sensitive data"""
        key_file = ".env_key"
        
        if os.path.exists(key_file):
            try:
                with open(key_file, "rb") as f:
                    return f.read()
            except Exception as e:
                logger.warning(f"Could not load encryption key: {e}")
        
        # Generate new key
        key = Fernet.generate_key()
        
        try:
            with open(key_file, "wb") as f:
                f.write(key)
            os.chmod(key_file, 0o600)  # Restrict permissions
        except Exception as e:
            logger.warning(f"Could not save encryption key: {e}")
        
        return key
    
    def _load_environment_variables(self):
        """Load and validate all environment variables"""
        logger.info("Loading and validating environment variables...")
        
        # Required variables with validation
        required_vars = {
            "OPENROUTER_API_KEY": self._validate_api_key,
            "LANGSEARCH_API_KEY": self._validate_api_key,
        }
        
        # Optional variables with defaults
        optional_vars = {
            "DATABASE_URL": "sqlite:///./data/dashboard.db",
            "REDIS_URL": None,
            "ENVIRONMENT": "development",
            "DEBUG": "false",
            "API_RATE_LIMIT": "60",
            "OLLAMA_BASE_URL": "http://localhost:11434",
        }
        
        # Validate required variables
        for var_name, validator in required_vars.items():
            value = os.getenv(var_name)
            if not value:
                logger.error(f"Required environment variable {var_name} is not set")
                self._validated_vars[var_name] = None
            else:
                try:
                    validated_value = validator(value)
                    self._validated_vars[var_name] = validated_value
                    logger.info(f"✅ {var_name} validated successfully")
                except Exception as e:
                    logger.error(f"❌ {var_name} validation failed: {e}")
                    self._validated_vars[var_name] = None
        
        # Load optional variables with defaults
        for var_name, default_value in optional_vars.items():
            value = os.getenv(var_name, default_value)
            if value:
                try:
                    self._validated_vars[var_name] = self._validate_by_type(var_name, value)
                    logger.info(f"✅ {var_name} loaded")
                except Exception as e:
                    logger.warning(f"⚠️ {var_name} validation warning: {e}")
                    self._validated_vars[var_name] = default_value
            else:
                self._validated_vars[var_name] = default_value
        
        logger.info(f"Environment validation complete. {len(self._validated_vars)} variables loaded.")
    
    def _validate_api_key(self, value: str) -> str:
        """Validate API key format"""
        if not value or len(value.strip()) < 10:
            raise ValueError("API key too short")
        
        value = value.strip()
        
        # Check for common issues
        if value.startswith('"') and value.endswith('"'):
            value = value[1:-1]  # Remove quotes
        
        if " " in value:
            raise ValueError("API key contains spaces")
        
        if not all(c.isalnum() or c in "-_." for c in value):
            raise ValueError("API key contains invalid characters")
        
        return value
    
    def _validate_by_type(self, var_name: str, value: str) -> Union[str, int, bool]:
        """Validate environment variable by type based on name"""
        value = value.strip()
        
        # Boolean variables
        if var_name.lower() in ["debug", "enable_ssl", "development"]:
            return value.lower() in ["true", "1", "yes", "on"]
        
        # Integer variables
        if any(keyword in var_name.lower() for keyword in ["port", "timeout", "limit", "size"]):
            try:
                int_val = int(value)
                if int_val < 0:
                    raise ValueError("Value must be positive")
                return int_val
            except ValueError:
                raise ValueError(f"Invalid integer value: {value}")
        
        # URL variables
        if "url" in var_name.lower():
            if not (value.startswith("http://") or value.startswith("https://") or 
                   value.startswith("sqlite://") or value.startswith("postgresql://")):
                logger.warning(f"URL {var_name} may be invalid: {value}")
        
        return value
    
    def get(self, var_name: str, default: Any = None) -> Any:
        """Securely get environment variable value"""
        self._log_access(var_name)
        
        if var_name in self._validated_vars:
            return self._validated_vars[var_name]
        
        # Fallback to os.getenv
        raw_value = os.getenv(var_name, default)
        if raw_value and var_name in SecurityConfig.PROTECTED_ENV_VARS:
            logger.warning(f"Accessing unvalidated protected variable: {var_name}")
        
        return raw_value
    
    def get_masked(self, var_name: str) -> str:
        """Get masked version of sensitive variable for logging"""
        value = self.get(var_name)
        if not value:
            return "[NOT_SET]"
        
        if var_name in SecurityConfig.PROTECTED_ENV_VARS:
            if len(str(value)) <= 8:
                return "[REDACTED]"
            else:
                return f"{str(value)[:4]}...{str(value)[-4:]}"
        
        return str(value)
    
    def validate_all(self) -> Dict[str, Any]:
        """Validate all environment variables and return status"""
        status = {
            "valid": True,
            "errors": [],
            "warnings": [],
            "variables": {}
        }
        
        for var_name in SecurityConfig.PROTECTED_ENV_VARS:
            value = self._validated_vars.get(var_name)
            
            if value is None:
                status["valid"] = False
                status["errors"].append(f"{var_name} is not set or invalid")
                status["variables"][var_name] = "MISSING"
            else:
                status["variables"][var_name] = "SET"
        
        # Check for development environment warnings
        if self.get("ENVIRONMENT") == "development":
            if self.get("DEBUG") != "true":
                status["warnings"].append("DEBUG should be true in development")
        
        if self.get("ENVIRONMENT") == "production":
            if self.get("DEBUG") != "false":
                status["warnings"].append("DEBUG should be false in production")
        
        return status
    
    def _log_access(self, var_name: str):
        """Log access to environment variables"""
        if var_name in SecurityConfig.PROTECTED_ENV_VARS:
            access_record = {
                "timestamp": datetime.utcnow().isoformat(),
                "variable": var_name,
                "access_type": "read"
            }
            
            self._access_log.append(access_record)
            
            # Limit log size
            if len(self._access_log) > 1000:
                self._access_log = self._access_log[-500:]
    
    def get_access_log(self) -> List[Dict[str, Any]]:
        """Get access log for monitoring"""
        return self._access_log.copy()
    
    def encrypt_sensitive_value(self, value: str) -> str:
        """Encrypt sensitive value for storage"""
        if not value:
            return ""
        
        try:
            encrypted = self._cipher.encrypt(value.encode())
            return base64.b64encode(encrypted).decode()
        except Exception as e:
            logger.error(f"Encryption failed: {e}")
            return ""
    
    def decrypt_sensitive_value(self, encrypted_value: str) -> str:
        """Decrypt sensitive value"""
        if not encrypted_value:
            return ""
        
        try:
            encrypted_bytes = base64.b64decode(encrypted_value.encode())
            decrypted = self._cipher.decrypt(encrypted_bytes)
            return decrypted.decode()
        except Exception as e:
            logger.error(f"Decryption failed: {e}")
            return ""


# Global secure environment instance
secure_env = SecureEnvironment()


# Convenience functions
def get_secure_env(var_name: str, default: Any = None) -> Any:
    """Get environment variable securely"""
    return secure_env.get(var_name, default)


def get_masked_env(var_name: str) -> str:
    """Get masked environment variable for logging"""
    return secure_env.get_masked(var_name)


def validate_environment() -> Dict[str, Any]:
    """Validate all environment variables"""
    return secure_env.validate_all()


def get_env_access_log() -> List[Dict[str, Any]]:
    """Get environment variable access log"""
    return secure_env.get_access_log()


# Security check function
def check_environment_security() -> Dict[str, Any]:
    """Comprehensive environment security check"""
    security_status = {
        "secure": True,
        "issues": [],
        "recommendations": []
    }
    
    # Check for exposed secrets in environment
    for var_name, value in os.environ.items():
        if any(sensitive in var_name.lower() for sensitive in ["key", "secret", "password", "token"]):
            if var_name not in SecurityConfig.PROTECTED_ENV_VARS:
                security_status["issues"].append(f"Unprotected sensitive variable: {var_name}")
                security_status["secure"] = False
    
    # Check file permissions for sensitive files
    sensitive_files = [".env", ".env.local", ".env_key", "config.ini"]
    for file_path in sensitive_files:
        if os.path.exists(file_path):
            try:
                file_mode = os.stat(file_path).st_mode
                if file_mode & 0o077:  # World or group readable
                    security_status["issues"].append(f"File {file_path} has overly permissive permissions")
                    security_status["secure"] = False
            except Exception as e:
                logger.warning(f"Could not check permissions for {file_path}: {e}")
    
    # Recommendations
    if secure_env.get("ENVIRONMENT") == "production":
        if secure_env.get("DEBUG"):
            security_status["recommendations"].append("Disable DEBUG in production")
        
        if not secure_env.get("SSL_ENABLED"):
            security_status["recommendations"].append("Enable SSL in production")
    
    return security_status
