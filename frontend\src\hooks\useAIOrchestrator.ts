import { useState, useCallback, useRef, useEffect } from 'react'
import { useAppStore } from '@/stores/appStore'

/**
 * WebSocket connection states
 */
type ConnectionState = 'disconnected' | 'connecting' | 'connected' | 'reconnecting' | 'error'

/**
 * Processing step following mermaid diagram EXACTLY
 */
export interface ProcessingStep {
  id: string
  step: string
  message: string
  animation: string
  color?: string
  completed: boolean
}

/**
 * Input categories from mermaid diagram
 */
export type InputCategory = 'TASK' | 'EVENT' | 'AI_QUESTION'

/**
 * Question types for AI_QUESTION category
 */
export type QuestionType = 'SIMPLE_KNOWLEDGE' | 'DATABASE_SEARCH' | 'WEB_SEARCH'

/**
 * WebSocket message interface
 */
interface WebSocketMessage {
  type: string
  payload: any
  id?: string
}

/**
 * Environment variables interface for type safety
 * Note: These are already declared in vite-env.d.ts, so we reference them directly
 */

/**
 * AI Orchestrator Hook - Main Workflow for Task 20
 * 
 * INCREDIBLY IMPORTANT: Follow the flow described in the mermaid diagram EXACTLY
 * 
 * Main workflow hook integrating all AI operations:
 * - WebSocket connection management
 * - State management for processing steps per mermaid diagram
 * - Error handling and retry logic
 * - Result state management
 * - Visual feedback exactly matching examples/ai_orchestrator_flow.mermaid
 */
export const useAIOrchestrator = () => {
  const { setProcessing, addError, setWebsocketConnected } = useAppStore()
  
  // Connection state
  const [connectionState, setConnectionState] = useState<ConnectionState>('disconnected')
  const maxReconnectAttempts = 5
  
  // Processing state following mermaid diagram
  const [isProcessing, setIsProcessing] = useState(false)
  const [processingSteps, setProcessingSteps] = useState<ProcessingStep[]>([])
  const [currentStepIndex, setCurrentStepIndex] = useState(-1)
  const [category, setCategory] = useState<InputCategory | null>(null)
  const [questionType, setQuestionType] = useState<QuestionType | null>(null)
  
  // Results state
  const [processingResults, setProcessingResults] = useState<any>(null)
  const [lastMessage, setLastMessage] = useState<WebSocketMessage | null>(null)
  
  // WebSocket reference
  const wsRef = useRef<WebSocket | null>(null)
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const reconnectAttemptsRef = useRef(0)

  /**
   * CRITICAL: Define processing steps exactly matching mermaid diagram
   */
  const getProcessingSteps = useCallback((category: InputCategory, questionType?: QuestionType): ProcessingStep[] => {
    const baseSteps: ProcessingStep[] = [
      {
        id: 'analyzing',
        step: 'analyzing',
        message: 'Analyzing input...',
        animation: 'typing_indicator',
        completed: false
      },
      {
        id: 'categorizing',
        step: 'categorizing', 
        message: 'Categorizing...',
        animation: 'brain_animation',
        completed: false
      }
    ]

    if (category === 'TASK') {
      return [
        ...baseSteps,
        {
          id: 'task_identified',
          step: 'task_identified',
          message: 'Task identified!',
          animation: 'green_pulse',
          color: 'green',
          completed: false
        },
        {
          id: 'extracting_details',
          step: 'extracting_details',
          message: 'Extracting details...',
          animation: 'spinner_animation',
          completed: false
        },
        {
          id: 'auto_categorizing',
          step: 'auto_categorizing',
          message: 'Auto-categorizing...',
          animation: 'tag_animations',
          completed: false
        },
        {
          id: 'checking_dates',
          step: 'checking_dates',
          message: 'Checking for dates...',
          animation: 'calendar_scan',
          completed: false
        },
        {
          id: 'updating_task_list',
          step: 'updating_task_list',
          message: 'Updating task list...',
          animation: 'list_grow_animation',
          completed: false
        },
        {
          id: 'syncing_calendar',
          step: 'syncing_calendar',
          message: 'Syncing calendar...',
          animation: 'sync_spinner',
          completed: false
        },
        {
          id: 'task_complete',
          step: 'task_complete',
          message: 'Task added successfully!',
          animation: 'success_confetti',
          color: 'green',
          completed: false
        }
      ]
    }

    if (category === 'EVENT') {
      return [
        ...baseSteps,
        {
          id: 'event_identified',
          step: 'event_identified',
          message: 'Event identified!',
          animation: 'blue_pulse',
          color: 'blue',
          completed: false
        },
        {
          id: 'extracting_datetime',
          step: 'extracting_datetime',
          message: 'Extracting date/time...',
          animation: 'clock_animation',
          completed: false
        },
        {
          id: 'adding_to_calendar',
          step: 'adding_to_calendar',
          message: 'Adding to calendar...',
          animation: 'calendar_drop',
          completed: false
        },
        {
          id: 'syncing_calendar',
          step: 'syncing_calendar',
          message: 'Syncing calendar...',
          animation: 'sync_animation',
          completed: false
        },
        {
          id: 'event_complete',
          step: 'event_complete',
          message: 'Event scheduled!',
          animation: 'success_sparkle',
          color: 'blue',
          completed: false
        }
      ]
    }

    if (category === 'AI_QUESTION') {
      const questionSteps = [
        ...baseSteps,
        {
          id: 'question_identified',
          step: 'question_identified',
          message: 'Question identified!',
          animation: 'purple_pulse',
          color: 'purple',
          completed: false
        },
        {
          id: 'analyzing_query_type',
          step: 'analyzing_query_type',
          message: 'Analyzing query type...',
          animation: 'thinking_dots',
          completed: false
        }
      ]

      if (questionType === 'SIMPLE_KNOWLEDGE') {
        return [
          ...questionSteps,
          {
            id: 'accessing_knowledge',
            step: 'accessing_knowledge',
            message: 'Accessing knowledge...',
            animation: 'brain_glow',
            completed: false
          },
          {
            id: 'formatting_answer',
            step: 'formatting_answer',
            message: 'Formatting answer...',
            animation: 'text_typewriter',
            completed: false
          },
          {
            id: 'answer_ready',
            step: 'answer_ready',
            message: 'Answer ready!',
            animation: 'reveal_animation',
            color: 'purple',
            completed: false
          }
        ]
      }

      if (questionType === 'DATABASE_SEARCH') {
        return [
          ...questionSteps,
          {
            id: 'searching_database',
            step: 'searching_database',
            message: 'Searching database...',
            animation: 'radar_scan',
            completed: false
          },
          {
            id: 'generating_embeddings',
            step: 'generating_embeddings',
            message: 'Generating embeddings...',
            animation: 'matrix_animation',
            completed: false
          },
          {
            id: 'vector_searching',
            step: 'vector_searching',
            message: 'Vector searching...',
            animation: 'progress_dots',
            completed: false
          },
          {
            id: 'found_docs',
            step: 'found_docs',
            message: 'Found relevant docs...',
            animation: 'document_fly_in',
            completed: false
          },
          {
            id: 'processing_context',
            step: 'processing_context',
            message: 'Processing context...',
            animation: 'loading_spinner',
            completed: false
          },
          {
            id: 'generating_answer',
            step: 'generating_answer',
            message: 'Generating answer...',
            animation: 'typewriter_effect',
            completed: false
          },
          {
            id: 'database_search_complete',
            step: 'database_search_complete',
            message: 'Database search complete!',
            animation: 'success_fade_in',
            color: 'purple',
            completed: false
          }
        ]
      }

      if (questionType === 'WEB_SEARCH') {
        return [
          ...questionSteps,
          {
            id: 'starting_web_search',
            step: 'starting_web_search',
            message: 'Starting web search...',
            animation: 'globe_spin',
            completed: false
          },
          {
            id: 'calling_langsearch',
            step: 'calling_langsearch',
            message: 'Calling LangSearch API...',
            animation: 'api_pulse',
            completed: false
          },
          {
            id: 'processing_results',
            step: 'processing_results',
            message: 'Processing results...',
            animation: 'gear_animation',
            completed: false
          },
          {
            id: 'summarizing_findings',
            step: 'summarizing_findings',
            message: 'Summarizing findings...',
            animation: 'text_processing',
            completed: false
          },
          {
            id: 'adding_sources',
            step: 'adding_sources',
            message: 'Adding sources...',
            animation: 'link_animations',
            completed: false
          },
          {
            id: 'web_search_complete',
            step: 'web_search_complete',
            message: 'Web search complete!',
            animation: 'success_burst',
            color: 'purple',
            completed: false
          }
        ]
      }
    }

    return baseSteps
  }, [])

  /**
   * Advance to next processing step with animation
   */
  const advanceStep = useCallback(() => {
    setCurrentStepIndex(prev => {
      const nextIndex = prev + 1
      setProcessingSteps(steps => 
        steps.map((step, index) => ({
          ...step,
          completed: index <= prev
        }))
      )
      return nextIndex
    })
  }, [])

  /**
   * Complete processing with success
   */
  const completeProcessing = useCallback((results?: any) => {
    setProcessingSteps(steps => 
      steps.map(step => ({ ...step, completed: true }))
    )
    setIsProcessing(false)
    setProcessing(false)
    if (results) {
      setProcessingResults(results)
    }
    
    // Reset after animation completes
    setTimeout(() => {
      setCurrentStepIndex(-1)
      setProcessingSteps([])
      setCategory(null)
      setQuestionType(null)
    }, 3000)
  }, [setProcessing])

  /**
   * Handle processing error
   */
  const handleProcessingError = useCallback((error: string) => {
    setIsProcessing(false)
    setProcessing(false)
    addError(error, 'error', 'AI Processing')
    
    // Reset state
    setTimeout(() => {
      setCurrentStepIndex(-1)
      setProcessingSteps([])
      setCategory(null)
      setQuestionType(null)
    }, 2000)
  }, [setProcessing, addError])

  /**
   * Connect to WebSocket
   */
  const connect = useCallback(() => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      return
    }

    setConnectionState('connecting')
    
    try {
      const wsUrl = (import.meta as any).env?.PROD 
        ? `wss://${window.location.host}/ws`
        : 'ws://localhost:8000/ws'
      
      wsRef.current = new WebSocket(wsUrl)

      wsRef.current.onopen = () => {
        setConnectionState('connected')
        setWebsocketConnected(true)
        reconnectAttemptsRef.current = 0
        
        // Send initial connection message
        send({
          type: 'connect',
          payload: { clientId: crypto.randomUUID() }
        })
      }

      wsRef.current.onmessage = (event) => {
        try {
          const message: WebSocketMessage = JSON.parse(event.data)
          setLastMessage(message)
          handleMessage(message)
        } catch (error) {
          console.error('Failed to parse WebSocket message:', error)
        }
      }

      wsRef.current.onclose = (event) => {
        setConnectionState('disconnected')
        setWebsocketConnected(false)
        
        // Attempt to reconnect if not a normal closure
        if (event.code !== 1000 && reconnectAttemptsRef.current < maxReconnectAttempts) {
          scheduleReconnect()
        }
      }

      wsRef.current.onerror = (error) => {
        console.error('WebSocket error:', error)
        setConnectionState('error')
        addError('WebSocket connection error', 'error', 'useAIOrchestrator')
      }

    } catch (error) {
      console.error('Failed to create WebSocket connection:', error)
      setConnectionState('error')
      addError('Failed to connect to AI services', 'error', 'useAIOrchestrator')
    }
  }, [setWebsocketConnected, addError])

  /**
   * Disconnect WebSocket
   */
  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current)
    }
    
    if (wsRef.current) {
      wsRef.current.close(1000, 'Client disconnect')
      wsRef.current = null
    }
    
    setConnectionState('disconnected')
    setWebsocketConnected(false)
  }, [setWebsocketConnected])

  /**
   * Schedule reconnection attempt
   */
  const scheduleReconnect = useCallback(() => {
    if (reconnectAttemptsRef.current >= maxReconnectAttempts) {
      setConnectionState('error')
      addError('Max reconnection attempts reached', 'error', 'useAIOrchestrator')
      return
    }

    setConnectionState('reconnecting')
    reconnectAttemptsRef.current++
    
    const delay = Math.min(1000 * Math.pow(2, reconnectAttemptsRef.current), 10000)
    
    reconnectTimeoutRef.current = setTimeout(() => {
      connect()
    }, delay)
  }, [connect, addError])

  /**
   * Send message through WebSocket
   */
  const send = useCallback((message: WebSocketMessage) => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      const fullMessage = {
        ...message,
        id: message.id || crypto.randomUUID()
      }
      
      wsRef.current.send(JSON.stringify(fullMessage))
      return fullMessage.id
    } else {
      console.warn('WebSocket not connected, cannot send message')
      return null
    }
  }, [])

  /**
   * Handle incoming WebSocket messages
   * CRITICAL: This implements the exact flow from the mermaid diagram
   */
  const handleMessage = useCallback((message: WebSocketMessage) => {
    setLastMessage(message)
    
    switch (message.type) {
      case 'processing_step':
        // Handle streaming visual feedback steps from mermaid diagram
        const { step, stepCategory, stepQuestionType } = message.payload
        
        // Update current step based on mermaid diagram
        if (step === 'analyzing') {
          setCurrentStepIndex(0)
        } else if (step === 'categorizing') {
          setCurrentStepIndex(1)
        } else if (step === 'task_identified' || step === 'event_identified' || step === 'question_identified') {
          // Category has been determined, update steps
          setCategory(stepCategory)
          if (stepQuestionType) setQuestionType(stepQuestionType)
          
          const allSteps = getProcessingSteps(stepCategory, stepQuestionType)
          setProcessingSteps(allSteps)
          setCurrentStepIndex(2) // Move to the category-specific step
        } else {
          // Find step index in current steps
          const stepIndex = processingSteps.findIndex(s => s.step === step)
          if (stepIndex >= 0) {
            setCurrentStepIndex(stepIndex)
          }
        }
        
        // Auto-advance after a delay for smooth animations
        setTimeout(() => {
          advanceStep()
        }, 1000)
        break
        
      case 'categorization_result':
        // Update category and question type from AI
        const { category: resultCategory, questionType: resultQuestionType } = message.payload
        setCategory(resultCategory)
        if (resultQuestionType) setQuestionType(resultQuestionType)
        
        // Update processing steps to match category
        const categorySteps = getProcessingSteps(resultCategory, resultQuestionType)
        setProcessingSteps(categorySteps)
        break
        
      case 'processing_completed':
        const { results, category: finalCategory } = message.payload
        completeProcessing(results)
        setCategory(finalCategory)
        break
        
      case 'processing_error':
        handleProcessingError(message.payload.error)
        break
        
      case 'task_created':
        // Handle successful task creation
        setProcessingResults(message.payload)
        completeProcessing(message.payload)
        break
        
      case 'event_created':
        // Handle successful event creation  
        setProcessingResults(message.payload)
        completeProcessing(message.payload)
        break
        
      case 'search_results':
        // Handle AI question search results
        setProcessingResults(message.payload)
        completeProcessing(message.payload)
        break
        
      default:
        console.log('Unknown message type:', message.type, message.payload)
    }
  }, [setProcessing, addError, getProcessingSteps, processingSteps, advanceStep, completeProcessing, handleProcessingError])

  /**
   * Process user input through AI orchestrator
   * CRITICAL: This follows the mermaid diagram exactly with streaming visual feedback
   */
  const processInput = useCallback(async (input: string) => {
    if (!input.trim()) {
      throw new Error('Input cannot be empty')
    }

    if (connectionState !== 'connected') {
      throw new Error('Not connected to AI services')
    }

    try {
      // Reset processing state
      setIsProcessing(true)
      setProcessing(true)
      setCategory(null)
      setQuestionType(null)
      setProcessingResults(null)
      
      // Initialize with common processing steps from mermaid diagram
      const initialSteps = getProcessingSteps('TASK') // We'll update this based on actual categorization
      setProcessingSteps(initialSteps.slice(0, 2)) // Start with analyzing and categorizing
      setCurrentStepIndex(0)
      
      const messageId = send({
        type: 'process_input',
        payload: { input: input.trim() }
      })

      if (!messageId) {
        throw new Error('Failed to send message')
      }

      return messageId
    } catch (error) {
      console.error('Failed to process input:', error)
      handleProcessingError(error instanceof Error ? error.message : 'Unknown error')
      throw error
    }
  }, [connectionState, send, setProcessing, getProcessingSteps, handleProcessingError])

  /**
   * Get categorization for input
   */
  const categorizeInput = useCallback(async (input: string) => {
    if (connectionState !== 'connected') {
      throw new Error('Not connected to AI services')
    }

    return send({
      type: 'categorize_input',
      payload: { input: input.trim() }
    })
  }, [connectionState, send])

  /**
   * Create task from input
   */
  const createTask = useCallback(async (input: string, category?: string) => {
    if (connectionState !== 'connected') {
      throw new Error('Not connected to AI services')
    }

    return send({
      type: 'create_task',
      payload: { input: input.trim(), category }
    })
  }, [connectionState, send])

  /**
   * Create event from input
   */
  const createEvent = useCallback(async (input: string, category?: string) => {
    if (connectionState !== 'connected') {
      throw new Error('Not connected to AI services')
    }

    return send({
      type: 'create_event',
      payload: { input: input.trim(), category }
    })
  }, [connectionState, send])

  /**
   * Perform search
   */
  const performSearch = useCallback(async (query: string, searchType?: 'semantic' | 'web' | 'hybrid') => {
    if (connectionState !== 'connected') {
      throw new Error('Not connected to AI services')
    }

    return send({
      type: 'perform_search',
      payload: { query: query.trim(), searchType: searchType || 'hybrid' }
    })
  }, [connectionState, send])

  /**
   * Auto-connect on mount
   */
  useEffect(() => {
    connect()
    
    return () => {
      disconnect()
    }
  }, [connect, disconnect])

  /**
   * Cleanup on unmount
   */
  useEffect(() => {
    return () => {
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current)
      }
    }
  }, [])

  return {
    // Connection state
    connectionState,
    isConnected: connectionState === 'connected',
    isConnecting: connectionState === 'connecting',
    isReconnecting: connectionState === 'reconnecting',
    hasError: connectionState === 'error',
    
    // Processing state (following mermaid diagram)
    isProcessing,
    processingSteps,
    currentStepIndex,
    category,
    questionType,
    
    // Data
    lastMessage,
    processingResults,
    
    // Actions
    connect,
    disconnect,
    send,
    processInput,
    categorizeInput,
    createTask,
    createEvent,
    performSearch,
    
    // Utils
    reconnectAttempts: reconnectAttemptsRef.current,
    maxReconnectAttempts,
  }
}
