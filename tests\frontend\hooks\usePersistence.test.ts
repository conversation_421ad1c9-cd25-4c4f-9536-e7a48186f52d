// usePersistence Hook Tests - Complex State Management Testing
import { renderHook, act } from '@testing-library/react';
import { usePersistence } from '../../../frontend/src/hooks/usePersistence';

// Mock storage APIs
const mockLocalStorage = (() => {
  let store: Record<string, string> = {};
  
  return {
    getItem: jest.fn((key: string) => store[key] || null),
    setItem: jest.fn((key: string, value: string) => {
      store[key] = value;
    }),
    removeItem: jest.fn((key: string) => {
      delete store[key];
    }),
    clear: jest.fn(() => {
      store = {};
    }),
    get length() {
      return Object.keys(store).length;
    },
    key: jest.fn((index: number) => Object.keys(store)[index] || null),
  };
})();

// Mock fetch for remote storage
const mockFetch = jest.fn();
global.fetch = mockFetch;

Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
});

describe('usePersistence Hook', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockLocalStorage.clear();
    mockFetch.mockClear();
  });

  it('initializes with empty state when no stored data exists', () => {
    const { result } = renderHook(() => 
      usePersistence<{ name: string; age: number }>('test-key', { name: '', age: 0 })
    );

    expect(result.current[0].data).toEqual({ name: '', age: 0 });
    expect(result.current[0].isLoading).toBe(false);
    expect(result.current[0].error).toBeNull();
  });

  it('loads data from localStorage on initialization', () => {
    const testData = { name: 'John', age: 30 };
    mockLocalStorage.setItem('test-key', JSON.stringify(testData));

    const { result } = renderHook(() => 
      usePersistence<{ name: string; age: number }>('test-key', { name: '', age: 0 })
    );

    expect(result.current[0].data).toEqual(testData);
  });

  it('persists data to localStorage when updated', async () => {
    const { result } = renderHook(() => 
      usePersistence<{ name: string; age: number }>('test-key', { name: '', age: 0 })
    );

    const newData = { name: 'Jane', age: 25 };

    await act(async () => {
      await result.current.save(newData);
    });

    expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
      'test-key',
      JSON.stringify(newData)
    );
    expect(result.current.data).toEqual(newData);
  });

  it('handles partial updates correctly', async () => {
    const initialData = { name: 'John', age: 30 };
    const { result } = renderHook(() => 
      usePersistence<{ name: string; age: number }>('test-key', initialData)
    );

    await act(async () => {
      result.current[1]({ age: 31 });
    });

    expect(result.current[0].data).toEqual({ name: 'John', age: 31 });
  });

  it('debounces save operations to prevent excessive writes', async () => {
    jest.useFakeTimers();
    
    const { result } = renderHook(() => 
      usePersistence<{ count: number }>('test-key', { count: 0 }, {
        debounceMs: 500,
      })
    );

    // Make multiple rapid updates
    await act(async () => {
      result.current.update({ count: 1 });
      result.current.update({ count: 2 });
      result.current.update({ count: 3 });
    });

    // Should not have saved yet due to debouncing
    expect(mockLocalStorage.setItem).not.toHaveBeenCalled();

    // Fast-forward time to trigger debounced save
    await act(async () => {
      jest.advanceTimersByTime(500);
    });

    expect(mockLocalStorage.setItem).toHaveBeenCalledTimes(1);
    expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
      'test-key',
      JSON.stringify({ count: 3 })
    );

    jest.useRealTimers();
  });

  it('syncs with remote storage when enabled', async () => {
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({ success: true, data: { name: 'Remote User' } }),
    });

    const { result } = renderHook(() => 
      usePersistence<{ name: string }>('test-key', { name: '' }, {
        syncToRemote: true,
      })
    );

    await act(async () => {
      await result.current.sync();
    });

    expect(mockFetch).toHaveBeenCalledWith('/api/sync/test-key');
    expect(result.current.data).toEqual({ name: 'Remote User' });
  });

  it('handles sync conflicts with conflict resolution strategy', async () => {
    const localData = { name: 'Local User', updatedAt: '2024-01-15T10:00:00Z' };
    const remoteData = { name: 'Remote User', updatedAt: '2024-01-15T11:00:00Z' };

    mockLocalStorage.setItem('test-key', JSON.stringify(localData));
    
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({ success: true, data: remoteData }),
    });

    const { result } = renderHook(() => 
      usePersistence<{ name: string; updatedAt: string }>('test-key', { name: '', updatedAt: '' }, {
        syncToRemote: true,
      })
    );

    await act(async () => {
      await result.current.sync();
    });

    // Remote data should win due to conflict resolution strategy
    expect(result.current.data).toEqual(remoteData);
  });

  it('provides form recovery functionality', async () => {
    const formData = {
      title: 'Draft Article',
      content: 'This is a draft...',
      tags: ['draft', 'article'],
    };

    const { result } = renderHook(() => 
      usePersistence<typeof formData>('form-draft', formData, {
        syncToRemote: true,
        debounceMs: 1000,
      })
    );

    await act(async () => {
      await result.current.save(formData);
    });

    // Simulate page reload
    const { result: newResult } = renderHook(() => 
      usePersistence<typeof formData>('form-draft', { title: '', content: '', tags: [] })
    );

    expect(newResult.current[0].data).toEqual(formData);
  });

  it('cleans up expired data based on TTL', async () => {
    jest.useFakeTimers();
    const currentTime = Date.now();
    jest.setSystemTime(currentTime);

    const { result } = renderHook(() => 
      usePersistence<{ message: string }>('temp-key', { message: '' }, {
        // ttlMs: 5000, // TTL not supported in current interface
        debounceMs: 1000,
      })
    );

    await act(async () => {
      await result.current.save({ message: 'Temporary data' });
    });

    // Fast-forward past TTL
    jest.setSystemTime(currentTime + 6000);

    await act(async () => {
      result.current.cleanup();
    });

    expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('temp-key');
    expect(result.current.data).toEqual({});

    jest.useRealTimers();
  });

  it('handles storage quota exceeded gracefully', async () => {
    mockLocalStorage.setItem.mockImplementation(() => {
      throw new DOMException('QuotaExceededError');
    });

    const { result } = renderHook(() => 
      usePersistence<{ data: string }>('test-key', { data: '' })
    );

    await act(async () => {
      await result.current[2].save({ data: 'test' });
    });

    expect(result.current[0].error).toContain('storage quota exceeded');
  });

  it('provides statistics about storage usage', () => {
    const { result } = renderHook(() => 
      usePersistence<{ count: number }>('test-key', { count: 0 })
    );

    const stats = result.current[2].getStats();

    expect(stats).toHaveProperty('totalKeys');
    expect(stats).toHaveProperty('totalSize');
    expect(stats).toHaveProperty('lastAccess');
    expect(stats).toHaveProperty('saveCount');
  });

  it('supports encryption for sensitive data', async () => {
    const sensitiveData = { token: 'secret-api-key', userId: '12345' };

    const { result } = renderHook(() => 
      usePersistence<typeof sensitiveData>('secure-key', sensitiveData, {
        // encrypt: true, // Encryption not supported in current interface
        syncToRemote: false,
      })
    );

    await act(async () => {
      await result.current.save(sensitiveData);
    });

    // Verify data is encrypted in storage
    const storedValue = mockLocalStorage.getItem('secure-key');
    expect(storedValue).not.toContain('secret-api-key');
    expect(storedValue).not.toContain('12345');

    // But decrypted correctly when loaded
    expect(result.current.data).toEqual(sensitiveData);
  });

  it('handles concurrent access safely', async () => {
    const { result: hook1 } = renderHook(() => 
      usePersistence<{ counter: number }>('shared-key', { counter: 0 })
    );
    
    const { result: hook2 } = renderHook(() => 
      usePersistence<{ counter: number }>('shared-key', { counter: 0 })
    );

    // Simulate concurrent updates
    await act(async () => {
      await Promise.all([
        hook1.current[1]({ counter: 1 }),
        hook2.current.update({ counter: 2 }),
      ]);
    });

    // Both hooks should eventually have the same data
    expect(hook1.current.data).toEqual(hook2.current.data);
  });

  it('supports custom serialization/deserialization', async () => {
    const customData = new Date('2024-01-15T10:00:00Z');

    const { result } = renderHook(() => 
      usePersistence<Date>('date-key', customData, {
        // serialize/deserialize not supported in current interface
        syncToRemote: false,
      })
    );

    await act(async () => {
      await result.current.save(customData);
    });

    expect(result.current.data).toEqual(customData);
    expect(result.current.data).toBeInstanceOf(Date);
  });

  it('provides offline mode support', async () => {
    // Simulate offline state
    Object.defineProperty(navigator, 'onLine', {
      writable: true,
      value: false,
    });

    mockFetch.mockRejectedValue(new Error('Network error'));

    const { result } = renderHook(() => 
      usePersistence<{ message: string }>('offline-key', { message: '' }, {
        syncToRemote: true,
        // offlineMode: true, // Not supported in current interface
      })
    );

    await act(async () => {
      await result.current.save({ message: 'Offline data' });
    });

    // Should save locally even when offline
    expect(result.current.data).toEqual({ message: 'Offline data' });
    expect(result.current.isPendingSync).toBe(true);

    // When back online, should attempt sync
    Object.defineProperty(navigator, 'onLine', {
      value: true,
    });

    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({ success: true }),
    });

    await act(async () => {
      window.dispatchEvent(new Event('online'));
    });

    expect(mockFetch).toHaveBeenCalled();
    expect(result.current.isPendingSync).toBe(false);
  });

  it('supports batch operations for performance', async () => {
    const { result } = renderHook(() => 
      usePersistence<{ items: string[] }>('batch-key', { items: [] })
    );

    const batchUpdates = [
      { items: ['item1'] },
      { items: ['item1', 'item2'] },
      { items: ['item1', 'item2', 'item3'] },
    ];

    await act(async () => {
      await result.current.batchUpdate(batchUpdates);
    });

    expect(result.current.data).toEqual({ items: ['item1', 'item2', 'item3'] });
    // Should only call setItem once for the final state
    expect(mockLocalStorage.setItem).toHaveBeenCalledTimes(1);
  });

  it('handles memory pressure by clearing non-essential data', async () => {
    const { result } = renderHook(() => 
      usePersistence<{ cache: any[] }>('cache-key', { cache: [] }, {
        // priority: 'low', // Not supported in current interface
        // maxMemoryUsage: 1024, // Not supported in current interface
        debounceMs: 500,
      })
    );

    const largeData = { cache: new Array(1000).fill('large-data-item') };

    await act(async () => {
      await result.current.save(largeData);
    });

    // Simulate memory pressure event
    await act(async () => {
      window.dispatchEvent(new Event('memorypressure'));
    });

    // Low priority data should be cleared
    expect(result.current.data).toEqual({});
  });
});
