import { motion } from 'framer-motion'

interface SearchResult {
  title: string
  snippet: string
  url: string
  relevance: number
}

interface SearchResultsProps {
  results: SearchResult[]
  maxResults?: number
}

/**
 * SearchResults Component
 * PATTERN: Search results display with relevance scoring
 * Features:
 * - Relevance-based sorting and highlighting
 * - Snippet preview with highlighting
 * - External link handling
 * - Staggered animations
 */
export function SearchResults({ results, maxResults = 5 }: SearchResultsProps) {
  const sortedResults = results
    .sort((a, b) => b.relevance - a.relevance)
    .slice(0, maxResults)

  const getRelevanceColor = (relevance: number) => {
    if (relevance >= 0.8) return 'bg-green-100 text-green-800'
    if (relevance >= 0.6) return 'bg-yellow-100 text-yellow-800'
    return 'bg-red-100 text-red-800'
  }

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 20
      }
    }
  }

  if (results.length === 0) {
    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        className="text-center py-8 text-gray-500"
      >
        No search results found.
      </motion.div>
    )
  }

  return (
    <div>
      <h4 className="font-medium text-gray-900 mb-4 flex items-center gap-2">
        <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
        </svg>
        Search Results ({results.length})
      </h4>

      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="space-y-4"
      >
        {sortedResults.map((result, index) => (
          <motion.div
            key={`${result.url}-${index}`}
            variants={itemVariants}
            className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
          >
            <div className="flex items-start justify-between gap-3">
              <div className="flex-1">
                {/* Title */}
                <motion.a
                  href={result.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="block font-medium text-blue-600 hover:text-blue-800 transition-colors"
                  whileHover={{ scale: 1.01 }}
                >
                  {result.title}
                </motion.a>

                {/* URL */}
                <div className="text-sm text-green-600 mt-1 truncate">
                  {result.url}
                </div>

                {/* Snippet */}
                <p className="text-sm text-gray-600 mt-2 leading-relaxed">
                  {result.snippet}
                </p>
              </div>

              {/* Relevance Score */}
              <motion.span
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.2 }}
                className={`px-2 py-1 rounded-full text-xs font-medium ${getRelevanceColor(result.relevance)}`}
              >
                {Math.round(result.relevance * 100)}%
              </motion.span>
            </div>

            {/* External Link Icon */}
            <motion.div 
              className="mt-3 flex items-center justify-end"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.3 }}
            >
              <a
                href={result.url}
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center gap-1 text-xs text-gray-500 hover:text-gray-700 transition-colors"
              >
                <span>Open source</span>
                <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                </svg>
              </a>
            </motion.div>
          </motion.div>
        ))}
      </motion.div>

      {results.length > maxResults && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.5 }}
          className="mt-4 text-center"
        >
          <p className="text-sm text-gray-500">
            Showing {maxResults} of {results.length} results
          </p>
        </motion.div>
      )}
    </div>
  )
}
