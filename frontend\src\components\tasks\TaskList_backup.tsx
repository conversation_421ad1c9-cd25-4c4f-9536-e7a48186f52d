import { useState, useMemo, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useAIOrchestrator } from '../../hooks/useAIOrchestrator'
import { TaskItem } from './TaskItem.tsx'
import { Task, TaskFilters } from './types'

/**
 * TaskList Component
 * PATTERN: Dynamic categorization display with Framer Motion animations
 * Features:
 * - Framer Motion list animations (grow, slide, stagger)
 * - Filter and search functionality  
 * - Drag and drop reordering
 * - Real-time updates from WebSocket
 */
export function TaskList() {
  // AI Orchestrator integration for real-time updates
  const { category } = useAIOrchestrator()
  
  // Real tasks from backend API with persistence
  const [tasks, setTasks] = useState<Task[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Load tasks from backend API
  useEffect(() => {
    const loadTasks = async () => {
      try {
        setLoading(true)
        setError(null)
        
        const response = await fetch('/api/tasks', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        })
        
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }
        
        const data = await response.json()
        setTasks(data.tasks || [])
      } catch (err) {
        console.error('Failed to load tasks:', err)
        setError('Failed to load tasks. Please try again.')
        // Fallback to empty array on error
        setTasks([])
      } finally {
        setLoading(false)
      }
    }
    
    loadTasks()
  }, [])

  // Filter state
  const [filters, setFilters] = useState<TaskFilters>({})
  const [searchTerm, setSearchTerm] = useState('')

  // Filtered and sorted tasks
  const filteredTasks = useMemo(() => {
    let filtered = tasks

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(task => 
        task.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        task.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        task.category?.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    // Category filter
    if (filters.category) {
      filtered = filtered.filter(task => task.category === filters.category)
    }

    // Priority filter
    if (filters.priority) {
      filtered = filtered.filter(task => task.priority === filters.priority)
    }

    // Completion filter
    if (filters.completed !== undefined) {
      filtered = filtered.filter(task => task.completed === filters.completed)
    }

    // Sort by priority (urgent > high > medium > low) then by due date
    return filtered.sort((a, b) => {
      const priorityOrder = { urgent: 4, high: 3, medium: 2, low: 1 }
      const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority]
      
      if (priorityDiff !== 0) return priorityDiff
      
      // If same priority, sort by due date
      if (a.due_date && b.due_date) {
        return new Date(a.due_date).getTime() - new Date(b.due_date).getTime()
      }
      
      return 0
    })
  }, [tasks, filters, searchTerm])

  // Get unique categories for filter dropdown
  const categories = useMemo(() => {
    const cats = tasks.map(task => task.ai_generated_category || task.category).filter(Boolean)
    return [...new Set(cats)] as string[]
  }, [tasks])

  // Animation variants for stagger effect
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  }

  const itemVariants = {
    hidden: { 
      opacity: 0, 
      y: 20,
      scale: 0.95
    },
    visible: { 
      opacity: 1, 
      y: 0,
      scale: 1,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 24
      }
    },
    exit: {
      opacity: 0,
      y: -20,
      scale: 0.95,
      transition: {
        duration: 0.2
      }
    }
  }

  // Handle task completion toggle
  const handleTaskToggle = (taskId: string) => {
    setTasks(prev => prev.map(task => 
      task.id === taskId 
        ? { ...task, completed: !task.completed }
        : task
    ))
  }

  // Handle task deletion
  const handleTaskDelete = (taskId: string) => {
    setTasks(prev => prev.filter(task => task.id !== taskId))
  }

  // Handle task editing
  const handleTaskEdit = (taskId: string, updates: Partial<Task>) => {
    setTasks(prev => prev.map(task => 
      task.id === taskId 
        ? { ...task, ...updates }
        : task
    ))
  }

  return (
    <div className="w-full max-w-4xl mx-auto p-6">
      {/* Header with title and filters */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, ease: "easeOut" }}
        className="mb-6"
      >
        <h2 className="text-2xl font-bold text-white mb-4">Smart Task List</h2>
        
        {/* Loading State */}
        {loading && (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <span className="ml-2 text-gray-300">Loading tasks...</span>
          </div>
        )}
        
        {/* Error State */}
        {error && (
          <div className="bg-red-900/50 border border-red-500 rounded-lg p-4 mb-4">
            <p className="text-red-300">{error}</p>
          </div>
        )}
        
        {/* Search and Filter Controls */}
        {!loading && (
        <div className="flex flex-col sm:flex-row gap-4 mb-4">
          {/* Search Input */}
          <div className="flex-1">
            <input
              type="text"
              placeholder="Search tasks..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:border-blue-500 focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
            />
          </div>
          
          {/* Category Filter */}
          <select
            value={filters.category || ''}
            onChange={(e) => setFilters(prev => ({ ...prev, category: e.target.value || undefined }))}
            className="px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:border-blue-500"
          >
            <option value="">All Categories</option>
            {categories.map(cat => (
              <option key={cat} value={cat}>{cat}</option>
            ))}
          </select>
          
          {/* Priority Filter */}
          <select
            value={filters.priority || ''}
            onChange={(e) => setFilters(prev => ({ ...prev, priority: (e.target.value || undefined) as TaskFilters['priority'] }))}
            className="px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:border-blue-500"
          >
            <option value="">All Priorities</option>
            <option value="urgent">Urgent</option>
            <option value="high">High</option>
            <option value="medium">Medium</option>
            <option value="low">Low</option>
          </select>
          
          {/* Completion Filter */}
          <select
            value={filters.completed === undefined ? '' : filters.completed.toString()}
            onChange={(e) => {
              const value = e.target.value
              setFilters(prev => ({ 
                ...prev, 
                completed: value === '' ? undefined : value === 'true'
              }))
            }}
            className="px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:border-blue-500"
          >
            <option value="">All Tasks</option>
            <option value="false">Active</option>
            <option value="true">Completed</option>
          </select>
        </div>
        
        {/* Results Count */}
        <div className="text-gray-400 text-sm">
          Showing {filteredTasks.length} of {tasks.length} tasks
        </div>
        )}
      </motion.div>

      {/* Task List with Framer Motion animations */}
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="space-y-3"
      >
        <AnimatePresence mode="wait">
          {filteredTasks.length === 0 ? (
            <motion.div
              key="empty"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.9 }}
              className="text-center py-12 text-gray-400"
            >
              <div className="text-4xl mb-4">📝</div>
              <h3 className="text-lg font-medium mb-2">No tasks found</h3>
              <p>Try adjusting your filters or add a new task</p>
            </motion.div>
          ) : (
            filteredTasks.map((task) => (
              <motion.div
                key={task.id}
                variants={itemVariants}
                layout
                className="task-item-wrapper"
              >
                <TaskItem
                  task={task}
                  onToggle={() => handleTaskToggle(task.id)}
                  onDelete={() => handleTaskDelete(task.id)}
                  onEdit={(updates: Partial<Task>) => handleTaskEdit(task.id, updates)}
                />
              </motion.div>
            ))
          )}
        </AnimatePresence>
      </motion.div>

      {/* Real-time update indicator */}
      {category === 'TASK' && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 20 }}
          className="fixed bottom-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg"
        >
          ✨ New task detected from AI!
        </motion.div>
      )}
    </div>
  )
}
