import { useState } from 'react'
import { motion } from 'framer-motion'
import { Task } from './types'

/**
 * TaskItem Component
 * PATTERN: Individual task with edit capabilities
 * Features:
 * - Completion animations
 * - Priority indicators with color coding
 * - Due date handling and overdue states
 * - Category badges (AI-generated)
 */
interface TaskItemProps {
  task: Task
  onToggle: () => void
  onDelete: () => void
  onEdit: (updates: Partial<Task>) => void
}

export function TaskItem({ task, onToggle, onDelete, onEdit }: TaskItemProps) {
  const [isEditing, setIsEditing] = useState(false)
  const [editTitle, setEditTitle] = useState(task.title)
  const [editDescription, setEditDescription] = useState(task.description || '')

  // Priority color mapping
  const priorityColors = {
    urgent: 'bg-red-500',
    high: 'bg-orange-500',
    medium: 'bg-yellow-500',
    low: 'bg-green-500'
  }

  // Due date status
  const getDueDateStatus = () => {
    if (!task.due_date) return null
    
    const dueDate = new Date(task.due_date)
    const today = new Date()
    const diffTime = dueDate.getTime() - today.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    
    if (diffDays < 0) return { status: 'overdue', text: `${Math.abs(diffDays)} days overdue`, color: 'text-red-400' }
    if (diffDays === 0) return { status: 'today', text: 'Due today', color: 'text-orange-400' }
    if (diffDays === 1) return { status: 'tomorrow', text: 'Due tomorrow', color: 'text-yellow-400' }
    if (diffDays <= 7) return { status: 'soon', text: `Due in ${diffDays} days`, color: 'text-blue-400' }
    return { status: 'future', text: `Due ${dueDate.toLocaleDateString()}`, color: 'text-gray-400' }
  }

  const dueDateStatus = getDueDateStatus()

  // Handle save edit
  const handleSaveEdit = () => {
    onEdit({
      title: editTitle.trim(),
      description: editDescription.trim() || undefined
    })
    setIsEditing(false)
  }

  // Handle cancel edit
  const handleCancelEdit = () => {
    setEditTitle(task.title)
    setEditDescription(task.description || '')
    setIsEditing(false)
  }

  return (
    <motion.div
      layout
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.9, x: -100 }}
      whileHover={{ scale: 1.02 }}
      className={`bg-gray-800 border border-gray-700 rounded-lg p-4 transition-all duration-200 ${
        task.completed ? 'opacity-75' : ''
      }`}
    >
      <div className="flex items-start gap-3">
        {/* Completion Checkbox */}
        <motion.button
          onClick={onToggle}
          whileTap={{ scale: 0.9 }}
          className={`relative w-5 h-5 mt-1 rounded border-2 flex-shrink-0 ${
            task.completed
              ? 'bg-green-500 border-green-500'
              : 'border-gray-500 hover:border-green-400'
          }`}
        >
          {task.completed && (
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              className="absolute inset-0 flex items-center justify-center text-white text-xs"
            >
              ✓
            </motion.div>
          )}
        </motion.button>

        {/* Task Content */}
        <div className="flex-1 min-w-0">
          {isEditing ? (
            /* Edit Mode */
            <div className="space-y-3">
              <input
                type="text"
                value={editTitle}
                onChange={(e) => setEditTitle(e.target.value)}
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white"
                placeholder="Task title..."
                autoFocus
              />
              <textarea
                value={editDescription}
                onChange={(e) => setEditDescription(e.target.value)}
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white resize-none"
                placeholder="Task description..."
                rows={2}
              />
              <div className="flex gap-2">
                <button
                  onClick={handleSaveEdit}
                  className="px-3 py-1 bg-green-600 hover:bg-green-700 text-white rounded text-sm"
                >
                  Save
                </button>
                <button
                  onClick={handleCancelEdit}
                  className="px-3 py-1 bg-gray-600 hover:bg-gray-700 text-white rounded text-sm"
                >
                  Cancel
                </button>
              </div>
            </div>
          ) : (
            /* Display Mode */
            <div>
              {/* Title and Priority */}
              <div className="flex items-center gap-2 mb-1">
                <h3 className={`font-medium ${
                  task.completed ? 'line-through text-gray-500' : 'text-white'
                }`}>
                  {task.title}
                </h3>
                
                {/* Priority Indicator */}
                <div className={`w-2 h-2 rounded-full ${priorityColors[task.priority]}`} />
                <span className={`text-xs px-2 py-1 rounded ${priorityColors[task.priority]} text-white`}>
                  {task.priority.toUpperCase()}
                </span>
              </div>

              {/* Description */}
              {task.description && (
                <p className={`text-sm mb-2 ${
                  task.completed ? 'line-through text-gray-500' : 'text-gray-300'
                }`}>
                  {task.description}
                </p>
              )}

              {/* Category Badge and Due Date */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  {/* AI-Generated Category Badge */}
                  {task.ai_generated_category && (
                    <span className="text-xs px-2 py-1 bg-blue-600 text-white rounded-full">
                      🤖 {task.ai_generated_category}
                    </span>
                  )}
                  
                  {/* Regular Category */}
                  {task.category && task.category !== task.ai_generated_category && (
                    <span className="text-xs px-2 py-1 bg-gray-600 text-gray-300 rounded-full">
                      {task.category}
                    </span>
                  )}
                </div>

                {/* Due Date */}
                {dueDateStatus && (
                  <span className={`text-xs ${dueDateStatus.color}`}>
                    {dueDateStatus.text}
                  </span>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Action Buttons */}
        {!isEditing && (
          <div className="flex gap-1">
            <motion.button
              onClick={() => setIsEditing(true)}
              whileTap={{ scale: 0.9 }}
              className="p-1 text-gray-400 hover:text-blue-400 transition-colors"
              title="Edit task"
            >
              ✏️
            </motion.button>
            
            <motion.button
              onClick={onDelete}
              whileTap={{ scale: 0.9 }}
              className="p-1 text-gray-400 hover:text-red-400 transition-colors"
              title="Delete task"
            >
              🗑️
            </motion.button>
          </div>
        )}
      </div>
    </motion.div>
  )
}
