@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom base styles */
@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  body {
    @apply bg-background-primary text-text-primary;
    margin: 0;
    padding: 0;
    overflow-x: hidden;
  }

  * {
    border-color: theme('colors.border.primary');
  }

  /* Scrollbar styles */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-background-secondary;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-primary-600 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-primary-500;
  }
}

/* Custom component styles */
@layer components {
  .glass-effect {
    @apply backdrop-blur-lg bg-background-secondary/80 border border-border-primary;
  }

  .gradient-text {
    @apply bg-gradient-to-r from-accent-blue to-accent-purple bg-clip-text text-transparent;
  }

  .button-primary {
    @apply bg-accent-blue hover:bg-accent-blue/90 text-white font-medium px-4 py-2 rounded-xl transition-all duration-200 ease-out transform hover:scale-105 active:scale-95 shadow-medium hover:shadow-glow;
  }

  .button-secondary {
    @apply bg-background-tertiary hover:bg-background-hover text-text-primary font-medium px-4 py-2 rounded-xl transition-all duration-200 ease-out border border-border-primary hover:border-border-focus;
  }

  .input-primary {
    @apply bg-background-secondary border border-border-primary rounded-xl px-4 py-3 text-text-primary placeholder-text-muted focus:border-accent-blue focus:ring-2 focus:ring-accent-blue/20 focus:outline-none transition-all duration-200;
  }

  .card-primary {
    @apply bg-background-secondary border border-border-primary rounded-2xl shadow-soft backdrop-blur-sm;
  }

  .loading-spinner {
    @apply animate-spin rounded-full border-2 border-border-primary border-t-accent-blue;
  }

  .shimmer-effect {
    @apply relative overflow-hidden;
  }

  .shimmer-effect::after {
    @apply absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent animate-shimmer;
    content: '';
  }
}

/* Utility classes */
@layer utilities {
  .text-shadow-sm {
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  }

  .text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  }

  .text-shadow-lg {
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.5);
  }

  .perspective-1000 {
    perspective: 1000px;
  }

  .transform-style-preserve-3d {
    transform-style: preserve-3d;
  }

  .backface-hidden {
    backface-visibility: hidden;
  }
}

/* Framer Motion custom classes */
.motion-safe {
  @media (prefers-reduced-motion: no-preference) {
    /* Animation styles when motion is safe */
  }
}

/* Focus styles for accessibility */
.focus-visible\:ring-accent-blue:focus-visible {
  @apply ring-2 ring-accent-blue ring-offset-2 ring-offset-background-primary;
}

/* Selection styles */
::selection {
  @apply bg-accent-blue/30 text-text-primary;
}

::-moz-selection {
  @apply bg-accent-blue/30 text-text-primary;
}
