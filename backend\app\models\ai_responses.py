"""
AI response schemas for structured outputs from Mirascope agents.

This module defines response models specifically designed for AI agents
to return structured data, following Mirascope response_model patterns.
"""

from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any, Union
from datetime import datetime
from enum import Enum

from app.models.pydantic_models import (
    InputCategory, TaskPriority, QuestionType, 
    TaskStatus, EventStatus
)


class AICategorizationResponse(BaseModel):
    """
    Response model for the orchestrator agent's categorization.
    
    This model is used with Mirascope's response_model parameter
    to ensure structured output from free OpenRouter models.
    """
    category: InputCategory = Field(..., description="The determined category for the input")
    confidence: float = Field(..., ge=0.0, le=1.0, description="Confidence score for the categorization")
    reasoning: str = Field(..., description="Clear explanation of why this category was chosen")
    key_indicators: List[str] = Field(..., description="Specific words/phrases that led to this decision")
    
    # Processing metadata
    requires_additional_info: bool = Field(default=False, description="Whether more information is needed")
    suggested_clarifications: List[str] = Field(default=[], description="Questions to ask user if more info needed")
    
    class Config:
        schema_extra = {
            "example": {
                "category": "task",
                "confidence": 0.85,
                "reasoning": "Contains action-oriented language 'need to' and 'project', indicating a task to be completed",
                "key_indicators": ["need to", "project", "complete", "deadline"],
                "requires_additional_info": False,
                "suggested_clarifications": []
            }
        }


class AITaskExtractionResponse(BaseModel):
    """Response model for task agent's task extraction and processing."""
    
    title: str = Field(..., description="Clear, concise task title")
    description: Optional[str] = Field(None, description="Detailed task description if available")
    category: str = Field(..., description="AI-generated task category (no hardcoded options)")
    priority: TaskPriority = Field(default=TaskPriority.MEDIUM, description="Estimated task priority")
    
    # Date extraction
    has_due_date: bool = Field(default=False, description="Whether a due date was mentioned")
    extracted_due_date: Optional[str] = Field(None, description="Raw date string if found")
    parsed_due_date: Optional[datetime] = Field(None, description="Parsed datetime if successful")
    date_confidence: Optional[float] = Field(None, description="Confidence in date parsing")
    
    # AI reasoning
    extraction_reasoning: str = Field(..., description="How the task details were extracted")
    category_reasoning: str = Field(..., description="Why this category was assigned")
    priority_reasoning: str = Field(..., description="Why this priority was assigned")
    
    # Additional suggestions
    suggested_subtasks: List[str] = Field(default=[], description="Potential subtasks identified")
    estimated_time: Optional[str] = Field(None, description="Estimated time to complete if determinable")
    
    class Config:
        schema_extra = {
            "example": {
                "title": "Complete project documentation",
                "description": "Write comprehensive documentation for the AI dashboard project including setup instructions and API reference",
                "category": "development",
                "priority": "high",
                "has_due_date": True,
                "extracted_due_date": "end of week",
                "parsed_due_date": "2024-01-26T17:00:00Z",
                "date_confidence": 0.7,
                "extraction_reasoning": "Identified action verb 'complete' with object 'project documentation'",
                "category_reasoning": "Technical nature and mention of 'project' indicates development work",
                "priority_reasoning": "Documentation is typically high priority for project completion"
            }
        }


class AIEventExtractionResponse(BaseModel):
    """Response model for calendar agent's event extraction and processing."""
    
    title: str = Field(..., description="Clear event title")
    description: Optional[str] = Field(None, description="Event description")
    location: Optional[str] = Field(None, description="Event location if mentioned")
    
    # Date/time extraction
    has_start_time: bool = Field(..., description="Whether start time was identified")
    extracted_start_time: str = Field(..., description="Raw start time string")
    parsed_start_time: Optional[datetime] = Field(None, description="Parsed start datetime")
    
    has_end_time: bool = Field(default=False, description="Whether end time was mentioned")
    extracted_end_time: Optional[str] = Field(None, description="Raw end time string")
    parsed_end_time: Optional[datetime] = Field(None, description="Parsed end datetime")
    
    all_day: bool = Field(default=False, description="Whether this is an all-day event")
    date_confidence: float = Field(..., description="Confidence in datetime parsing")
    
    # Event classification
    event_type: str = Field(..., description="AI-determined event type")
    requires_reminder: bool = Field(default=True, description="Whether event should have reminder")
    suggested_reminder_minutes: int = Field(default=15, description="Suggested reminder time in minutes")
    
    # AI processing info
    extraction_reasoning: str = Field(..., description="How event details were extracted")
    datetime_reasoning: str = Field(..., description="How dates/times were interpreted")
    conflicts_checked: bool = Field(default=False, description="Whether conflict checking was performed")
    potential_conflicts: List[str] = Field(default=[], description="Potential scheduling conflicts found")
    
    # Additional information requests
    needs_clarification: bool = Field(default=False, description="Whether clarification is needed")
    clarification_questions: List[str] = Field(default=[], description="Questions to ask user")
    
    class Config:
        schema_extra = {
            "example": {
                "title": "Team meeting",
                "description": "Weekly sync meeting with development team",
                "location": "Conference Room A",
                "has_start_time": True,
                "extracted_start_time": "tomorrow 2pm",
                "parsed_start_time": "2024-01-24T14:00:00Z",
                "has_end_time": True,
                "extracted_end_time": "3pm",
                "parsed_end_time": "2024-01-24T15:00:00Z",
                "date_confidence": 0.9,
                "event_type": "meeting",
                "extraction_reasoning": "Identified meeting context with time reference"
            }
        }


class AIQuestionAnalysisResponse(BaseModel):
    """Response model for search agent's question analysis and routing."""
    
    question_type: QuestionType = Field(..., description="Type of question identified")
    confidence: float = Field(..., ge=0.0, le=1.0, description="Confidence in question type classification")
    
    # Question understanding
    intent: str = Field(..., description="What the user is trying to find out")
    key_concepts: List[str] = Field(..., description="Important concepts extracted from question")
    entities: List[str] = Field(default=[], description="Named entities found in question")
    
    # Tool selection
    recommended_tools: List[str] = Field(..., description="Tools that should be used to answer")
    tool_reasoning: str = Field(..., description="Why these tools were selected")
    
    # Search strategy
    search_queries: List[str] = Field(default=[], description="Suggested search queries")
    search_scope: str = Field(..., description="Scope of search needed (local, web, knowledge)")
    
    # Processing expectations
    expected_answer_type: str = Field(..., description="Type of answer expected (factual, list, explanation, etc.)")
    complexity_level: str = Field(..., description="Question complexity (simple, moderate, complex)")
    
    class Config:
        schema_extra = {
            "example": {
                "question_type": "web_search",
                "confidence": 0.8,
                "intent": "Find current information about AI developments",
                "key_concepts": ["artificial intelligence", "latest developments", "2024"],
                "recommended_tools": ["web_search_tool"],
                "tool_reasoning": "Question asks for current information requiring web search",
                "search_queries": ["AI developments 2024", "artificial intelligence news"],
                "search_scope": "web",
                "expected_answer_type": "summary_with_sources"
            }
        }


class AISearchResultsResponse(BaseModel):
    """Response model for search operations and result synthesis."""
    
    # Search metadata
    query: str = Field(..., description="Original search query")
    search_type: str = Field(..., description="Type of search performed")
    results_count: int = Field(..., description="Number of results found")
    search_time_ms: int = Field(..., description="Search execution time")
    
    # Results
    results: List[Dict[str, Any]] = Field(..., description="Search results with metadata")
    synthesized_answer: str = Field(..., description="AI-synthesized answer from results")
    
    # Source tracking
    sources_used: List[str] = Field(..., description="Sources that contributed to the answer")
    reliability_score: float = Field(..., ge=0.0, le=1.0, description="Overall reliability of sources")
    
    # Answer quality
    answer_confidence: float = Field(..., ge=0.0, le=1.0, description="Confidence in the synthesized answer")
    completeness_score: float = Field(..., ge=0.0, le=1.0, description="How complete the answer is")
    
    # Additional context
    related_questions: List[str] = Field(default=[], description="Related questions user might ask")
    follow_up_suggestions: List[str] = Field(default=[], description="Suggested follow-up actions")
    
    class Config:
        schema_extra = {
            "example": {
                "query": "latest AI developments 2024",
                "search_type": "web_search",
                "results_count": 5,
                "search_time_ms": 1250,
                "synthesized_answer": "Recent AI developments in 2024 include...",
                "sources_used": ["https://example.com/ai-news", "https://tech.example.org"],
                "reliability_score": 0.85,
                "answer_confidence": 0.8
            }
        }


class AIProcessingError(BaseModel):
    """Response model for error handling in AI operations."""
    
    error_type: str = Field(..., description="Type of error encountered")
    error_message: str = Field(..., description="Human-readable error description")
    error_code: str = Field(..., description="Machine-readable error code")
    
    # Context
    operation: str = Field(..., description="Operation that failed")
    input_data: Optional[str] = Field(None, description="Input that caused the error")
    
    # Recovery
    recoverable: bool = Field(..., description="Whether error can be retried")
    suggested_action: Optional[str] = Field(None, description="Suggested user action")
    retry_with_fallback: bool = Field(default=False, description="Whether to try fallback model")
    
    # Debug info
    model_used: Optional[str] = Field(None, description="AI model that failed")
    timestamp: datetime = Field(default_factory=datetime.now)
    
    class Config:
        schema_extra = {
            "example": {
                "error_type": "parsing_error",
                "error_message": "Could not parse the date 'sometime next week'",
                "error_code": "DATE_PARSE_FAILED",
                "operation": "event_extraction",
                "recoverable": True,
                "suggested_action": "Please provide a specific date and time"
            }
        }


# Export all AI response models
__all__ = [
    "AICategorizationResponse",
    "AITaskExtractionResponse", 
    "AIEventExtractionResponse",
    "AIQuestionAnalysisResponse",
    "AISearchResultsResponse",
    "AIProcessingError"
]
