/**
 * Error Recovery React Component
 * 
 * Displays user-friendly error messages with recovery options
 */

import React from 'react';
import { ErrorSeverity, ApiError } from '../utils/errorHandling';

interface ErrorRecoveryProps {
  error?: any;
  onRetry?: () => void;
  onCancel?: () => void;
  showDetails?: boolean;
}

export const ErrorRecovery: React.FC<ErrorRecoveryProps> = ({ 
  error, 
  onRetry, 
  onCancel, 
  showDetails = false 
}) => {
  const isApiError = (error: any): error is ApiError => {
    return error && typeof error === 'object' && error.error === true;
  };

  const getErrorMessage = (error: any): string => {
    if (isApiError(error)) {
      return error.message;
    }
    
    if (error instanceof Error) {
      return error.message;
    }
    
    if (typeof error === 'string') {
      return error;
    }
    
    return 'An unexpected error occurred';
  };

  const getErrorSeverity = (error: any): ErrorSeverity => {
    if (isApiError(error)) {
      return error.severity;
    }
    
    return ErrorSeverity.MEDIUM;
  };

  const message = getErrorMessage(error);
  const severity = getErrorSeverity(error);
  const apiError = isApiError(error);

  const severityColors = {
    [ErrorSeverity.LOW]: 'border-blue-200 bg-blue-50 text-blue-800',
    [ErrorSeverity.MEDIUM]: 'border-yellow-200 bg-yellow-50 text-yellow-800',
    [ErrorSeverity.HIGH]: 'border-orange-200 bg-orange-50 text-orange-800',
    [ErrorSeverity.CRITICAL]: 'border-red-200 bg-red-50 text-red-800',
  };

  const severityIcons = {
    [ErrorSeverity.LOW]: 'ℹ️',
    [ErrorSeverity.MEDIUM]: '⚠️',
    [ErrorSeverity.HIGH]: '🚨',
    [ErrorSeverity.CRITICAL]: '💥',
  };

  return (
    <div className={`border rounded-lg p-4 ${severityColors[severity]}`}>
      <div className="flex items-start space-x-3">
        <span className="text-2xl">{severityIcons[severity]}</span>
        <div className="flex-1">
          <h3 className="font-medium">Something went wrong</h3>
          <p className="mt-1 text-sm">{message}</p>
          
          {apiError && error.service_status?.degraded && (
            <div className="mt-2 p-2 bg-white bg-opacity-50 rounded">
              <p className="text-xs font-medium">Service Status:</p>
              <p className="text-xs">{error.service_status.message}</p>
              {error.service_status.features_disabled && (
                <p className="text-xs mt-1">
                  Disabled: {error.service_status.features_disabled.join(', ')}
                </p>
              )}
            </div>
          )}

          {showDetails && apiError && error.debug && (
            <details className="mt-2">
              <summary className="cursor-pointer text-xs font-medium">
                Technical Details
              </summary>
              <pre className="mt-1 text-xs bg-white bg-opacity-50 p-2 rounded overflow-auto">
                {JSON.stringify(error.debug, null, 2)}
              </pre>
            </details>
          )}

          <div className="mt-3 flex space-x-2">
            {onRetry && apiError && error.recoverable && (
              <button
                onClick={onRetry}
                className="px-3 py-1 bg-white bg-opacity-75 hover:bg-opacity-100 rounded text-sm font-medium transition-colors"
              >
                Try Again
                {error.retry_after && ` (wait ${error.retry_after}s)`}
              </button>
            )}
            {onCancel && (
              <button
                onClick={onCancel}
                className="px-3 py-1 bg-white bg-opacity-50 hover:bg-opacity-75 rounded text-sm transition-colors"
              >
                Cancel
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
