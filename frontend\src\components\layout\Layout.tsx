import React from 'react'
import { motion } from 'framer-motion'
import Sidebar from './Sidebar'
import { useAppStore } from '@/stores/appStore'

/**
 * Props for the Layout component
 */
interface LayoutProps {
  children: React.ReactNode
}

/**
 * Main layout wrapper component with sidebar integration
 * 
 * Features:
 * - Responsive layout handling
 * - Sidebar integration with main content area
 * - Global loading states
 * - Smooth transitions between states
 */
const Layout: React.FC<LayoutProps> = ({ children }) => {
  const { sidebarCollapsed, isProcessing, currentOperation } = useAppStore()

  return (
    <div className="layout-container h-screen bg-background-primary overflow-hidden">
      {/* Global loading overlay */}
      {isProcessing && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 z-50 bg-background-primary/80 backdrop-blur-sm flex items-center justify-center"
        >
          <div className="glass-effect p-6 rounded-2xl max-w-sm mx-4">
            <div className="flex items-center gap-4">
              <div className="loading-spinner w-8 h-8" />
              <div>
                <div className="text-text-primary font-medium">
                  {currentOperation || 'Processing...'}
                </div>
                <div className="text-text-secondary text-sm mt-1">
                  AI is working on your request
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      )}

      <div className="flex h-full">
        {/* Sidebar */}
        <Sidebar />

        {/* Main content area */}
        <motion.main
          initial={false}
          animate={{
            marginLeft: sidebarCollapsed ? '4rem' : '16rem',
          }}
          transition={{
            type: 'spring',
            stiffness: 300,
            damping: 30,
          }}
          className="flex-1 relative overflow-hidden"
        >
          {/* Content wrapper with padding */}
          <div className="h-full overflow-y-auto">
            <div className="min-h-full p-6">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
                className="max-w-7xl mx-auto"
              >
                {children}
              </motion.div>
            </div>
          </div>

          {/* Scroll to top button */}
          <ScrollToTopButton />
        </motion.main>

        {/* Mobile sidebar overlay */}
        {!sidebarCollapsed && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 z-30 lg:hidden"
            onClick={() => useAppStore.getState().toggleSidebar()}
          />
        )}
      </div>
    </div>
  )
}

/**
 * Scroll to top button component
 */
const ScrollToTopButton: React.FC = () => {
  const [showButton, setShowButton] = React.useState(false)

  React.useEffect(() => {
    const handleScroll = () => {
      const scrollY = window.scrollY || document.documentElement.scrollTop
      setShowButton(scrollY > 400)
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    })
  }

  if (!showButton) return null

  return (
    <motion.button
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.8 }}
      whileHover={{ scale: 1.1 }}
      whileTap={{ scale: 0.9 }}
      onClick={scrollToTop}
      className="fixed bottom-8 right-8 z-40 w-12 h-12 bg-accent-blue hover:bg-accent-blue/90 text-white rounded-full shadow-lg flex items-center justify-center transition-all duration-200"
    >
      <svg
        width="20"
        height="20"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      >
        <path d="m18 15-6-6-6 6" />
      </svg>
    </motion.button>
  )
}

export default Layout
