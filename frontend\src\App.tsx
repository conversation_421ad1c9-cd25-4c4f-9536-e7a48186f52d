import React, { Suspense, useEffect } from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import { motion, AnimatePresence } from 'framer-motion'
import { Toaster } from 'react-hot-toast'
import Layout from './components/layout/Layout'
import LoadingScreen from './components/ui/LoadingScreen'
import { ErrorBoundary } from './components/ErrorBoundary'
import { useAppStore } from './stores/appStore'
import { useWebSocket } from './hooks/useWebSocketWrapper'
import { useNetworkStatus, setupGlobalErrorHandling } from './utils/errorHandling'

// Lazy-loaded pages for better performance
const Dashboard = React.lazy(() => import('./pages/Dashboard'))
const Tasks = React.lazy(() => import('./pages/Tasks'))
const Events = React.lazy(() => import('./pages/Events'))
const Search = React.lazy(() => import('./pages/Search'))
const Settings = React.lazy(() => import('./pages/Settings'))

/**
 * Main App component with routing, layout wrapper, and global state management.
 * 
 * Features:
 * - React Router for navigation
 * - Framer Motion for page transitions
 * - Global state management with Zustand
 * - WebSocket connection for real-time updates
 * - Comprehensive error handling with boundaries
 * - Network status monitoring
 * - Toast notifications for user feedback
 * - Lazy loading for performance
 */
const App: React.FC = () => {
  const { initialize, isInitialized } = useAppStore()
  const { connect, disconnect, isConnected } = useWebSocket()
  const isOnline = useNetworkStatus()

  useEffect(() => {
    // Setup global error handling
    setupGlobalErrorHandling()
    
    // Initialize application state
    initialize()
    
    // Connect to WebSocket for real-time updates
    connect()
    
    // Cleanup WebSocket connection on unmount
    return () => {
      disconnect()
    }
  }, [initialize, connect, disconnect])

  // Show loading screen while app initializes
  if (!isInitialized) {
    return <LoadingScreen message="Initializing AI Dashboard..." />
  }

  return (
    <ErrorBoundary>
      <div className="app-container min-h-screen bg-background-primary">
        {/* Connection status indicators */}
        <AnimatePresence>
          {!isConnected && isOnline && (
            <motion.div
              initial={{ opacity: 0, y: -50 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -50 }}
              className="fixed top-4 left-1/2 transform -translate-x-1/2 z-50"
            >
              <div className="glass-effect px-4 py-2 rounded-xl text-sm text-text-secondary">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-accent-yellow rounded-full animate-pulse" />
                  Reconnecting to AI services...
                </div>
              </div>
            </motion.div>
          )}
          
          {!isOnline && (
            <motion.div
              initial={{ opacity: 0, y: -50 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -50 }}
              className="fixed top-4 left-1/2 transform -translate-x-1/2 z-50"
            >
              <div className="glass-effect px-4 py-2 rounded-xl text-sm text-red-600 border border-red-200">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-red-500 rounded-full" />
                  No internet connection
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        <Layout>
          <ErrorBoundary fallback={
            <div className="min-h-[60vh] flex items-center justify-center">
              <div className="text-center">
                <h2 className="text-xl font-semibold mb-2">Page Error</h2>
                <p className="text-gray-600 mb-4">This page encountered an error</p>
                <button 
                  onClick={() => window.location.reload()} 
                  className="button-primary"
                >
                  Reload Page
                </button>
              </div>
            </div>
          }>
            <Suspense fallback={<LoadingScreen message="Loading page..." />}>
              <Routes>
                {/* Default route redirects to dashboard */}
                <Route path="/" element={<Navigate to="/dashboard" replace />} />
                
                {/* Main application routes */}
                <Route path="/dashboard" element={<Dashboard />} />
                <Route path="/tasks" element={<Tasks />} />
                <Route path="/events" element={<Events />} />
                <Route path="/search" element={<Search />} />
                <Route path="/settings" element={<Settings />} />
                
                {/* Catch-all route for 404 */}
                <Route path="*" element={<NotFoundPage />} />
              </Routes>
            </Suspense>
          </ErrorBoundary>
        </Layout>

        {/* Toast notifications */}
        <Toaster 
          position="top-right"
          toastOptions={{
            duration: 4000,
            style: {
              background: 'var(--glass-bg)',
              border: '1px solid var(--glass-border)',
              backdropFilter: 'var(--glass-blur)',
              color: 'var(--text-primary)',
            },
          }}
        />
      </div>
    </ErrorBoundary>
  )
}

/**
 * 404 Not Found page component
 */
const NotFoundPage: React.FC = () => {
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.9 }}
      transition={{ duration: 0.3 }}
      className="min-h-[60vh] flex items-center justify-center"
    >
      <div className="text-center max-w-md mx-auto px-4">
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.1, type: "spring", stiffness: 200 }}
          className="text-8xl mb-4"
        >
          🤖
        </motion.div>
        
        <motion.h1
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="text-3xl font-bold text-text-primary mb-4"
        >
          Page Not Found
        </motion.h1>
        
        <motion.p
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="text-text-secondary mb-8"
        >
          The AI couldn't find what you're looking for. Let's get you back on track.
        </motion.p>
        
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <button
            onClick={() => window.history.back()}
            className="button-secondary mr-4"
          >
            Go Back
          </button>
          <button
            onClick={() => window.location.href = '/dashboard'}
            className="button-primary"
          >
            Go to Dashboard
          </button>
        </motion.div>
      </div>
    </motion.div>
  )
}

export default App
