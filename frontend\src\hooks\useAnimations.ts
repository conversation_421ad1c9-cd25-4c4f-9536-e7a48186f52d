import { useState, useEffect, useCallback } from 'react'
import { useAppStore } from '@/stores/appStore'

/**
 * Processing step interface
 */
export interface ProcessingStep {
  id: string
  label: string
  description: string
  status: 'pending' | 'active' | 'completed' | 'error'
  progress?: number
}

/**
 * Animation Hook - State Management for Task 19
 * 
 * Custom hook for animation state management:
 * - Sequential animation triggering per mermaid diagram
 * - Spring physics configuration for organic feel
 * - Error state animations (shake, fade, etc.)
 * - Success state celebrations (confetti, pulse, etc.)
 */
export const useAnimations = () => {
  const { isProcessing } = useAppStore()
  const [currentStep, setCurrentStep] = useState(-1)
  const [processingSteps, setProcessingSteps] = useState<ProcessingStep[]>([])
  const [completedSteps, setCompletedSteps] = useState<string[]>([])
  const [showSuccessAnimation, setShowSuccessAnimation] = useState(false)
  const [showErrorAnimation, setShowErrorAnimation] = useState(false)

  /**
   * Start animation sequence
   */
  const startAnimation = useCallback((steps: ProcessingStep[] = []) => {
    setCurrentStep(0)
    setProcessingSteps(steps)
    setCompletedSteps([])
    setShowSuccessAnimation(false)
    setShowErrorAnimation(false)
  }, [])

  /**
   * Advance to next step
   */
  const nextStep = useCallback(() => {
    setCurrentStep(prev => {
      const next = prev + 1
      if (next > 0) {
        setCompletedSteps(prevCompleted => [...prevCompleted, `step-${prev}`])
      }
      return next
    })
  }, [])

  /**
   * Complete animation with success
   */
  const completeWithSuccess = useCallback(() => {
    setCurrentStep(-1)
    setShowSuccessAnimation(true)
    
    // Auto-hide success animation after 3 seconds
    setTimeout(() => {
      setShowSuccessAnimation(false)
    }, 3000)
  }, [])

  /**
   * Complete animation with error
   */
  const completeWithError = useCallback(() => {
    setCurrentStep(-1)
    setShowErrorAnimation(true)
    
    // Auto-hide error animation after 4 seconds
    setTimeout(() => {
      setShowErrorAnimation(false)
    }, 4000)
  }, [])

  /**
   * Reset animation state
   */
  const resetAnimation = useCallback(() => {
    setCurrentStep(-1)
    setProcessingSteps([])
    setCompletedSteps([])
    setShowSuccessAnimation(false)
    setShowErrorAnimation(false)
  }, [])

  /**
   * Auto-advance steps based on processing state
   */
  useEffect(() => {
    if (!isProcessing && currentStep >= 0) {
      // Processing finished, show success or error
      completeWithSuccess()
    }
  }, [isProcessing, currentStep, completeWithSuccess])

  /**
   * Spring physics configuration for different animation types
   */
  const springConfigs = {
    gentle: {
      type: 'spring' as const,
      stiffness: 100,
      damping: 15,
    },
    bouncy: {
      type: 'spring' as const,
      stiffness: 300,
      damping: 20,
    },
    snappy: {
      type: 'spring' as const,
      stiffness: 400,
      damping: 25,
    },
    wobbly: {
      type: 'spring' as const,
      stiffness: 500,
      damping: 15,
    },
  }

  /**
   * Error animation variants
   */
  const errorAnimations = {
    shake: {
      x: [0, -10, 10, -10, 10, 0],
      transition: { duration: 0.6 }
    },
    bounce: {
      y: [0, -20, 0, -10, 0],
      transition: { duration: 0.8 }
    },
    pulse: {
      scale: [1, 1.05, 1],
      transition: { duration: 0.4, repeat: 3 }
    },
  }

  /**
   * Success animation variants
   */
  const successAnimations = {
    celebrate: {
      scale: [1, 1.2, 1],
      rotate: [0, 5, -5, 0],
      transition: { duration: 0.6 }
    },
    pulse: {
      scale: [1, 1.1, 1],
      opacity: [1, 0.8, 1],
      transition: { duration: 0.8, repeat: 2 }
    },
    confetti: {
      y: [0, -30, 0],
      scale: [1, 1.3, 1],
      rotate: [0, 360],
      transition: { duration: 1.2 }
    },
  }

  return {
    // State
    currentStep,
    processingSteps,
    completedSteps,
    showSuccessAnimation,
    showErrorAnimation,
    
    // Actions
    startAnimation,
    nextStep,
    completeWithSuccess,
    completeWithError,
    resetAnimation,
    
    // Configuration
    springConfigs,
    errorAnimations,
    successAnimations,
  }
}
