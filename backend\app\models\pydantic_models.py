"""
Pydantic models for AI-Powered Dashboard data validation.

This module defines all data structures used throughout the application,
following the patterns from the PRP document and research documentation.
"""

from pydantic import BaseModel, Field, validator
from typing import List, Optional, Union, Dict, Any
from datetime import datetime
from enum import Enum


class InputCategory(str, Enum):
    """AI-determined input categories."""
    TASK = "task"
    EVENT = "event"
    AI_QUESTION = "ai_question"


class TaskPriority(str, Enum):
    """Task priority levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    URGENT = "urgent"


class QuestionType(str, Enum):
    """AI-determined question sub-categories."""
    SIMPLE_KNOWLEDGE = "simple_knowledge"
    DATABASE_SEARCH = "database_search"
    WEB_SEARCH = "web_search"


class TaskStatus(str, Enum):
    """Task completion status."""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    CANCELLED = "cancelled"


class EventStatus(str, Enum):
    """Event status."""
    SCHEDULED = "scheduled"
    COMPLETED = "completed"
    CANCELLED = "cancelled"


class UserInput(BaseModel):
    """Raw user input from hero bar."""
    text: str = Field(..., min_length=1, description="User's input text")
    timestamp: datetime = Field(default_factory=datetime.now)

    @validator('text')
    def validate_text_not_empty(cls, v):
        """Ensure text is not just whitespace."""
        if not v.strip():
            raise ValueError('Input text cannot be empty or just whitespace')
        return v.strip()


class CategoryDecision(BaseModel):
    """AI categorization result."""
    category: InputCategory = Field(..., description="Determined category")
    confidence: float = Field(..., ge=0.0, le=1.0, description="AI confidence score")
    reasoning: str = Field(..., description="Why this category was chosen")
    processing_steps: List[str] = Field(default=[], description="Visual feedback steps")
    suggested_actions: Optional[List[str]] = Field(default=[], description="Suggested next steps")


class TaskData(BaseModel):
    """Task data model."""
    id: Optional[str] = None
    title: str = Field(..., min_length=1)
    description: Optional[str] = None
    category: Optional[str] = None  # AI-generated category
    priority: TaskPriority = TaskPriority.MEDIUM
    status: TaskStatus = TaskStatus.PENDING
    due_date: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: Optional[datetime] = None
    
    # AI metadata
    ai_generated_category: Optional[str] = None
    ai_confidence: Optional[float] = Field(None, ge=0.0, le=1.0)
    ai_reasoning: Optional[str] = None
    ai_suggestions: Optional[Dict[str, Any]] = None


class EventData(BaseModel):
    """Calendar event data model."""
    id: Optional[str] = None
    title: str = Field(..., min_length=1)
    description: Optional[str] = None
    location: Optional[str] = None
    
    # Timing information
    start_datetime: datetime
    end_datetime: Optional[datetime] = None
    all_day: bool = False
    timezone: str = "UTC"
    
    # Event metadata
    event_type: Optional[str] = None
    reminder_minutes: Optional[int] = Field(None, ge=0)
    status: EventStatus = EventStatus.SCHEDULED
    
    # Timestamps
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: Optional[datetime] = None
    
    # AI metadata
    ai_confidence: Optional[float] = Field(None, ge=0.0, le=1.0)
    ai_extracted_datetime: Optional[Dict[str, Any]] = None
    ai_conflicts: Optional[List[str]] = None

    @validator('end_datetime')
    def validate_end_after_start(cls, v, values):
        """Ensure end datetime is after start datetime."""
        if v and 'start_datetime' in values and v <= values['start_datetime']:
            raise ValueError('End datetime must be after start datetime')
        return v


class AIQuestion(BaseModel):
    """AI question processing model."""
    text: str = Field(..., min_length=1)
    question_type: QuestionType
    reasoning: str = Field(..., description="Why this question type")
    confidence: float = Field(..., ge=0.0, le=1.0)
    suggested_tools: List[str] = Field(default=[], description="Tools to use")


class SearchResult(BaseModel):
    """Search result from web or database."""
    title: str
    content: str
    source: str
    url: Optional[str] = None
    relevance_score: float = Field(..., ge=0.0, le=1.0)
    snippet: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


class AIResponse(BaseModel):
    """Final AI response to user."""
    original_input: str
    category: InputCategory
    processing_steps: List[str]  # For visual feedback
    result: Union[TaskData, EventData, str]  # Task/Event object or answer string
    search_results: Optional[List[SearchResult]] = None
    sources: Optional[List[str]] = None
    confidence: float = Field(..., ge=0.0, le=1.0)
    processing_time_ms: Optional[int] = None
    model_used: Optional[str] = None
    token_usage: Optional[Dict[str, int]] = None


class AnimationStep(BaseModel):
    """Individual animation step for visual feedback."""
    id: str
    message: str
    animation_type: str  # "pulse", "spin", "typewriter", "fade", etc.
    duration: float = 1.0
    delay: float = 0.0
    completed: bool = False
    data: Optional[Dict[str, Any]] = None  # Additional animation data


class ProcessingState(BaseModel):
    """Overall processing state for animations."""
    current_step: int = 0
    steps: List[AnimationStep] = []
    is_processing: bool = False
    error: Optional[str] = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    total_duration_ms: Optional[int] = None


class WebSocketMessage(BaseModel):
    """WebSocket message format."""
    type: str  # "processing_update", "error", "complete", etc.
    data: Dict[str, Any]
    timestamp: datetime = Field(default_factory=datetime.now)
    session_id: Optional[str] = None


class EmbeddingData(BaseModel):
    """Embedding data for semantic search."""
    content: str
    content_type: str
    content_id: Optional[int] = None
    embedding: List[float]
    embedding_model: str
    chunk_index: int = 0
    token_count: Optional[int] = None


class PerformanceMetrics(BaseModel):
    """Performance tracking for AI operations."""
    operation_name: str
    start_time: datetime
    end_time: Optional[datetime] = None
    duration_ms: Optional[int] = None
    tokens_used: Optional[int] = None
    model_used: Optional[str] = None
    success: bool = True
    error_message: Optional[str] = None


class HealthCheck(BaseModel):
    """API health check response."""
    status: str = "healthy"
    service: str = "ai-powered-dashboard-api"
    version: str = "1.0.0"
    timestamp: datetime = Field(default_factory=datetime.now)
    database_connected: bool = True
    ollama_connected: bool = True
    services: Dict[str, bool] = Field(default_factory=dict)


# Request/Response models for API endpoints
class ProcessInputRequest(BaseModel):
    """Request model for processing user input."""
    input_text: str = Field(..., min_length=1)
    session_id: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


class ProcessInputResponse(BaseModel):
    """Response model for processed input."""
    success: bool
    result: Optional[AIResponse] = None
    error: Optional[str] = None
    session_id: str
    processing_id: str


class TaskListResponse(BaseModel):
    """Response model for task list."""
    tasks: List[TaskData]
    total_count: int
    page: int = 1
    page_size: int = 50
    filters: Optional[Dict[str, Any]] = None


class EventListResponse(BaseModel):
    """Response model for event list."""
    events: List[EventData]
    total_count: int
    page: int = 1
    page_size: int = 50
    filters: Optional[Dict[str, Any]] = None


# Export all models
__all__ = [
    # Enums
    "InputCategory", "TaskPriority", "QuestionType", "TaskStatus", "EventStatus",
    
    # Core models
    "UserInput", "CategoryDecision", "TaskData", "EventData", "AIQuestion",
    "SearchResult", "AIResponse",
    
    # Animation and state models
    "AnimationStep", "ProcessingState", "WebSocketMessage",
    
    # Service models
    "EmbeddingData", "PerformanceMetrics", "HealthCheck",
    
    # API models
    "ProcessInputRequest", "ProcessInputResponse", 
    "TaskListResponse", "EventListResponse"
]
