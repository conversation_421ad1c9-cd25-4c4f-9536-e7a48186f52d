services:
  # Frontend - React + Vite development server
  frontend:
    build:
      context: .
      dockerfile: docker/frontend.Dockerfile
      target: development
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    environment:
      - VITE_API_BASE_URL=http://localhost:8000
      - VITE_WS_URL=ws://localhost:8000/ws
      - VITE_PROD=false
      - VITE_DEV=true
    depends_on:
      backend:
        condition: service_healthy
    networks:
      - app-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000 || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.frontend.rule=Host(`localhost`)"

  # Backend - FastAPI + Mirascope AI orchestration
  backend:
    build:
      context: .
      dockerfile: docker/backend.Dockerfile
      target: development
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app
      - dashboard_data:/app/data
    environment:
      - ENVIRONMENT=development
      - DATABASE_URL=sqlite:///app/data/dashboard.db
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
      - LANGSEARCH_API_KEY=${LANGSEARCH_API_KEY}
      - OLLAMA_BASE_URL=http://ollama:11434
      - CORS_ORIGINS=http://localhost:3000
      - LOG_LEVEL=DEBUG
      - ENABLE_WAL_MODE=true
      - HEALTH_CHECK_ENABLED=true
      - PROMETHEUS_METRICS_ENABLED=true
      - DEBUG=true
      - SECRET_KEY=dev_secret_key_change_in_production
    depends_on:
      - ollama
      - db-migrate
    networks:
      - app-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Ollama - Local LLM and embeddings service
  ollama:
    image: ollama/ollama:latest
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    environment:
      - OLLAMA_KEEP_ALIVE=24h
      - OLLAMA_HOST=0.0.0.0
      - OLLAMA_MODELS_PATH=/root/.ollama/models
    networks:
      - app-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "ollama list || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Database initialization and migration service
  db-migrate:
    build:
      context: .
      dockerfile: docker/backend.Dockerfile
      target: development
    volumes:
      - ./backend:/app
      - dashboard_data:/app/data
    environment:
      - DATABASE_URL=sqlite:///app/data/dashboard.db
      - ENABLE_WAL_MODE=true
    networks:
      - app-network
    command: ["python", "-m", "app.database.migrations"]
    restart: "no"
    depends_on:
      ollama:
        condition: service_healthy

volumes:
  ollama_data:
    driver: local
  dashboard_data:
    driver: local

networks:
  app-network:
    driver: bridge
