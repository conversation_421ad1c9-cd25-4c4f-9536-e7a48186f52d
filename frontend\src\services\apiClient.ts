/**
 * API Client with integrated error handling
 * 
 * Provides a standardized way to make API calls with:
 * - Automatic error handling and retry logic
 * - Request/response interceptors
 * - Authentication handling
 * - Loading state management
 */

import { ApiErrorHandler } from '../utils/errorHandling';

export interface ApiClientConfig {
  baseUrl: string;
  timeout: number;
  retryAttempts: number;
  authToken?: string;
}

export interface RequestConfig {
  showToast?: boolean;
  retryable?: boolean;
  fallbackValue?: any;
  onError?: (error: any) => void;
  timeout?: number;
}

export class ApiClient {
  private config: ApiClientConfig;
  private abortControllers: Map<string, AbortController> = new Map();

  constructor(config: Partial<ApiClientConfig> = {}) {
    this.config = {
      baseUrl: process.env.REACT_APP_API_URL || 'http://localhost:8000',
      timeout: 30000,
      retryAttempts: 3,
      ...config,
    };
  }

  setAuthToken(token: string) {
    this.config.authToken = token;
  }

  clearAuthToken() {
    this.config.authToken = undefined;
  }

  async request<T>(
    endpoint: string,
    options: RequestInit & RequestConfig = {}
  ): Promise<T | undefined> {
    const {
      showToast = true,
      retryable = true,
      fallbackValue,
      onError,
      timeout = this.config.timeout,
      ...fetchOptions
    } = options;

    const requestId = `${endpoint}-${Date.now()}`;
    const abortController = new AbortController();
    this.abortControllers.set(requestId, abortController);

    const timeoutId = setTimeout(() => {
      abortController.abort();
    }, timeout);

    try {
      return await ApiErrorHandler.handleApiCall(
        async () => {
          const url = endpoint.startsWith('http') 
            ? endpoint 
            : `${this.config.baseUrl.replace(/\/$/, '')}/${endpoint.replace(/^\//, '')}`;

          const headers = new Headers(fetchOptions.headers);
          
          if (!headers.has('Content-Type')) {
            headers.set('Content-Type', 'application/json');
          }

          if (this.config.authToken) {
            headers.set('Authorization', `Bearer ${this.config.authToken}`);
          }

          const response = await fetch(url, {
            ...fetchOptions,
            headers,
            signal: abortController.signal,
          });

          if (!response.ok) {
            const errorData = await this.parseErrorResponse(response);
            throw errorData;
          }

          const contentType = response.headers.get('content-type');
          if (contentType && contentType.includes('application/json')) {
            return await response.json();
          }

          return await response.text();
        },
        {
          showToast,
          retryable,
          onError,
          fallbackValue,
        }
      );
    } finally {
      clearTimeout(timeoutId);
      this.abortControllers.delete(requestId);
    }
  }

  private async parseErrorResponse(response: Response): Promise<any> {
    try {
      const errorData = await response.json();
      
      // If the response follows our API error format, return it
      if (errorData.error) {
        return errorData;
      }

      // Otherwise, create a standardized error
      return {
        error: true,
        message: errorData.message || `HTTP ${response.status}: ${response.statusText}`,
        category: 'system',
        severity: response.status >= 500 ? 'critical' : 'medium',
        recoverable: response.status >= 500 || response.status === 429,
        request_id: response.headers.get('x-request-id') || '',
        timestamp: new Date().toISOString(),
      };
    } catch (e) {
      // If we can't parse the error response, create a generic error
      return {
        error: true,
        message: `HTTP ${response.status}: ${response.statusText}`,
        category: 'system',
        severity: response.status >= 500 ? 'critical' : 'medium',
        recoverable: response.status >= 500 || response.status === 429,
        request_id: response.headers.get('x-request-id') || '',
        timestamp: new Date().toISOString(),
      };
    }
  }

  // Convenience methods for common HTTP verbs
  async get<T>(endpoint: string, config?: RequestConfig): Promise<T | undefined> {
    return this.request<T>(endpoint, { ...config, method: 'GET' });
  }

  async post<T>(
    endpoint: string, 
    data?: any, 
    config?: RequestConfig
  ): Promise<T | undefined> {
    return this.request<T>(endpoint, {
      ...config,
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async put<T>(
    endpoint: string, 
    data?: any, 
    config?: RequestConfig
  ): Promise<T | undefined> {
    return this.request<T>(endpoint, {
      ...config,
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async patch<T>(
    endpoint: string, 
    data?: any, 
    config?: RequestConfig
  ): Promise<T | undefined> {
    return this.request<T>(endpoint, {
      ...config,
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async delete<T>(endpoint: string, config?: RequestConfig): Promise<T | undefined> {
    return this.request<T>(endpoint, { ...config, method: 'DELETE' });
  }

  // Cancel all pending requests
  cancelAllRequests() {
    this.abortControllers.forEach((controller) => {
      controller.abort();
    });
    this.abortControllers.clear();
  }

  // Cancel specific request
  cancelRequest(requestId: string) {
    const controller = this.abortControllers.get(requestId);
    if (controller) {
      controller.abort();
      this.abortControllers.delete(requestId);
    }
  }

  // Health check endpoint
  async healthCheck(): Promise<boolean> {
    try {
      const result = await this.get<{ status: string }>('/health', {
        showToast: false,
        retryable: false,
        timeout: 5000,
      });
      
      return result?.status === 'healthy';
    } catch (error) {
      return false;
    }
  }
}

// Create a default API client instance
export const apiClient = new ApiClient();
