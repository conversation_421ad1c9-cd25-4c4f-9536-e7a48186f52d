import { useState, useEffect, useCallback, useRef } from 'react'
import { storageService } from '../services/storageService'

/**
 * usePersistence Hook
 * PATTERN: Dual persistence strategy
 * Features:
 * - Local storage for UI state (input text, settings)
 * - SQLite integration for permanent data
 * - Automatic sync between local and remote state
 * - Cleanup and memory management
 */

interface PersistenceOptions {
  syncToRemote?: boolean
  debounceMs?: number
  maxRetries?: number
  onSyncError?: (error: Error) => void
}

interface PersistenceState<T> {
  data: T
  isLoading: boolean
  isSyncing: boolean
  lastSynced: Date | null
  error: string | null
}

export function usePersistence<T>(
  key: string,
  initialValue: T,
  options: PersistenceOptions = {}
): [
  PersistenceState<T>,
  (value: T | ((prev: T) => T)) => void,
  {
    refresh: () => Promise<void>
    forcSync: () => Promise<void>
    clear: () => void
    getStats: () => any
  }
] {
  const {
    syncToRemote = true,
    debounceMs = 500,
    maxRetries = 3,
    onSyncError
  } = options

  const [state, setState] = useState<PersistenceState<T>>({
    data: initialValue,
    isLoading: true,
    isSyncing: false,
    lastSynced: null,
    error: null
  })

  const retryCountRef = useRef(0)
  const debounceTimerRef = useRef<NodeJS.Timeout>()
  const mountedRef = useRef(true)

  // Load initial data from storage
  useEffect(() => {
    const loadData = async () => {
      try {
        setState(prev => ({ ...prev, isLoading: true, error: null }))

        // Try to load from local storage first
        const localData = storageService.get<T>(key)
        
        if (localData !== null) {
          setState(prev => ({
            ...prev,
            data: localData,
            isLoading: false
          }))
        } else {
          // Use initial value if no stored data
          setState(prev => ({
            ...prev,
            data: initialValue,
            isLoading: false
          }))
        }

        // TODO: Load from remote storage if available
        // This would be implemented when backend API is ready
        
      } catch (error) {
        console.error('[usePersistence] Failed to load data:', error)
        setState(prev => ({
          ...prev,
          data: initialValue,
          isLoading: false,
          error: error instanceof Error ? error.message : 'Failed to load data'
        }))
      }
    }

    loadData()
  }, [key, initialValue])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      mountedRef.current = false
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current)
      }
    }
  }, [])

  // Debounced save function
  const debouncedSave = useCallback((value: T) => {
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current)
    }

    debounceTimerRef.current = setTimeout(async () => {
      if (!mountedRef.current) return

      try {
        setState(prev => ({ ...prev, isSyncing: true, error: null }))

        // Save to local storage immediately
        storageService.set(key, value, syncToRemote)

        // TODO: Implement remote sync when backend is ready
        if (syncToRemote) {
          // Simulate remote sync
          await new Promise(resolve => setTimeout(resolve, 100))
        }

        if (mountedRef.current) {
          setState(prev => ({
            ...prev,
            isSyncing: false,
            lastSynced: new Date()
          }))
          retryCountRef.current = 0
        }
      } catch (error) {
        console.error('[usePersistence] Failed to save data:', error)
        
        if (mountedRef.current) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to save data'
          setState(prev => ({
            ...prev,
            isSyncing: false,
            error: errorMessage
          }))

          // Retry logic
          if (retryCountRef.current < maxRetries) {
            retryCountRef.current++
            console.log(`[usePersistence] Retrying save (${retryCountRef.current}/${maxRetries})`)
            debouncedSave(value)
          } else {
            if (onSyncError) {
              onSyncError(error instanceof Error ? error : new Error(errorMessage))
            }
          }
        }
      }
    }, debounceMs)
  }, [key, syncToRemote, debounceMs, maxRetries, onSyncError])

  // Update function
  const updateData = useCallback((value: T | ((prev: T) => T)) => {
    setState(prev => {
      const newData = typeof value === 'function' ? (value as (prev: T) => T)(prev.data) : value
      
      // Save the new data
      debouncedSave(newData)
      
      return {
        ...prev,
        data: newData
      }
    })
  }, [debouncedSave])

  // Refresh data from storage
  const refresh = useCallback(async () => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }))

      const freshData = storageService.get<T>(key)
      
      setState(prev => ({
        ...prev,
        data: freshData !== null ? freshData : initialValue,
        isLoading: false
      }))
    } catch (error) {
      console.error('[usePersistence] Failed to refresh data:', error)
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to refresh data'
      }))
    }
  }, [key, initialValue])

  // Force sync to remote
  const forcSync = useCallback(async () => {
    try {
      setState(prev => ({ ...prev, isSyncing: true, error: null }))

      // Force sync through storage service
      await storageService.forcSync()

      setState(prev => ({
        ...prev,
        isSyncing: false,
        lastSynced: new Date()
      }))
    } catch (error) {
      console.error('[usePersistence] Failed to force sync:', error)
      setState(prev => ({
        ...prev,
        isSyncing: false,
        error: error instanceof Error ? error.message : 'Failed to sync data'
      }))
    }
  }, [])

  // Clear data
  const clear = useCallback(() => {
    storageService.remove(key, syncToRemote)
    setState(prev => ({
      ...prev,
      data: initialValue,
      lastSynced: null,
      error: null
    }))
  }, [key, initialValue, syncToRemote])

  // Get storage statistics
  const getStats = useCallback(() => {
    return storageService.getStorageStats()
  }, [])

  return [
    state,
    updateData,
    {
      refresh,
      forcSync,
      clear,
      getStats
    }
  ]
}

/**
 * Hook for persisting form data with auto-recovery
 */
export function useFormPersistence<T extends Record<string, any>>(
  formId: string,
  initialData: T,
  options: { autoSave?: boolean; saveIntervalMs?: number } = {}
) {
  const { autoSave = true, saveIntervalMs = 1000 } = options
  
  const [formData, setFormData] = useState<T>(initialData)
  const [isRecovered, setIsRecovered] = useState(false)
  const saveTimerRef = useRef<NodeJS.Timeout>()

  // Load saved form data on mount
  useEffect(() => {
    const savedData = storageService.getFormData(formId)
    if (savedData && savedData.data) {
      setFormData({ ...initialData, ...savedData.data })
      setIsRecovered(true)
    }
  }, [formId, initialData])

  // Auto-save form data
  useEffect(() => {
    if (!autoSave) return

    if (saveTimerRef.current) {
      clearTimeout(saveTimerRef.current)
    }

    saveTimerRef.current = setTimeout(() => {
      storageService.setFormData(formId, formData)
    }, saveIntervalMs)

    return () => {
      if (saveTimerRef.current) {
        clearTimeout(saveTimerRef.current)
      }
    }
  }, [formData, formId, autoSave, saveIntervalMs])

  const updateFormData = useCallback((updates: Partial<T> | ((prev: T) => T)) => {
    setFormData(prev => {
      return typeof updates === 'function' ? updates(prev) : { ...prev, ...updates }
    })
  }, [])

  const clearFormData = useCallback(() => {
    storageService.remove(`forms.${formId}`, false)
    setFormData(initialData)
    setIsRecovered(false)
  }, [formId, initialData])

  const saveFormData = useCallback(() => {
    storageService.setFormData(formId, formData)
  }, [formId, formData])

  return {
    formData,
    updateFormData,
    clearFormData,
    saveFormData,
    isRecovered
  }
}

/**
 * Hook for persisting user preferences
 */
export function usePreferences<T extends Record<string, any>>(
  defaultPreferences: T
) {
  const [preferences, updatePreferences] = usePersistence(
    'user-preferences',
    defaultPreferences,
    { syncToRemote: true, debounceMs: 1000 }
  )

  const setPreference = useCallback(<K extends keyof T>(key: K, value: T[K]) => {
    updatePreferences(prev => ({
      ...prev,
      [key]: value
    }))
  }, [updatePreferences])

  const resetPreferences = useCallback(() => {
    updatePreferences(defaultPreferences)
  }, [updatePreferences, defaultPreferences])

  return {
    preferences: preferences.data,
    setPreference,
    resetPreferences,
    isLoading: preferences.isLoading,
    isSyncing: preferences.isSyncing,
    error: preferences.error
  }
}

/**
 * Hook for persisting application state
 */
export function useAppState<T>(key: string, initialState: T) {
  const [state, setState] = useState<T>(initialState)

  // Load state on mount
  useEffect(() => {
    const savedState = storageService.getState<T>(key)
    if (savedState !== null) {
      setState(savedState)
    }
  }, [key])

  // Save state changes
  const updateState = useCallback((newState: T | ((prev: T) => T)) => {
    setState(prev => {
      const updatedState = typeof newState === 'function' ? (newState as (prev: T) => T)(prev) : newState
      storageService.setState(key, updatedState)
      return updatedState
    })
  }, [key])

  const clearState = useCallback(() => {
    storageService.setState(key, initialState)
    setState(initialState)
  }, [key, initialState])

  return [state, updateState, clearState] as const
}
