"""
Embedding Service

Service layer wrapper around the EmbeddingTool to provide high-level
embedding functionality for the FastAPI application.
"""

import logging
from typing import Dict, Any, List, Optional

from ..tools.embedding_tool import (
    EmbeddingTool,
    EmbeddingInput,
    SimilaritySearchInput,
    ModelManagementInput,
    EmbeddingModel
)

logger = logging.getLogger(__name__)

class EmbeddingService:
    """
    Service layer for embedding operations.
    Provides high-level interface for the FastAPI application.
    """
    
    def __init__(self):
        """Initialize the embedding service."""
        self.tool = EmbeddingTool()
        self.initialized = False
        
    async def initialize(self):
        """Initialize the embedding service."""
        try:
            # Test that the tool is working
            stats = await self.tool.get_embedding_stats()
            if stats.get('success'):
                self.initialized = True
                logger.info("EmbeddingService initialized successfully")
            else:
                logger.error(f"EmbeddingService initialization failed: {stats.get('error')}")
                
        except Exception as e:
            logger.error(f"EmbeddingService initialization error: {e}")
            
    async def cleanup(self):
        """Cleanup the embedding service."""
        try:
            if self.initialized:
                await self.tool.clear_cache()
                logger.info("EmbeddingService cleanup completed")
        except Exception as e:
            logger.error(f"EmbeddingService cleanup error: {e}")
    
    async def generate_embeddings(
        self,
        text: str | List[str],
        model: str = "nomic-embed-text"
    ) -> Dict[str, Any]:
        """Generate embeddings for text(s)."""
        if not self.initialized:
            return {"success": False, "error": "Service not initialized"}
            
        try:
            # Convert model string to enum if needed
            embedding_model = EmbeddingModel.NOMIC_EMBED_TEXT
            for model_enum in EmbeddingModel:
                if model_enum.value == model:
                    embedding_model = model_enum
                    break
            
            embedding_input = EmbeddingInput(
                text=text,
                model=embedding_model,
                normalize=True,
                include_metadata=True
            )
            
            return await self.tool.process_embedding_request(embedding_input)
            
        except Exception as e:
            logger.error(f"Embedding generation failed: {e}")
            return {"success": False, "error": str(e)}
    
    async def similarity_search(
        self,
        query_text: str,
        search_texts: List[str],
        model: str = "nomic-embed-text",
        top_k: int = 5
    ) -> Dict[str, Any]:
        """Perform similarity search."""
        if not self.initialized:
            return {"success": False, "error": "Service not initialized"}
            
        try:
            # Convert model string to enum if needed
            embedding_model = EmbeddingModel.NOMIC_EMBED_TEXT
            for model_enum in EmbeddingModel:
                if model_enum.value == model:
                    embedding_model = model_enum
                    break
            
            search_input = SimilaritySearchInput(
                query_text=query_text,
                texts=search_texts,
                model=embedding_model,
                top_k=top_k,
                threshold=0.1
            )
            
            return await self.tool.process_similarity_search(search_input)
            
        except Exception as e:
            logger.error(f"Similarity search failed: {e}")
            return {"success": False, "error": str(e)}
    
    async def get_stats(self) -> Dict[str, Any]:
        """Get service statistics."""
        try:
            return await self.tool.get_embedding_stats()
        except Exception as e:
            logger.error(f"Failed to get stats: {e}")
            return {"success": False, "error": str(e)}
