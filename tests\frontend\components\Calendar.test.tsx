// Calendar Component Tests - Animation and Interaction Testing
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { Calendar } from '../../../frontend/src/components/calendar/Calendar';
import { mockCalendarEvent } from '../setup';
import type { CalendarEvent } from '../../../frontend/src/types';

// Mock framer-motion animations
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
    button: ({ children, ...props }: any) => <button {...props}>{children}</button>,
  },
  AnimatePresence: ({ children }: any) => children,
  useAnimation: () => ({
    start: jest.fn(),
    stop: jest.fn(),
    set: jest.fn(),
  }),
}));

describe('Calendar Component', () => {
  const mockEvents: CalendarEvent[] = [
    mockCalendarEvent({
      id: '1',
      title: 'Meeting',
      startDate: new Date('2024-01-15T10:00:00Z'),
      endDate: new Date('2024-01-15T11:00:00Z'),
    }),
    mockCalendarEvent({
      id: '2',
      title: 'AI Generated Event',
      startDate: new Date('2024-01-20T14:00:00Z'),
      endDate: new Date('2024-01-20T15:00:00Z'),
      aiGenerated: true,
      aiConfidence: 0.9,
    }),
  ];

  const defaultProps = {
    view: 'month' as const,
    currentDate: new Date('2024-01-15'),
    events: mockEvents,
    onDateSelect: jest.fn(),
    onEventSelect: jest.fn(),
    onEventCreate: jest.fn(),
    onEventUpdate: jest.fn(),
    onEventDelete: jest.fn(),
    onViewChange: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders calendar in month view by default', () => {
    render(<Calendar {...defaultProps} />);
    
    expect(screen.getByTestId('calendar-container')).toBeInTheDocument();
    expect(screen.getByTestId('calendar-header')).toBeInTheDocument();
    expect(screen.getByTestId('calendar-grid')).toBeInTheDocument();
  });

  it('displays current month and year in header', () => {
    render(<Calendar {...defaultProps} />);
    
    expect(screen.getByText('January 2024')).toBeInTheDocument();
  });

  it('shows all events for the month', () => {
    render(<Calendar {...defaultProps} />);
    
    expect(screen.getByText('Meeting')).toBeInTheDocument();
    expect(screen.getByText('AI Generated Event')).toBeInTheDocument();
  });

  it('highlights AI-generated events with special styling', () => {
    render(<Calendar {...defaultProps} />);
    
    const aiEvent = screen.getByText('AI Generated Event').closest('.calendar-event');
    expect(aiEvent).toHaveClass('ai-generated');
  });

  it('handles date selection', async () => {
    const user = userEvent.setup();
    render(<Calendar {...defaultProps} />);
    
    const dateCell = screen.getByTestId('calendar-cell-20');
    await user.click(dateCell);
    
    expect(defaultProps.onDateSelect).toHaveBeenCalledWith(new Date('2024-01-20'));
  });

  it('handles event selection', async () => {
    const user = userEvent.setup();
    render(<Calendar {...defaultProps} />);
    
    const eventElement = screen.getByText('Meeting');
    await user.click(eventElement);
    
    expect(defaultProps.onEventSelect).toHaveBeenCalledWith(mockEvents[0]);
  });

  it('switches between different view modes', async () => {
    const user = userEvent.setup();
    render(<Calendar {...defaultProps} />);
    
    const weekViewButton = screen.getByText('Week');
    await user.click(weekViewButton);
    
    expect(defaultProps.onViewChange).toHaveBeenCalledWith('week');
  });

  it('navigates to previous month', async () => {
    const user = userEvent.setup();
    render(<Calendar {...defaultProps} />);
    
    const prevButton = screen.getByTestId('calendar-prev');
    await user.click(prevButton);
    
    expect(screen.getByText('December 2023')).toBeInTheDocument();
  });

  it('navigates to next month', async () => {
    const user = userEvent.setup();
    render(<Calendar {...defaultProps} />);
    
    const nextButton = screen.getByTestId('calendar-next');
    await user.click(nextButton);
    
    expect(screen.getByText('February 2024')).toBeInTheDocument();
  });

  it('creates new event on double click', async () => {
    const user = userEvent.setup();
    render(<Calendar {...defaultProps} />);
    
    const dateCell = screen.getByTestId('calendar-cell-25');
    await user.dblClick(dateCell);
    
    expect(defaultProps.onEventCreate).toHaveBeenCalledWith(
      expect.objectContaining({
        startDate: new Date('2024-01-25'),
      })
    );
  });

  it('displays today with special highlighting', () => {
    const todayProps = {
      ...defaultProps,
      currentDate: new Date(),
    };
    render(<Calendar {...todayProps} />);
    
    const today = new Date().getDate();
    const todayCell = screen.getByTestId(`calendar-cell-${today}`);
    expect(todayCell).toHaveClass('calendar-today');
  });

  it('shows correct number of days in month', () => {
    render(<Calendar {...defaultProps} />);
    
    // January 2024 has 31 days
    for (let day = 1; day <= 31; day++) {
      expect(screen.getByTestId(`calendar-cell-${day}`)).toBeInTheDocument();
    }
  });

  it('handles event drag and drop for rescheduling', async () => {
    const user = userEvent.setup();
    render(<Calendar {...defaultProps} />);
    
    const eventElement = screen.getByText('Meeting');
    const targetCell = screen.getByTestId('calendar-cell-16');
    
    await user.pointer([
      { keys: '[MouseLeft>]', target: eventElement },
      { pointerName: 'mouse', target: targetCell },
      { keys: '[/MouseLeft]' },
    ]);
    
    expect(defaultProps.onEventUpdate).toHaveBeenCalledWith(
      expect.objectContaining({
        id: '1',
        startDate: new Date('2024-01-16T10:00:00Z'),
      })
    );
  });

  it('displays event tooltips on hover', async () => {
    const user = userEvent.setup();
    render(<Calendar {...defaultProps} />);
    
    const eventElement = screen.getByText('Meeting');
    await user.hover(eventElement);
    
    await waitFor(() => {
      expect(screen.getByTestId('event-tooltip')).toBeInTheDocument();
    });
  });

  it('shows loading state when events are being fetched', () => {
    const loadingProps = {
      ...defaultProps,
      loading: true,
    };
    render(<Calendar {...loadingProps} />);
    
    expect(screen.getByTestId('calendar-loading')).toBeInTheDocument();
  });

  it('displays error message when there is an error', () => {
    const errorProps = {
      ...defaultProps,
      error: 'Failed to load events',
    };
    render(<Calendar {...errorProps} />);
    
    expect(screen.getByText('Failed to load events')).toBeInTheDocument();
  });

  it('filters events by category when selected', async () => {
    const user = userEvent.setup();
    render(<Calendar {...defaultProps} />);
    
    const categoryFilter = screen.getByTestId('category-filter');
    await user.selectOptions(categoryFilter, 'work');
    
    // Should only show work events
    expect(screen.getByText('Meeting')).toBeInTheDocument();
  });

  it('supports keyboard navigation', async () => {
    const user = userEvent.setup();
    render(<Calendar {...defaultProps} />);
    
    const calendarGrid = screen.getByTestId('calendar-grid');
    calendarGrid.focus();
    
    await user.keyboard('{ArrowRight}');
    
    // Should move focus to next day
    expect(document.activeElement).toHaveAttribute('data-testid', 'calendar-cell-16');
  });

  it('maintains accessibility standards', () => {
    render(<Calendar {...defaultProps} />);
    
    const calendar = screen.getByTestId('calendar-container');
    expect(calendar).toHaveAttribute('role', 'grid');
    
    const cells = screen.getAllByRole('gridcell');
    expect(cells.length).toBeGreaterThan(0);
    
    cells.forEach(cell => {
      expect(cell).toHaveAttribute('tabindex');
    });
  });

  it('responds to window resize for responsive layout', () => {
    render(<Calendar {...defaultProps} />);
    
    // Simulate window resize
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 768,
    });
    
    fireEvent(window, new Event('resize'));
    
    const calendar = screen.getByTestId('calendar-container');
    expect(calendar).toHaveClass('calendar-responsive');
  });

  it('integrates with WebSocket for real-time updates', async () => {
    const { rerender } = render(<Calendar {...defaultProps} />);
    
    // Simulate new event from WebSocket
    const newEvent = mockCalendarEvent({
      id: '3',
      title: 'New WebSocket Event',
      startDate: new Date('2024-01-25T16:00:00Z'),
    });
    
    const updatedProps = {
      ...defaultProps,
      events: [...mockEvents, newEvent],
    };
    
    rerender(<Calendar {...updatedProps} />);
    
    await waitFor(() => {
      expect(screen.getByText('New WebSocket Event')).toBeInTheDocument();
    });
  });

  it('handles AI confidence indicators for events', () => {
    render(<Calendar {...defaultProps} />);
    
    const aiEvent = screen.getByText('AI Generated Event').closest('.calendar-event');
    const confidenceIndicator = aiEvent?.querySelector('.confidence-indicator');
    
    expect(confidenceIndicator).toBeInTheDocument();
    expect(confidenceIndicator).toHaveTextContent('90%');
  });

  it('supports event color coding by category', () => {
    const colorCodedEvents = [
      mockCalendarEvent({ id: '1', title: 'Work Event', category: 'work' }),
      mockCalendarEvent({ id: '2', title: 'Personal Event', category: 'personal' }),
      mockCalendarEvent({ id: '3', title: 'Health Event', category: 'health' }),
    ];
    
    const props = {
      ...defaultProps,
      events: colorCodedEvents,
    };
    
    render(<Calendar {...props} />);
    
    const workEvent = screen.getByText('Work Event').closest('.calendar-event');
    const personalEvent = screen.getByText('Personal Event').closest('.calendar-event');
    const healthEvent = screen.getByText('Health Event').closest('.calendar-event');
    
    expect(workEvent).toHaveClass('category-work');
    expect(personalEvent).toHaveClass('category-personal');
    expect(healthEvent).toHaveClass('category-health');
  });
});
