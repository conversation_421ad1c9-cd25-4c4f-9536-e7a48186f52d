"""
Database migrations and table definitions for AI-Powered Dashboard.

This module defines all database tables using SQLAlchemy models,
following the data structure from the PRP document.
"""

from sqlalchemy import (
    Column, Integer, String, Text, DateTime, Boolean, Float,
    ForeignKey, Index, JSON
)
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from datetime import datetime
from typing import Optional

from app.database.connection import Base


class UserInput(Base):
    """
    Table for storing all user inputs for persistence and context.
    
    This table stores every input the user makes, allowing for:
    - Context awareness across sessions
    - Learning from user patterns
    - Input history and retrieval
    """
    __tablename__ = "user_inputs"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    content = Column(Text, nullable=False, index=True)
    category = Column(String(20), nullable=True)  # task, event, ai_question
    confidence_score = Column(Float, nullable=True)
    processed_at = Column(DateTime(timezone=True), server_default=func.now())
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    tasks = relationship("Task", back_populates="source_input")
    events = relationship("Event", back_populates="source_input")
    ai_responses = relationship("AIResponse", back_populates="source_input")
    
    # Indexes for performance
    __table_args__ = (
        Index('idx_user_inputs_category', 'category'),
        Index('idx_user_inputs_created_at', 'created_at'),
        Index('idx_user_inputs_content_fts', 'content'),  # Full-text search
    )


class Task(Base):
    """
    Table for storing AI-categorized tasks.
    
    Tasks are automatically extracted and categorized by AI agents,
    with no hardcoded rules - pure AI decision making.
    """
    __tablename__ = "tasks"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    title = Column(String(500), nullable=False, index=True)
    description = Column(Text, nullable=True)
    category = Column(String(100), nullable=True)  # AI-generated category
    priority = Column(String(10), default="medium")  # low, medium, high, urgent
    status = Column(String(20), default="pending")  # pending, in_progress, completed, cancelled
    
    # Dates and timing
    due_date = Column(DateTime(timezone=True), nullable=True)
    completed_at = Column(DateTime(timezone=True), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # AI-generated metadata
    ai_confidence = Column(Float, nullable=True)
    ai_reasoning = Column(Text, nullable=True)
    ai_suggestions = Column(JSON, nullable=True)  # Store AI suggestions as JSON
    
    # Foreign keys
    source_input_id = Column(Integer, ForeignKey('user_inputs.id'), nullable=True)
    
    # Relationships
    source_input = relationship("UserInput", back_populates="tasks")
    
    # Indexes
    __table_args__ = (
        Index('idx_tasks_status', 'status'),
        Index('idx_tasks_priority', 'priority'),
        Index('idx_tasks_due_date', 'due_date'),
        Index('idx_tasks_category', 'category'),
        Index('idx_tasks_created_at', 'created_at'),
    )


class Event(Base):
    """
    Table for storing calendar events and reminders.
    
    Events are extracted from user inputs and processed by AI agents
    for date/time parsing and conflict detection.
    """
    __tablename__ = "events"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    title = Column(String(500), nullable=False, index=True)
    description = Column(Text, nullable=True)
    location = Column(String(500), nullable=True)
    
    # Event timing
    start_datetime = Column(DateTime(timezone=True), nullable=False)
    end_datetime = Column(DateTime(timezone=True), nullable=True)
    all_day = Column(Boolean, default=False)
    timezone = Column(String(50), default="UTC")
    
    # Event metadata
    event_type = Column(String(50), nullable=True)  # meeting, appointment, reminder, etc.
    reminder_minutes = Column(Integer, nullable=True)  # Minutes before event to remind
    
    # AI processing metadata
    ai_confidence = Column(Float, nullable=True)
    ai_extracted_datetime = Column(JSON, nullable=True)  # Raw AI datetime extraction
    ai_conflicts = Column(JSON, nullable=True)  # Potential scheduling conflicts
    
    # Status and timestamps
    status = Column(String(20), default="scheduled")  # scheduled, completed, cancelled
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Foreign keys
    source_input_id = Column(Integer, ForeignKey('user_inputs.id'), nullable=True)
    
    # Relationships
    source_input = relationship("UserInput", back_populates="events")
    
    # Indexes
    __table_args__ = (
        Index('idx_events_start_datetime', 'start_datetime'),
        Index('idx_events_status', 'status'),
        Index('idx_events_event_type', 'event_type'),
        Index('idx_events_created_at', 'created_at'),
    )


class AIResponse(Base):
    """
    Table for storing AI responses to questions and search queries.
    
    This table stores AI-generated answers, search results, and
    knowledge base responses for future reference and context.
    """
    __tablename__ = "ai_responses"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    question = Column(Text, nullable=False, index=True)
    answer = Column(Text, nullable=False)
    response_type = Column(String(50), nullable=False)  # search, knowledge, calculation, etc.
    
    # Source and confidence
    sources = Column(JSON, nullable=True)  # URLs, documents, etc.
    confidence_score = Column(Float, nullable=True)
    model_used = Column(String(100), nullable=True)
    
    # Processing metadata
    processing_time_ms = Column(Integer, nullable=True)
    token_usage = Column(JSON, nullable=True)
    search_queries_used = Column(JSON, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Foreign keys
    source_input_id = Column(Integer, ForeignKey('user_inputs.id'), nullable=True)
    
    # Relationships  
    source_input = relationship("UserInput", back_populates="ai_responses")
    
    # Indexes
    __table_args__ = (
        Index('idx_ai_responses_type', 'response_type'),
        Index('idx_ai_responses_created_at', 'created_at'),
        Index('idx_ai_responses_question_fts', 'question'),  # Full-text search
    )


class EmbeddingIndex(Base):
    """
    Table for storing text embeddings for semantic search.
    
    This table stores vector embeddings generated by Ollama
    for semantic search across tasks, events, and responses.
    """
    __tablename__ = "embedding_index"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    content = Column(Text, nullable=False)
    content_type = Column(String(50), nullable=False)  # task, event, response, input
    content_id = Column(Integer, nullable=True)  # ID of the related record
    
    # Embedding data (stored as JSON for simplicity)
    embedding = Column(JSON, nullable=False)  # Vector embedding as JSON array
    embedding_model = Column(String(100), nullable=False)  # Model used for embedding
    
    # Metadata
    chunk_index = Column(Integer, default=0)  # For long texts split into chunks
    token_count = Column(Integer, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Indexes
    __table_args__ = (
        Index('idx_embeddings_content_type', 'content_type'),
        Index('idx_embeddings_content_id', 'content_id'),
        Index('idx_embeddings_model', 'embedding_model'),
        Index('idx_embeddings_created_at', 'created_at'),
        # Composite index for efficient content lookup
        Index('idx_embeddings_type_id', 'content_type', 'content_id'),
    )


class ProcessingLog(Base):
    """
    Table for logging AI processing steps for debugging and transparency.
    
    This table captures every step of the AI processing workflow
    for debugging, performance analysis, and user transparency.
    """
    __tablename__ = "processing_logs"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    user_input_id = Column(Integer, ForeignKey('user_inputs.id'), nullable=False)
    
    # Processing step information
    step_name = Column(String(100), nullable=False)  # categorize, extract, search, etc.
    step_order = Column(Integer, nullable=False)
    agent_name = Column(String(100), nullable=True)  # Which agent handled this step
    tool_name = Column(String(100), nullable=True)   # Which tool was used
    
    # Step results
    status = Column(String(20), nullable=False)  # success, error, retry
    result_data = Column(JSON, nullable=True)    # Step output data
    error_message = Column(Text, nullable=True)  # Error details if failed
    
    # Performance metrics
    start_time = Column(DateTime(timezone=True), nullable=False)
    end_time = Column(DateTime(timezone=True), nullable=True)
    duration_ms = Column(Integer, nullable=True)
    tokens_used = Column(Integer, nullable=True)
    
    # Relationships
    user_input = relationship("UserInput")
    
    # Indexes
    __table_args__ = (
        Index('idx_processing_logs_input_id', 'user_input_id'),
        Index('idx_processing_logs_step_name', 'step_name'),
        Index('idx_processing_logs_status', 'status'),
        Index('idx_processing_logs_start_time', 'start_time'),
        # Composite index for workflow analysis
        Index('idx_processing_logs_input_step_order', 'user_input_id', 'step_order'),
    )


# Export all models
__all__ = [
    "UserInput",
    "Task", 
    "Event",
    "AIResponse",
    "EmbeddingIndex",
    "ProcessingLog"
]
