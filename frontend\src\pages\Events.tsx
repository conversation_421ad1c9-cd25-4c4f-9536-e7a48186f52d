import React from 'react'
import { motion } from 'framer-motion'
import { Calendar, Plus, Filter } from 'lucide-react'

/**
 * Events page component - AI-powered calendar management
 */
const Events: React.FC = () => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="p-6 min-h-screen"
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold text-text-primary mb-2">Events</h1>
          <p className="text-text-secondary">AI-powered calendar and event management</p>
        </div>
        
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          className="button-primary flex items-center gap-2"
        >
          <Plus className="w-5 h-5" />
          Add Event
        </motion.button>
      </div>

      {/* View Controls */}
      <div className="flex items-center gap-4 mb-8">
        <div className="flex items-center gap-2">
          {['Day', 'Week', 'Month'].map((view) => (
            <motion.button
              key={view}
              whileHover={{ scale: 1.02 }}
              className="px-4 py-2 rounded-xl text-text-secondary hover:text-text-primary hover:bg-background-secondary transition-colors"
            >
              {view}
            </motion.button>
          ))}
        </div>
        
        <motion.button
          whileHover={{ scale: 1.02 }}
          className="flex items-center gap-2 px-4 py-2 bg-background-secondary border border-border-primary rounded-xl text-text-secondary hover:text-text-primary transition-colors ml-auto"
        >
          <Filter className="w-4 h-4" />
          Filter
        </motion.button>
      </div>

      {/* Calendar Preview */}
      <div className="bg-background-secondary border border-border-primary rounded-2xl p-6 mb-8">
        <div className="grid grid-cols-7 gap-2 mb-4">
          {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day) => (
            <div key={day} className="text-center text-text-secondary font-medium py-2 text-sm">
              {day}
            </div>
          ))}
        </div>
        
        <div className="grid grid-cols-7 gap-2">
          {Array.from({ length: 21 }, (_, i) => {
            const day = i + 1
            return (
              <motion.div
                key={i}
                whileHover={{ scale: 1.05 }}
                className="aspect-square bg-background-primary border border-border-primary rounded-lg flex items-center justify-center text-text-secondary hover:bg-background-tertiary transition-colors cursor-pointer text-sm"
              >
                {day <= 31 ? day : ''}
              </motion.div>
            )
          })}
        </div>
      </div>

      {/* Empty State */}
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        className="text-center py-12"
      >
        <div className="w-20 h-20 bg-accent-green/10 rounded-2xl flex items-center justify-center mx-auto mb-6">
          <Calendar className="w-10 h-10 text-accent-green" />
        </div>
        
        <h2 className="text-2xl font-semibold text-text-primary mb-4">
          No Events Today
        </h2>
        
        <p className="text-text-secondary mb-6 max-w-md mx-auto">
          Ask the AI to schedule meetings, set reminders, or create calendar events from the main dashboard.
        </p>
        
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={() => window.location.href = '/dashboard'}
          className="button-primary"
        >
          Go to Dashboard
        </motion.button>
      </motion.div>
    </motion.div>
  )
}

export default Events
