import React from 'react'
import { motion } from 'framer-motion'
import { User, Bell, Palette, Shield, Database } from 'lucide-react'

/**
 * Settings page component - Application preferences and configuration
 */
const Settings: React.FC = () => {
  const settingSections = [
    {
      title: 'Profile',
      icon: User,
      color: 'blue',
      items: ['Account Information', 'Preferences', 'Privacy']
    },
    {
      title: 'Notifications',
      icon: Bell,
      color: 'purple',
      items: ['Push Notifications', 'Email Settings', 'AI Alerts']
    },
    {
      title: 'Appearance',
      icon: Palette,
      color: 'green',
      items: ['Theme', 'Layout', 'Animations']
    },
    {
      title: 'Security',
      icon: Shield,
      color: 'orange',
      items: ['Two-Factor Auth', 'API Keys', 'Data Encryption']
    },
    {
      title: 'Data Sources',
      icon: Database,
      color: 'red',
      items: ['Connected APIs', 'Search Engines', 'Cloud Storage']
    }
  ]

  const getColorClasses = (color: string) => {
    const colorMap: Record<string, { bg: string; text: string }> = {
      blue: { bg: 'bg-accent-blue/10', text: 'text-accent-blue' },
      purple: { bg: 'bg-accent-purple/10', text: 'text-accent-purple' },
      green: { bg: 'bg-accent-green/10', text: 'text-accent-green' },
      orange: { bg: 'bg-accent-orange/10', text: 'text-accent-orange' },
      red: { bg: 'bg-accent-red/10', text: 'text-accent-red' }
    }
    return colorMap[color] || colorMap.blue
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="p-6 min-h-screen"
    >
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-text-primary mb-2">Settings</h1>
        <p className="text-text-secondary">Configure your AI-powered search experience</p>
      </div>

      {/* Settings Grid */}
      <div className="grid gap-6 max-w-4xl">
        {settingSections.map((section, index) => {
          const IconComponent = section.icon
          const colors = getColorClasses(section.color)
          return (
            <motion.div
              key={section.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              whileHover={{ scale: 1.02 }}
              className="bg-background-secondary border border-border-primary rounded-2xl p-6 cursor-pointer hover:border-border-secondary transition-colors"
            >
              <div className="flex items-start gap-4">
                <div className={`w-12 h-12 ${colors.bg} rounded-xl flex items-center justify-center flex-shrink-0`}>
                  <IconComponent className={`w-6 h-6 ${colors.text}`} />
                </div>
                
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-text-primary mb-2">
                    {section.title}
                  </h3>
                  
                  <div className="space-y-2">
                    {section.items.map((item) => (
                      <motion.div
                        key={item}
                        whileHover={{ x: 4 }}
                        className="text-text-secondary hover:text-text-primary transition-colors cursor-pointer"
                      >
                        {item}
                      </motion.div>
                    ))}
                  </div>
                </div>
              </div>
            </motion.div>
          )
        })}
      </div>

      {/* Version Info */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.8 }}
        className="mt-12 text-center text-text-muted"
      >
        <p>OneSearch v1.0.0 - AI-Powered Universal Search</p>
      </motion.div>
    </motion.div>
  )
}

export default Settings
