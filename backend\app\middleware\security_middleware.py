"""
Security Middleware for API Protection

Comprehensive security middleware providing:
- Input validation and sanitization
- Rate limiting per IP and endpoint
- CORS configuration
- Security headers
- Request/response logging with sensitive data masking
- SQL injection and XSS protection
"""

import re
import time
import json
import asyncio
import logging
from collections import defaultdict, deque
from datetime import datetime, timed<PERSON>ta
from typing import Dict, Any, Optional, List, Callable
from fastapi import FastAPI, Request, Response, HTTPException
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp

from ..config.security import SecurityConfig, get_cors_config, mask_sensitive_data
from ..utils.validation import input_sanitizer


logger = logging.getLogger(__name__)


class SecurityMiddleware(BaseHTTPMiddleware):
    """Comprehensive security middleware for API protection"""
    
    def __init__(self, app: FastAPI, environment: str = "production"):
        super().__init__(app)
        self.environment = environment
        
        # Rate limiting storage
        self.rate_limit_storage: Dict[str, Dict[str, deque]] = defaultdict(lambda: defaultdict(deque))
        self.cleanup_interval = 300  # 5 minutes
        self.last_cleanup = time.time()
        
        # Security event tracking
        self.security_events: List[Dict[str, Any]] = []
        self.max_security_events = 1000
        
        logger.info(f"SecurityMiddleware initialized for {environment} environment")
    
    async def dispatch(self, request: Request, call_next) -> Response:
        """Main security processing pipeline"""
        start_time = time.time()
        client_ip = self._get_client_ip(request)
        
        # Create request context
        request.state.security_context = {
            "client_ip": client_ip,
            "start_time": start_time,
            "request_id": f"req_{int(time.time() * 1000)}",
            "endpoint": request.url.path,
            "method": request.method
        }
        
        try:
            # 1. Rate limiting check
            if not await self._check_rate_limit(request, client_ip):
                return JSONResponse(
                    status_code=429,
                    content={
                        "error": True,
                        "message": "Rate limit exceeded. Please try again later.",
                        "category": "rate_limit",
                        "retry_after": 60
                    },
                    headers={"Retry-After": "60"}
                )
            
            # 2. Input validation and sanitization
            await self._validate_request_input(request)
            
            # 3. Security headers
            response = await call_next(request)
            self._add_security_headers(response)
            
            # 4. Response validation
            await self._validate_response(response, request)
            
            # 5. Log request (with sensitive data masking)
            self._log_request(request, response, time.time() - start_time)
            
            # 6. Cleanup old rate limit data periodically
            if time.time() - self.last_cleanup > self.cleanup_interval:
                self._cleanup_rate_limits()
                self.last_cleanup = time.time()
            
            return response
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Security middleware error: {str(e)}", exc_info=True)
            
            # Log security event
            self._log_security_event("middleware_error", {
                "error": str(e),
                "client_ip": client_ip,
                "endpoint": request.url.path
            })
            
            return JSONResponse(
                status_code=500,
                content={
                    "error": True,
                    "message": "Internal security error",
                    "category": "system"
                }
            )
    
    def _get_client_ip(self, request: Request) -> str:
        """Extract client IP address with proxy support"""
        # Check for forwarded IP headers
        forwarded_headers = [
            "X-Forwarded-For",
            "X-Real-IP", 
            "CF-Connecting-IP",  # Cloudflare
            "X-Forwarded"
        ]
        
        for header in forwarded_headers:
            if header in request.headers:
                # Take first IP if multiple
                ip = request.headers[header].split(",")[0].strip()
                if ip:
                    return ip
        
        # Fallback to direct connection
        return request.client.host if request.client else "unknown"
    
    async def _check_rate_limit(self, request: Request, client_ip: str) -> bool:
        """Check rate limits for client IP and endpoint"""
        endpoint_path = request.url.path
        method = request.method
        
        # Determine endpoint type for rate limiting
        endpoint_type = self._get_endpoint_type(endpoint_path, method)
        
        # Get rate limit for this endpoint type
        rate_limit = SecurityConfig.RATE_LIMITS.get(endpoint_type, SecurityConfig.RATE_LIMITS["api_general"])
        
        # Check rate limit
        now = time.time()
        window_start = now - 60  # 1 minute window
        
        # Clean old requests
        client_requests = self.rate_limit_storage[client_ip][endpoint_type]
        while client_requests and client_requests[0] < window_start:
            client_requests.popleft()
        
        # Check if limit exceeded
        if len(client_requests) >= rate_limit:
            self._log_security_event("rate_limit_exceeded", {
                "client_ip": client_ip,
                "endpoint": endpoint_path,
                "rate_limit": rate_limit,
                "current_count": len(client_requests)
            })
            return False
        
        # Add current request
        client_requests.append(now)
        return True
    
    def _get_endpoint_type(self, path: str, method: str) -> str:
        """Determine endpoint type for rate limiting"""
        if "/api/process-input" in path:
            return "process_input"
        elif "/api/search" in path:
            return "search_queries" 
        elif "/api/tasks" in path:
            return "task_operations"
        elif "/api/events" in path:
            return "event_operations"
        elif method in ["POST", "PUT", "DELETE"]:
            return "api_intensive"
        else:
            return "api_general"
    
    async def _validate_request_input(self, request: Request):
        """Validate and sanitize request input"""
        try:
            # Skip validation for certain endpoints
            skip_paths = ["/health", "/docs", "/openapi.json", "/favicon.ico"]
            if any(path in request.url.path for path in skip_paths):
                return
            
            # Validate query parameters
            for key, value in request.query_params.items():
                result = input_sanitizer.sanitize_text(value, max_length=500)
                if not result.is_valid:
                    self._log_security_event("invalid_query_param", {
                        "client_ip": self._get_client_ip(request),
                        "param": key,
                        "errors": result.errors,
                        "security_flags": result.security_flags
                    })
                    
                    if result.security_flags:
                        raise HTTPException(
                            status_code=400,
                            detail=f"Invalid query parameter: {key}"
                        )
            
            # Validate JSON body for POST/PUT requests
            if request.method in ["POST", "PUT"] and "application/json" in request.headers.get("content-type", ""):
                try:
                    body = await request.body()
                    if body:
                        # Check JSON size
                        if len(body) > SecurityConfig.INPUT_VALIDATION["max_file_size_mb"] * 1024 * 1024:
                            raise HTTPException(
                                status_code=413,
                                detail="Request body too large"
                            )
                        
                        # Validate JSON format
                        json_str = body.decode('utf-8')
                        result = input_sanitizer.validate_json(json_str, max_size=len(body))
                        
                        if not result.is_valid:
                            self._log_security_event("invalid_json_body", {
                                "client_ip": self._get_client_ip(request),
                                "errors": result.errors
                            })
                            raise HTTPException(
                                status_code=400,
                                detail="Invalid JSON format"
                            )
                
                except UnicodeDecodeError:
                    raise HTTPException(
                        status_code=400,
                        detail="Invalid character encoding"
                    )
        
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Input validation error: {str(e)}")
            raise HTTPException(status_code=400, detail="Invalid request format")
    
    def _add_security_headers(self, response: Response):
        """Add security headers to response"""
        for header, value in SecurityConfig.SECURITY_HEADERS.items():
            response.headers[header] = value
        
        # Add Content Security Policy
        if self.environment == "production":
            csp_value = "; ".join([f"{k} {v}" for k, v in SecurityConfig.CSP_DIRECTIVES.items()])
            response.headers["Content-Security-Policy"] = csp_value
    
    async def _validate_response(self, response: Response, request: Request):
        """Validate response for sensitive data leaks"""
        # Skip validation for non-JSON responses
        if response.headers.get("content-type", "") != "application/json":
            return
        
        try:
            # Check for sensitive data in response
            if hasattr(response, 'body') and response.body:
                response_text = response.body.decode('utf-8')
                
                # Mask any sensitive data that might have leaked
                for pattern in SecurityConfig.SENSITIVE_PATTERNS:
                    if re.search(pattern, response_text, re.IGNORECASE):
                        self._log_security_event("sensitive_data_in_response", {
                            "client_ip": self._get_client_ip(request),
                            "endpoint": request.url.path,
                            "pattern_matched": pattern
                        })
        
        except Exception as e:
            logger.warning(f"Response validation error: {str(e)}")
    
    def _log_request(self, request: Request, response: Response, duration: float):
        """Log request with sensitive data masking"""
        client_ip = self._get_client_ip(request)
        
        # Prepare log data
        log_data = {
            "client_ip": client_ip,
            "method": request.method,
            "path": request.url.path,
            "status_code": response.status_code,
            "duration_ms": round(duration * 1000, 2),
            "user_agent": request.headers.get("user-agent", ""),
            "content_length": response.headers.get("content-length", "0"),
        }
        
        # Mask sensitive headers
        headers = dict(request.headers)
        for key, value in headers.items():
            if key.lower() in ["authorization", "cookie", "x-api-key"]:
                headers[key] = "[REDACTED]"
        
        log_data["headers"] = headers
        
        # Log at appropriate level
        if response.status_code >= 500:
            logger.error(f"API Request: {json.dumps(log_data)}")
        elif response.status_code >= 400:
            logger.warning(f"API Request: {json.dumps(log_data)}")
        else:
            logger.info(f"API Request: {json.dumps(log_data)}")
    
    def _log_security_event(self, event_type: str, details: Dict[str, Any]):
        """Log security event"""
        event = {
            "timestamp": datetime.utcnow().isoformat(),
            "event_type": event_type,
            "details": details
        }
        
        # Add to event storage
        self.security_events.append(event)
        
        # Limit storage size
        if len(self.security_events) > self.max_security_events:
            self.security_events = self.security_events[-self.max_security_events//2:]
        
        # Log high-priority events
        if event_type in ["rate_limit_exceeded", "sensitive_data_in_response", "invalid_json_body"]:
            logger.warning(f"Security Event: {json.dumps(event)}")
        else:
            logger.info(f"Security Event: {json.dumps(event)}")
    
    def _cleanup_rate_limits(self):
        """Clean up old rate limit data"""
        now = time.time()
        cutoff = now - 300  # 5 minutes
        
        for client_ip in list(self.rate_limit_storage.keys()):
            for endpoint_type in list(self.rate_limit_storage[client_ip].keys()):
                requests = self.rate_limit_storage[client_ip][endpoint_type]
                while requests and requests[0] < cutoff:
                    requests.popleft()
                
                # Remove empty queues
                if not requests:
                    del self.rate_limit_storage[client_ip][endpoint_type]
            
            # Remove empty client entries
            if not self.rate_limit_storage[client_ip]:
                del self.rate_limit_storage[client_ip]


def setup_security_middleware(app: FastAPI, environment: str = "production"):
    """Setup all security middleware for the FastAPI app"""
    
    # 1. CORS middleware
    cors_config = get_cors_config(environment)
    app.add_middleware(
        CORSMiddleware,
        allow_origins=cors_config["allowed_origins"],
        allow_credentials=cors_config["allow_credentials"],
        allow_methods=cors_config["allowed_methods"],
        allow_headers=cors_config["allowed_headers"],
    )
    
    # 2. Security middleware
    app.add_middleware(SecurityMiddleware, environment=environment)
    
    logger.info(f"Security middleware configured for {environment} environment")


# Security utilities for manual checking
def check_sql_injection(text: str) -> bool:
    """Check if text contains SQL injection patterns"""
    return any(re.search(pattern, text, re.IGNORECASE) for pattern in SecurityConfig.SQL_INJECTION_PATTERNS)


def check_xss_content(text: str) -> bool:
    """Check if text contains XSS patterns"""
    return any(re.search(pattern, text, re.IGNORECASE) for pattern in SecurityConfig.XSS_PATTERNS)
