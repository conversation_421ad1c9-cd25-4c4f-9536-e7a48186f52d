"""
FastAPI Error Handling Middleware

Integrates comprehensive error handling with FastAPI application:
- Global exception handling
- Error response formatting  
- Logging and monitoring
- Recovery strategies
- User-friendly error messages
"""

import time
import uuid
import logging
from fastapi import FastAPI, Request, Response, HTTPException
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.exceptions import HTTPException as StarletteHTTPException
from typing import Dict, Any, Optional

from ..utils.error_handling import (
    DashboardError, ErrorHandler, ErrorContext, ErrorCategory, 
    ValidationError, NetworkError, AuthenticationError,
    degradation_manager, get_error_summary
)

logger = logging.getLogger(__name__)


class ErrorHandlingMiddleware(BaseHTTPMiddleware):
    """Middleware for handling errors across all requests"""
    
    def __init__(self, app: FastAPI, error_handler: <PERSON>rrorHandler):
        super().__init__(app)
        self.error_handler = error_handler
        self.logger = logging.getLogger(__name__)
        
    async def dispatch(self, request: Request, call_next) -> Response:
        """Process request with error handling"""
        request_id = str(uuid.uuid4())
        request.state.request_id = request_id
        
        start_time = time.time()
        
        # Create error context
        context = ErrorContext(
            request_id=request_id,
            endpoint=str(request.url.path),
            additional_info={
                "method": request.method,
                "user_agent": request.headers.get("user-agent"),
                "ip_address": request.client.host if request.client else None
            }
        )
        
        try:
            # Process request
            response = await call_next(request)
            
            # Record success for degradation management
            degradation_manager.record_success()
            
            # Add request ID to response headers
            response.headers["X-Request-ID"] = request_id
            
            return response
            
        except Exception as e:
            # Handle the error
            processing_time = time.time() - start_time
            
            # Convert to DashboardError if needed
            if isinstance(e, DashboardError):
                dashboard_error = e
            elif isinstance(e, RequestValidationError):
                dashboard_error = ValidationError(
                    message="Request validation failed",
                    context=context,
                    user_message="Please check your input and try again",
                    original_exception=e
                )
            elif isinstance(e, (HTTPException, StarletteHTTPException)):
                dashboard_error = DashboardError(
                    message=str(e.detail),
                    category=ErrorCategory.VALIDATION,
                    context=context,
                    user_message=str(e.detail),
                    original_exception=e
                )
            else:
                dashboard_error = DashboardError(
                    message=str(e),
                    category=ErrorCategory.SYSTEM,
                    context=context,
                    user_message="An unexpected error occurred",
                    original_exception=e
                )
            
            # Handle the error
            handled_error = await self.error_handler.handle_error(dashboard_error)
            
            # Create error response
            return await self._create_error_response(handled_error, request_id)
    
    async def _create_error_response(
        self, 
        error: DashboardError, 
        request_id: str
    ) -> JSONResponse:
        """Create standardized error response"""
        
        # Get service status if system is degraded
        service_status = None
        if degradation_manager.is_degraded():
            service_status = {
                "message": "Service is running in degraded mode",
                "degraded": True,
                "features_disabled": degradation_manager.get_disabled_features()
            }
        
        # Build response data
        response_data = {
            "error": True,
            "message": error.user_message or error.message,
            "category": error.category.value,
            "severity": error.severity.value,
            "recoverable": error.recoverable,
            "request_id": request_id,
            "timestamp": error.context.timestamp.isoformat() if error.context else None,
        }
        
        # Add retry information if applicable
        if error.retry_after:
            response_data["retry_after"] = error.retry_after
        
        # Add service status if degraded
        if service_status:
            response_data["service_status"] = service_status
        
        # Add debug information in development
        if hasattr(error, 'context') and error.context and error.context.additional_info.get('debug'):
            response_data["debug"] = {
                "technical_message": error.message,
                "error_type": type(error).__name__,
                "context": error.context.additional_info
            }
        
        # Determine HTTP status code
        if error.category == ErrorCategory.AUTHENTICATION:
            status_code = 401
        elif error.category == ErrorCategory.VALIDATION:
            status_code = 400
        elif error.category == ErrorCategory.NETWORK:
            status_code = 503
        elif error.severity.value == "critical":
            status_code = 500
        else:
            status_code = 500
        
        # Add retry headers if applicable
        headers = {"X-Request-ID": request_id}
        if error.retry_after:
            headers["Retry-After"] = str(error.retry_after)
        
        return JSONResponse(
            status_code=status_code,
            content=response_data,
            headers=headers
        )


def setup_error_middleware(app: FastAPI, error_handler: ErrorHandler):
    """Setup error handling middleware"""
    app.add_middleware(ErrorHandlingMiddleware, error_handler=error_handler)
    logger.info("Error handling middleware configured")
