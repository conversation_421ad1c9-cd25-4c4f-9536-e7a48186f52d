import React from 'react'
import { motion } from 'framer-motion'
import { CheckSquare, Plus, Filter, Search } from 'lucide-react'

/**
 * Tasks page component - Intelligent task management
 */
const Tasks: React.FC = () => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="p-6 min-h-screen"
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold text-text-primary mb-2">Tasks</h1>
          <p className="text-text-secondary">AI-powered task management and organization</p>
        </div>
        
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          className="button-primary flex items-center gap-2"
        >
          <Plus className="w-5 h-5" />
          Add Task
        </motion.button>
      </div>

      {/* Filters */}
      <div className="flex items-center gap-4 mb-6">
        <motion.button
          whileHover={{ scale: 1.02 }}
          className="flex items-center gap-2 px-4 py-2 bg-background-secondary border border-border-primary rounded-xl text-text-secondary hover:text-text-primary transition-colors"
        >
          <Filter className="w-4 h-4" />
          Filter
        </motion.button>
        
        <div className="flex-1 max-w-md relative">
          <Search className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-text-muted" />
          <input
            type="text"
            placeholder="Search tasks..."
            className="input-primary pl-10 w-full"
          />
        </div>
      </div>

      {/* Empty State */}
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        className="text-center py-16"
      >
        <div className="w-20 h-20 bg-accent-blue/10 rounded-2xl flex items-center justify-center mx-auto mb-6">
          <CheckSquare className="w-10 h-10 text-accent-blue" />
        </div>
        
        <h2 className="text-2xl font-semibold text-text-primary mb-4">
          No Tasks Yet
        </h2>
        
        <p className="text-text-secondary mb-6 max-w-md mx-auto">
          Start by typing something in the main dashboard. The AI will automatically create tasks for you!
        </p>
        
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={() => window.location.href = '/dashboard'}
          className="button-primary"
        >
          Go to Dashboard
        </motion.button>
      </motion.div>
    </motion.div>
  )
}

export default Tasks
