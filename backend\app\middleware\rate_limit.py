"""
Rate Limiting Middleware - Backend Performance Optimization
PATTERN: Async rate limiting for API endpoints and AI services
Features:
- Per-IP rate limiting for API endpoints
- LLM call rate limiting to prevent overuse
- Embedding generation throttling
- Web search API rate limiting
- Background task rate limiting
"""

import time
import asyncio
import logging
from typing import Dict, Any, Optional
from collections import defaultdict, deque
from fastapi import Request, Response, HTTPException
from starlette.middleware.base import BaseHTTPMiddleware
from datetime import datetime, timedelta
import ipaddress

from app.config.settings import get_settings, PERFORMANCE_CONSTANTS

logger = logging.getLogger(__name__)
settings = get_settings()

class RateLimitMiddleware(BaseHTTPMiddleware):
    """Advanced rate limiting middleware with per-service limits."""
    
    def __init__(self, app):
        super().__init__(app)
        
        # Rate limit buckets: {client_ip: {service: deque of timestamps}}
        self.rate_buckets: Dict[str, Dict[str, deque]] = defaultdict(lambda: defaultdict(deque))
        
        # Service-specific rate limits (requests per minute)
        self.rate_limits = {
            "api_general": settings.api_rate_limit_per_minute,
            "embedding": settings.embedding_rate_limit_per_minute,
            "web_search": settings.web_search_rate_limit_per_minute,
            "llm_calls": settings.api_rate_limit_per_minute,  # Same as general for now
        }
        
        # Cleanup task for old entries
        self.last_cleanup = time.time()
        
    async def dispatch(self, request: Request, call_next):
        """Apply rate limiting based on client IP and service type."""
        client_ip = self._get_client_ip(request)
        service_type = self._determine_service_type(request)
        
        # Check rate limit
        if not await self._check_rate_limit(client_ip, service_type):
            # Rate limit exceeded
            retry_after = 60  # Wait 1 minute
            
            logger.warning(f"Rate limit exceeded for {client_ip} on {service_type}")
            
            raise HTTPException(
                status_code=429,
                detail={
                    "error": "Rate limit exceeded",
                    "service": service_type,
                    "retry_after": retry_after,
                    "limit": self.rate_limits.get(service_type, 60)
                },
                headers={"Retry-After": str(retry_after)}
            )
        
        # Record the request
        await self._record_request(client_ip, service_type)
        
        # Periodic cleanup of old entries
        await self._cleanup_if_needed()
        
        # Process the request
        response = await call_next(request)
        
        # Add rate limit headers to response
        remaining = await self._get_remaining_requests(client_ip, service_type)
        response.headers["X-RateLimit-Limit"] = str(self.rate_limits.get(service_type, 60))
        response.headers["X-RateLimit-Remaining"] = str(remaining)
        response.headers["X-RateLimit-Reset"] = str(int(time.time()) + 60)
        
        return response
    
    def _get_client_ip(self, request: Request) -> str:
        """Extract client IP from request, considering proxies."""
        # Check for forwarded headers (common in production behind proxies)
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            # Take the first IP in the chain
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        # Fallback to direct client IP
        return request.client.host if request.client else "unknown"
    
    def _determine_service_type(self, request: Request) -> str:
        """Determine the service type based on request path."""
        path = request.url.path.lower()
        
        if "/api/embedding" in path or "/api/vectorize" in path:
            return "embedding"
        elif "/api/search" in path and "web" in path:
            return "web_search"
        elif "/api/llm" in path or "/api/process" in path:
            return "llm_calls"
        else:
            return "api_general"
    
    async def _check_rate_limit(self, client_ip: str, service_type: str) -> bool:
        """Check if the client has exceeded the rate limit for the service."""
        current_time = time.time()
        window_start = current_time - 60  # 1-minute sliding window
        
        # Get the request queue for this client and service
        request_queue = self.rate_buckets[client_ip][service_type]
        
        # Remove requests older than 1 minute
        while request_queue and request_queue[0] < window_start:
            request_queue.popleft()
        
        # Check if under the limit
        limit = self.rate_limits.get(service_type, 60)
        return len(request_queue) < limit
    
    async def _record_request(self, client_ip: str, service_type: str):
        """Record a new request for rate limiting."""
        current_time = time.time()
        self.rate_buckets[client_ip][service_type].append(current_time)
    
    async def _get_remaining_requests(self, client_ip: str, service_type: str) -> int:
        """Get the number of remaining requests for the current window."""
        current_time = time.time()
        window_start = current_time - 60
        
        # Get the request queue for this client and service
        request_queue = self.rate_buckets[client_ip][service_type]
        
        # Count requests in current window
        current_requests = sum(1 for req_time in request_queue if req_time >= window_start)
        
        # Calculate remaining
        limit = self.rate_limits.get(service_type, 60)
        return max(0, limit - current_requests)
    
    async def _cleanup_if_needed(self):
        """Clean up old rate limit data periodically."""
        current_time = time.time()
        
        # Cleanup every 5 minutes
        if current_time - self.last_cleanup > 300:
            await self._cleanup_old_data()
            self.last_cleanup = current_time
    
    async def _cleanup_old_data(self):
        """Remove old rate limit data to prevent memory leaks."""
        current_time = time.time()
        cutoff_time = current_time - 3600  # Keep data for 1 hour
        
        # Clean up each client's data
        clients_to_remove = []
        
        for client_ip, services in self.rate_buckets.items():
            services_to_remove = []
            
            for service_type, request_queue in services.items():
                # Remove old requests
                while request_queue and request_queue[0] < cutoff_time:
                    request_queue.popleft()
                
                # Mark empty queues for removal
                if not request_queue:
                    services_to_remove.append(service_type)
            
            # Remove empty service queues
            for service_type in services_to_remove:
                del services[service_type]
            
            # Mark clients with no services for removal
            if not services:
                clients_to_remove.append(client_ip)
        
        # Remove empty clients
        for client_ip in clients_to_remove:
            del self.rate_buckets[client_ip]
        
        logger.info(f"Rate limit cleanup: removed {len(clients_to_remove)} inactive clients")
    
    def get_rate_limit_stats(self) -> Dict[str, Any]:
        """Get current rate limiting statistics."""
        current_time = time.time()
        window_start = current_time - 60
        
        stats = {
            "total_clients": len(self.rate_buckets),
            "service_limits": self.rate_limits,
            "active_clients_by_service": defaultdict(int),
            "requests_in_current_window": defaultdict(int)
        }
        
        for client_ip, services in self.rate_buckets.items():
            for service_type, request_queue in services.items():
                # Count active requests in current window
                active_requests = sum(1 for req_time in request_queue if req_time >= window_start)
                
                if active_requests > 0:
                    stats["active_clients_by_service"][service_type] += 1
                    stats["requests_in_current_window"][service_type] += active_requests
        
        return dict(stats)


class ServiceRateLimiter:
    """Service-specific rate limiter for internal AI services."""
    
    def __init__(self):
        self.service_buckets: Dict[str, deque] = defaultdict(deque)
        self.service_limits = {
            "llm_openrouter": 60,  # 60 calls per minute to OpenRouter
            "embedding_ollama": 30,  # 30 embedding calls per minute
            "web_search_langsearch": 20,  # 20 web searches per minute
            "database_queries": 300,  # 300 DB queries per minute
        }
    
    async def acquire(self, service_name: str, timeout: float = 5.0) -> bool:
        """Acquire permission to make a service call with timeout."""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            if await self._check_service_limit(service_name):
                await self._record_service_call(service_name)
                return True
            
            # Wait a bit before retrying
            await asyncio.sleep(0.1)
        
        logger.warning(f"Service rate limit timeout for {service_name}")
        return False
    
    async def _check_service_limit(self, service_name: str) -> bool:
        """Check if service is under its rate limit."""
        current_time = time.time()
        window_start = current_time - 60  # 1-minute window
        
        # Get the request queue for this service
        request_queue = self.service_buckets[service_name]
        
        # Remove old requests
        while request_queue and request_queue[0] < window_start:
            request_queue.popleft()
        
        # Check limit
        limit = self.service_limits.get(service_name, 60)
        return len(request_queue) < limit
    
    async def _record_service_call(self, service_name: str):
        """Record a service call for rate limiting."""
        current_time = time.time()
        self.service_buckets[service_name].append(current_time)
    
    async def get_service_usage(self, service_name: str) -> Dict[str, Any]:
        """Get current usage statistics for a service."""
        current_time = time.time()
        window_start = current_time - 60
        
        request_queue = self.service_buckets[service_name]
        current_usage = sum(1 for req_time in request_queue if req_time >= window_start)
        limit = self.service_limits.get(service_name, 60)
        
        return {
            "service": service_name,
            "current_usage": current_usage,
            "limit": limit,
            "remaining": max(0, limit - current_usage),
            "usage_percentage": round((current_usage / limit) * 100, 2)
        }
    
    def get_all_service_stats(self) -> Dict[str, Any]:
        """Get usage statistics for all services."""
        current_time = time.time()
        window_start = current_time - 60
        
        stats = {}
        
        for service_name in self.service_limits.keys():
            request_queue = self.service_buckets[service_name]
            current_usage = sum(1 for req_time in request_queue if req_time >= window_start)
            limit = self.service_limits[service_name]
            
            stats[service_name] = {
                "current_usage": current_usage,
                "limit": limit,
                "remaining": max(0, limit - current_usage),
                "usage_percentage": round((current_usage / limit) * 100, 2)
            }
        
        return stats


# Global service rate limiter instance
service_rate_limiter = ServiceRateLimiter()
