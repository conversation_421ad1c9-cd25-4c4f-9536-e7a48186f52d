"""
Specialized agent for AI question processing and search routing.

This agent handles inputs categorized as questions, determines the best approach
for answering them, and coordinates with search tools for comprehensive responses.
Follows research/mirascope/page7-agents.md for agent patterns.
"""

import asyncio
import logging
from typing import List, Dict, Any, Optional, AsyncGenerator
from datetime import datetime

from mirascope import llm
from pydantic import ValidationError

from app.models.ai_responses import AIQuestionAnalysisResponse, AISearchResultsResponse, AIProcessingError
from app.models.pydantic_models import (
    UserInput, AIResponse, SearchResult, QuestionType, ProcessingState, AnimationStep
)
from app.config.settings import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()


class SearchAgent:
    """
    Specialized agent for processing questions and coordinating search operations.
    
    This agent analyzes questions, determines the best search strategy,
    and synthesizes comprehensive answers from multiple sources.
    """
    
    def __init__(self):
        self.model = settings.primary_llm_model
        self.fallback_model = settings.ai_fallback_model
        self.max_retries = settings.ai_max_retries
        self.question_history: List[Dict[str, Any]] = []
        
        # Question classification patterns
        self.simple_knowledge_indicators = [
            "what is", "who is", "when was", "where is", "how do you",
            "definition of", "meaning of", "explain", "describe"
        ]
        
        self.web_search_indicators = [
            "latest", "recent", "current", "news", "today", "now",
            "price", "cost", "review", "compare", "best"
        ]
        
        self.database_search_indicators = [
            "my", "previous", "earlier", "before", "history",
            "what did i", "when did i", "show me my"
        ]
    
    async def process_question(
        self,
        user_input: UserInput,
        context: Optional[Dict[str, Any]] = None
    ) -> AIResponse:
        """
        Process a user question and generate a comprehensive answer.
        
        Args:
            user_input: The user's question
            context: Optional context from categorization
            
        Returns:
            AIResponse: Complete response with answer and sources
        """
        try:
            logger.info(f"Processing question: {user_input.text[:100]}...")
            
            # Analyze question to determine approach
            analysis_result = await self._analyze_question_with_retry(user_input, context)
            
            # Execute search strategy based on analysis
            search_results = await self._execute_search_strategy(analysis_result, user_input)
            
            # Synthesize final answer
            synthesized_answer = await self._synthesize_answer(
                user_input, analysis_result, search_results
            )
            
            # Create comprehensive AI response
            ai_response = AIResponse(
                original_input=user_input.text,
                category="ai_question",
                processing_steps=[
                    "Question analyzed",
                    f"Search strategy: {analysis_result.search_scope}",
                    f"Tools used: {', '.join(analysis_result.recommended_tools)}",
                    "Answer synthesized"
                ],
                result=synthesized_answer,
                search_results=search_results.results if search_results else None,
                sources=search_results.sources_used if search_results else None,
                confidence=search_results.answer_confidence if search_results else analysis_result.confidence,
                processing_time_ms=None,  # Set by calling code
                model_used=self.model
            )
            
            # Store in processing history
            self.question_history.append({
                "input": user_input.text,
                "question_type": analysis_result.question_type,
                "tools_used": analysis_result.recommended_tools,
                "confidence": analysis_result.confidence,
                "timestamp": datetime.now(),
                "answer_length": len(synthesized_answer)
            })
            
            # Keep history manageable
            if len(self.question_history) > 25:
                self.question_history.pop(0)
            
            logger.info(f"Question processed: {analysis_result.question_type} with {len(search_results.results if search_results else [])} sources")
            return ai_response
            
        except Exception as e:
            logger.error(f"Error processing question: {e}")
            
            # Return error response
            return AIResponse(
                original_input=user_input.text,
                category="ai_question",
                processing_steps=["Error occurred during processing"],
                result=f"I apologize, but I encountered an error processing your question: {str(e)}",
                confidence=0.1,
                model_used=self.model
            )
    
    async def _analyze_question_with_retry(
        self,
        user_input: UserInput,
        context: Optional[Dict[str, Any]] = None
    ) -> AIQuestionAnalysisResponse:
        """
        Analyze question with retry logic.
        
        Args:
            user_input: User input to analyze
            context: Optional context
            
        Returns:
            AIQuestionAnalysisResponse: Question analysis result
        """
        last_error = None
        
        # Try primary model
        for attempt in range(self.max_retries):
            try:
                logger.debug(f"Question analysis attempt {attempt + 1}/{self.max_retries}")
                return await self._perform_question_analysis(user_input, context, self.model)
                
            except Exception as e:
                last_error = e
                logger.warning(f"Question analysis attempt {attempt + 1} failed: {e}")
                
                if attempt < self.max_retries - 1:
                    await asyncio.sleep(0.5 * (attempt + 1))
        
        # Try fallback model
        try:
            logger.info(f"Trying fallback model for question analysis")
            return await self._perform_question_analysis(user_input, context, self.fallback_model)
            
        except Exception as e:
            logger.error(f"Fallback question analysis failed: {e}")
            raise AIProcessingError(
                error_type="question_analysis_failed",
                error_message=f"Question analysis failed: {str(last_error)}",
                error_code="QUESTION_ANALYSIS_FAILED",
                operation="question_processing",
                input_data=user_input.text,
                recoverable=True,
                suggested_action="Please try rephrasing your question",
                model_used=self.model
            )
    
    @llm.call(provider="openrouter", response_model=AIQuestionAnalysisResponse, json_mode=True)
    async def _perform_question_analysis(
        self,
        user_input: UserInput,
        context: Optional[Dict[str, Any]] = None,
        model: str = None
    ) -> str:
        """
        Mirascope call for question analysis using structured response model.
        """
        
        # Build context from question history
        context_str = ""
        if self.question_history:
            recent_questions = self.question_history[-3:]  # Last 3 questions
            context_str = "\\n".join([
                f"Recent: '{q['input'][:50]}...' → {q['question_type']} (tools: {', '.join(q['tools_used'])})"
                for q in recent_questions
            ])
            context_str = f"\\n\\nRecent question patterns:\\n{context_str}"
        
        # Add provided context
        if context:
            context_str += f"\\n\\nCategorization context: {context}"
        
        return f"""You are an expert question analysis agent. Analyze the user's question and determine the optimal search and response strategy.

QUESTION TO ANALYZE: "{user_input.text}"

CONTEXT: {context_str}

ANALYSIS FRAMEWORK:
1. **Question Type Classification**:
   - SIMPLE_KNOWLEDGE: Basic facts, definitions, explanations
   - DATABASE_SEARCH: Personal history, previous interactions, "my" data
   - WEB_SEARCH: Current events, prices, reviews, latest information

2. **Intent Understanding**:
   - What exactly is the user trying to learn?
   - What type of answer do they expect?
   - How detailed should the response be?

3. **Tool Selection Strategy**:
   - database_search_tool: For personal/historical data
   - web_search_tool: For current information, news, reviews
   - knowledge_base: For general facts and explanations
   - Multiple tools can be used for comprehensive answers

4. **Search Query Generation**:
   - Create specific, targeted search queries
   - Consider synonyms and related terms
   - Break complex questions into sub-queries

CLASSIFICATION HINTS:
- Simple knowledge: "What is photosynthesis?", "How does GPS work?"
- Database search: "What tasks did I complete yesterday?", "Show my meetings"
- Web search: "Latest iPhone reviews", "Current Bitcoin price", "News about AI"

ENTITY EXTRACTION:
- People, places, products, concepts, dates, quantities
- Technical terms, proper nouns, specific domains

COMPLEXITY ASSESSMENT:
- Simple: Single fact or definition
- Moderate: Requires explanation or comparison  
- Complex: Multi-part question or analysis needed

Respond with comprehensive JSON analysis including question type, recommended tools, search strategies, and detailed reasoning."""
    
    async def _execute_search_strategy(
        self,
        analysis: AIQuestionAnalysisResponse,
        user_input: UserInput
    ) -> Optional[AISearchResultsResponse]:
        """
        Execute the search strategy determined by question analysis.
        
        Args:
            analysis: Question analysis results
            user_input: Original user input
            
        Returns:
            AISearchResultsResponse: Search results and synthesis
        """
        try:
            search_results = []
            sources_used = []
            search_time_start = datetime.now()
            
            # Execute searches based on recommended tools
            if "web_search_tool" in analysis.recommended_tools:
                web_results = await self._perform_web_search(analysis.search_queries)
                search_results.extend(web_results)
                sources_used.extend([r.get("url", r.get("source", "Web")) for r in web_results])
            
            if "database_search_tool" in analysis.recommended_tools:
                db_results = await self._perform_database_search(analysis.search_queries)
                search_results.extend(db_results)
                sources_used.extend([r.get("source", "Database") for r in db_results])
            
            if "knowledge_base" in analysis.recommended_tools or not search_results:
                # Fallback to knowledge-based response
                knowledge_result = await self._generate_knowledge_response(user_input, analysis)
                search_results.append(knowledge_result)
                sources_used.append("AI Knowledge Base")
            
            search_time_ms = int((datetime.now() - search_time_start).total_seconds() * 1000)
            
            # Create search results response
            return AISearchResultsResponse(
                query=user_input.text,
                search_type=analysis.question_type,
                results_count=len(search_results),
                search_time_ms=search_time_ms,
                results=search_results,
                synthesized_answer="",  # Will be filled by synthesis step
                sources_used=list(set(sources_used)),  # Remove duplicates
                reliability_score=0.8,  # Default, can be improved with source analysis
                answer_confidence=analysis.confidence,
                completeness_score=min(1.0, len(search_results) / 3)  # More results = more complete
            )
            
        except Exception as e:
            logger.error(f"Error executing search strategy: {e}")
            return None
    
    async def _perform_web_search(self, queries: List[str]) -> List[Dict[str, Any]]:
        """
        Perform web search using LangSearch API.
        
        Args:
            queries: List of search queries
            
        Returns:
            List[Dict[str, Any]]: Web search results
        """
        try:
            # TODO: Implement actual LangSearch API call
            # For now, return mock results
            results = []
            for query in queries[:2]:  # Limit to 2 queries for performance
                results.append({
                    "title": f"Web result for: {query}",
                    "content": f"Mock web search result content for query '{query}'",
                    "url": f"https://example.com/search?q={query.replace(' ', '+')}",
                    "source": "Web Search",
                    "relevance_score": 0.8
                })
            
            logger.info(f"Performed web search for {len(queries)} queries, got {len(results)} results")
            return results
            
        except Exception as e:
            logger.error(f"Web search error: {e}")
            return []
    
    async def _perform_database_search(self, queries: List[str]) -> List[Dict[str, Any]]:
        """
        Perform database search using semantic similarity.
        
        Args:
            queries: List of search queries
            
        Returns:
            List[Dict[str, Any]]: Database search results
        """
        try:
            # TODO: Implement actual database search with embeddings
            # For now, return mock results
            results = []
            for query in queries[:2]:
                results.append({
                    "title": f"Database result for: {query}",
                    "content": f"Mock database search result for query '{query}'",
                    "source": "Personal Database",
                    "relevance_score": 0.7
                })
            
            logger.info(f"Performed database search for {len(queries)} queries, got {len(results)} results")
            return results
            
        except Exception as e:
            logger.error(f"Database search error: {e}")
            return []
    
    async def _generate_knowledge_response(
        self, 
        user_input: UserInput, 
        analysis: AIQuestionAnalysisResponse
    ) -> Dict[str, Any]:
        """
        Generate knowledge-based response for simple questions.
        
        Args:
            user_input: Original user input
            analysis: Question analysis
            
        Returns:
            Dict[str, Any]: Knowledge-based result
        """
        return {
            "title": "AI Knowledge Response",
            "content": f"Based on my knowledge, regarding '{user_input.text}': This appears to be a {analysis.question_type} question about {', '.join(analysis.key_concepts)}.",
            "source": "AI Knowledge Base",
            "relevance_score": analysis.confidence
        }
    
    async def _synthesize_answer(
        self,
        user_input: UserInput,
        analysis: AIQuestionAnalysisResponse,
        search_results: Optional[AISearchResultsResponse]
    ) -> str:
        """
        Synthesize a comprehensive answer from search results.
        
        Args:
            user_input: Original question
            analysis: Question analysis
            search_results: Search results to synthesize
            
        Returns:
            str: Synthesized answer
        """
        try:
            if not search_results or not search_results.results:
                return f"I understand you're asking about {analysis.intent}, but I wasn't able to find specific information to answer your question comprehensively."
            
            # Simple synthesis for now - could be enhanced with another LLM call
            result_contents = [r.get("content", "") for r in search_results.results if r.get("content")]
            
            if result_contents:
                # Update the search results with synthesized answer
                search_results.synthesized_answer = f"Based on my research, here's what I found about your question: {result_contents[0][:500]}..."
                return search_results.synthesized_answer
            else:
                return "I found some information related to your question, but I need more specific details to provide a comprehensive answer."
                
        except Exception as e:
            logger.error(f"Error synthesizing answer: {e}")
            return f"I encountered an issue while processing the search results, but I understand you're asking about {analysis.intent}."
    
    async def stream_question_processing(
        self,
        user_input: UserInput,
        context: Optional[Dict[str, Any]] = None
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Stream question processing with real-time visual feedback.
        
        Args:
            user_input: User question to process
            context: Optional context
            
        Yields:
            Dict[str, Any]: Processing updates for frontend animations
        """
        try:
            processing_steps = [
                AnimationStep(
                    id="analyzing",
                    message="Analyzing your question...",
                    animation_type="pulse",
                    duration=1.5
                ),
                AnimationStep(
                    id="strategy",
                    message="Determining search strategy...",
                    animation_type="spin",
                    duration=1.2,
                    delay=1.5
                ),
                AnimationStep(
                    id="searching",
                    message="Searching for information...",
                    animation_type="typewriter",
                    duration=2.5,
                    delay=2.7
                ),
                AnimationStep(
                    id="synthesizing",
                    message="Synthesizing answer...",
                    animation_type="fade",
                    duration=1.8,
                    delay=5.2
                ),
                AnimationStep(
                    id="finalizing",
                    message="Preparing response...",
                    animation_type="bounce",
                    duration=0.8,
                    delay=7.0
                )
            ]
            
            # Stream processing steps
            for i, step in enumerate(processing_steps):
                yield {
                    "type": "question_processing_update",
                    "data": {
                        "step": step.dict(),
                        "progress": i / len(processing_steps),
                        "message": step.message
                    }
                }
                
                if i < len(processing_steps) - 1:
                    await asyncio.sleep(step.delay + step.duration)
            
            # Perform actual question processing
            result = await self.process_question(user_input, context)
            
            # Yield final result
            yield {
                "type": "question_processing_complete",
                "data": {
                    "response": result.dict(),
                    "sources_count": len(result.sources or []),
                    "confidence": result.confidence
                }
            }
            
        except Exception as e:
            logger.error(f"Error in stream_question_processing: {e}")
            yield {
                "type": "error",
                "data": {
                    "error_message": str(e),
                    "error_type": "question_processing_error"
                }
            }
    
    def get_question_stats(self) -> Dict[str, Any]:
        """Get question processing statistics."""
        if not self.question_history:
            return {"total_processed": 0, "question_types": {}, "tools_used": {}}
        
        question_types = {}
        tools_used = {}
        
        for question in self.question_history:
            q_type = question["question_type"]
            question_types[q_type] = question_types.get(q_type, 0) + 1
            
            for tool in question["tools_used"]:
                tools_used[tool] = tools_used.get(tool, 0) + 1
        
        return {
            "total_processed": len(self.question_history),
            "question_types": question_types,
            "tools_used": tools_used,
            "avg_confidence": sum(q["confidence"] for q in self.question_history) / len(self.question_history),
            "avg_answer_length": sum(q["answer_length"] for q in self.question_history) / len(self.question_history),
            "last_processed": self.question_history[-1]["timestamp"].isoformat()
        }


# Global search agent instance
_search_agent = None


def get_search_agent() -> SearchAgent:
    """Get the global search agent instance (singleton pattern)."""
    global _search_agent
    if _search_agent is None:
        _search_agent = SearchAgent()
    return _search_agent


# Export main components
__all__ = ["SearchAgent", "get_search_agent"]
