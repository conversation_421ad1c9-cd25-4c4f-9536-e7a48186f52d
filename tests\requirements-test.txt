"""
Requirements file for integration testing
Task 40: Test dependencies and environment setup

PATTERN: Comprehensive testing dependency management
Features:
- End-to-end testing frameworks
- Performance testing tools
- Security testing libraries
- Browser automation
- Reporting and visualization
"""

# Core testing framework
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-mock==3.12.0
pytest-html==4.1.1
pytest-cov==4.1.0

# HTTP and API testing
httpx==0.25.2
requests==2.31.0
websockets==12.0

# Browser automation and UI testing
# Note: For browser testing, use the Playwright MCP server externally

# Performance testing
locust==2.17.0
memory-profiler==0.61.0
psutil==5.9.6

# Security testing
bandit==1.7.5
safety==2.3.4

# Data handling and validation
pydantic==2.5.0
jsonschema==4.20.0

# Reporting and visualization
jinja2==3.1.2
matplotlib==3.8.2
pandas==2.1.4

# Utilities
python-dotenv==1.0.0
colorama==0.4.6
rich==13.7.0
click==8.1.7

# Development and debugging
ipdb==0.13.13
python-json-logger==2.0.7
